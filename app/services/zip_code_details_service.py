import httpx, os

async def get_zip_details(postal_code):            
    async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
        response = await client.get(f"{os.getenv('SAMX_SERVICE_LAYER_URL')}/zip-mappings/{postal_code}")
        status_code = response.status_code
        zip_api_response = response.json()

        if status_code < 400 and len(zip_api_response) != 0:
            city = zip_api_response[0]["city"]
            state_code = zip_api_response[0]["stateCode"]
        else:
            city = None 
            state_code = None

        return {"city": city, "State_code": state_code}
