from config.openAI import json_model
from config.db import samx_one_db
import json
import httpx
import os
import time
import random
from utils.helpers.constants import OEC_SAMXONE_CLIENT_ID
from pydantic import ValidationError
# Import models from classes.py
from client_plugins.samx_one.samx_one.classes import Employee

prior_carriers_collection = samx_one_db['mst_priorCarriers']

async def create_structure_from_xtractor_response(content, key_value_pairs, schema):
    prompt = f"""
        System: You are an expert in encoding data from documents for creating health insurance group.
        You have been given a task to extract structured data from content and key_value_pair.
        You'll be given two pieces of data extracted from document and one schema.

        1. schema: List of fields with their descriptions and paths and few fields have rules and data_type. This is the blueprint for the data you need to extract. Each field in the schema is a dictionary with the following keys:
            fieldname: The name of the field that will be added to the JSON.
            description: A brief description of the field that will be used for extracting the data for that field.
            path: The path where the field will be added in the JSON.
            rules: Any specific rules or conditions that apply to the field.
            data_type: The data type of the field (e.g., string, number, date).

        Always follow the schema to create the JSON.

        extract the values for the specified fields from the content and key_value_pair and return them in JSON format. Here are example the fields:
        [
            {{
                "fieldname": "companyName",
                "description": "This is the company name, also termed as business name or full legal business name.",
                "path": "companyProfile.companyName"
            }},
            {{
                "fieldname": "effectiveDate",
                "description": "This is the effective date when the company profile becomes active.",
                "path": "companyProfile.effectiveDate"
            }},
            {{
                "fieldname": "zipCode",
                "description": "This is the ZIP code of the company's location.",
                "path": "companyProfile.zipCode"
            }},
            {{
                "fieldname": "state",
                "description": "This is the state where the company is located.",
                "path": "companyProfile.state"
            }}
        ]
        The output will be like this:   
        {{
            "companyProfile": {{
                "companyName": "Extracted Company Name",
                "effectiveDate": "Extracted Effective Date",
                "zipCode": "Extracted ZIP Code",
                "state": "Extracted State"
            }}
        }}

        2. content: The whole text extracted from the documents.
        3. key_value_pairs: list of dictionaries, each containing a "key" and "value" extracted from the document.

        Instructions:
        1. For each field in the 'schema', find the corresponding value in the 'key_value_pairs'.
        2. If a value is not found in the 'key_value_pairs', search for it in the 'content'.
        3. Construct a dictionary with the keys from the 'schema' and the corresponding values from the 'key_value_pairs' or 'content'.
        4. Extract as many fields as possible from the provided data and if any value is not found, keep it empty string.
        5. Strict Instruction. Do not miss any field and information obtained from content and key_value_pairs for which schema exists.
        6. Do not hallucinate. Do not change the 'schema'. Always follow the path mentioned in the 'schema'. 
        
        schema : {schema}
        content : {content}
        key_value_pairs : {key_value_pairs}

        Please provide the structured data as a JSON object.
    """
    response = await json_model.ainvoke(prompt)
    json_response = json.loads(response.content)
    return json_response

async def get_prior_carrier_list():
    """Fetch the list of prior carriers from the database."""
    try:
        prior_carriers_cursor = prior_carriers_collection.find({}, {"_id": 0, "CarrierDescription": 1})
        prior_carriers = []
        async for carrier in prior_carriers_cursor:
            prior_carriers.append(carrier["CarrierDescription"])
        return prior_carriers
    except Exception as e:
        raise ValueError("An error occurred while fetching prior carriers") from e
    
def update_schema_type_for_ny_surcharge(schema_type: list[str], content: list[str]) -> list[str]:
    if 'new_york_surcharge_form_or_applicable_waiver' in schema_type:
        schema_type.remove('new_york_surcharge_form_or_applicable_waiver')
        content_mapping = {
            'DOH -4403': 'ny_surcharge_4403',
            'DOH -4264': 'ny_surcharge_4264',
            'DOH -4399': 'ny_surcharge_4399',
            'Non-Participation Election Form': 'non-participation_election_form',
            'Non-Participation': 'non-participation_election_form'
        }
 
        for content in content:
            for key, value in content_mapping.items():
                if key in content and value not in schema_type:
                    schema_type.append(value)
    
    return schema_type

def get_samx_one_proposal_request_payload(quote: dict) -> dict:
    funding_type = quote.get("fundingType", "")
    medicalPlanType = []
    if "FI" in funding_type:
        medicalPlanType.append("Fully Insured")
    if "LF" in funding_type:
        medicalPlanType.append("Level Funded")
    quote["_id"] = str(quote["_id"])

    req_payload = {
        "exportType": "PDF",
        "whichPlansInProposal": "",
        "proposalType": [
            "Summary"
        ],
        "medicalPlanType": medicalPlanType,
        "optionalDocs": False,
        "oxfordMedicalSbcFI": [],
        "medicalBenefitSummaryLF": [],
        "medicalBenefitSummaryFI": [],
        "dentalBenefitSummary": [],
        "visionBenefitSummary": [],
        "basicLifeBenefitSummary": [],
        "suppLifeBenefitSummary": [],
        "stdBenefitSummary": [],
        "ltdBenefitSummary": [],
        "nevadaStopLossDisclosure": [],
        "isNevadaStopLossDisclosure": False,
        "recipients": [],
        "to": "",
        "from": "",
        "subject": "",
        "message": "",
        "quote": {
            **quote,
            "filterCriteria": {
                "favorites": False,
                "planCode": None,
                "labxray": {
                    "complete": False,
                    "dedandcoins": True
                },
                "planName": {
                    "essential": False,
                    "advantage": True
                },
                "planCategory": {
                    "ppo": False,
                    "epo": False,
                    "hsappo": False,
                    "hsaepo": False
                },
                "network_LF": {},
                "oopMax": [],
                "deductible": [],
                "premRange": [],
                "productCategory": {
                    "charterepo": False,
                    "charterhsaepo": False,
                    "proformanceepocharternetwork": False,
                    "epo": False,
                    "hsaepo": False,
                    "hsa": False,
                    "navigateepo": False,
                    "navigatehsaepo": False,
                    "proformanceeponavigatenetwork": False,
                    "nexusoaepo": False,
                    "nexusoahsaepo": False,
                    "nexusoaphsa": False,
                    "nexusoapppo": False,
                    "ppo": False,
                    "proformanceepo": False,
                    "proformanceppo": False,
                    "surestppo": False
                },
                "portfolioOfferingType": {
                    "surestlevelfunded": False,
                    "unitedhealthcarelevelfunded": False
                },
                "ratingMethod": {
                    "age": False,
                    "tier": False
                },
                "licenseType": {
                    "hmo": False,
                    "ppo": False,
                    "ins": False,
                    "hny": False,
                    "epo": False
                },
                "hsaHra": {
                    "hsa": False,
                    "hra": False
                },
                "network_FI": {},
                "metal": {
                    "bronze": False,
                    "silver": False,
                    "gold": False,
                    "platinum": False
                },
                "ooa": {
                    "In": False,
                    "Out": False,
                    "Out - Live and Travel": False,
                    "Out - Travel": False,
                    "yes": False,
                    "no": False
                },
                "pcpRequired": {
                    "yes": False,
                    "no": False
                },
                "uhcRewards": {
                    "uhcRewardsCore": False,
                    "uhcRewardsPremium": False
                },
                "riderDomesticPartner": {
                    "yes": False,
                    "no": False
                },
                "riderAge29": {
                    "yes": False,
                    "no": False
                },
                "packages": {
                    "tx054": False,
                    "tx055": False
                },
                "tiered": {
                    "isTiered": False
                },
                "network": {
                    "choiceplus": False,
                    "choice": False,
                    "navigate": False
                }
            },
            "marketType": "ALL",
            "premiumInfo": {
                "medicalEmployeeContributionMethod": "%",
                "medicalDependentContributionMethod": "%",
                "medicalEmployeeContribution": 50,
                "medicalDependentContribution": 0,
                "dentalEmployeeContributionMethod": "%",
                "dentalDependentContributionMethod": "%",
                "dentalEmployeeContribution": 0,
                "dentalDependentContribution": 0,
                "visionEmployeeContributionMethod": "%",
                "visionDependentContributionMethod": "%",
                "visionEmployeeContribution": 0,
                "visionDependentContribution": 0,
                "lifeEmployeeContributionMethod": "%",
                "lifeEmployeeContribution": 0,
                "suppLifeEmployeeContributionMethod": "%",
                "suppLifeEmployeeContribution": 0,
                "medicalEmployeeContributionUpdated": False,
                "stdEmployeeContributionMethod": "%",
                "stdEmployeeContribution": 0,
                "ltdEmployeeContributionMethod": "%",
                "ltdEmployeeContribution": 0,
                "lifeDependentContributionMethod": "%",
                "lifeDependentContribution": 0
            },
            "countingMethodology": "Eligible Employee",
            "countingMethodologyMin": 1,
            "countingMethodologyMax": 50,
        }
    }
    return req_payload

def get_quotes_url(env: str) -> str:
    if env == "dev":
        return os.environ.get('SAMX_ONE_QUOTE_SERVICE_URL_DEV')
    elif env == "test":
        return os.environ.get('SAMX_ONE_QUOTE_SERVICE_URL_TEST')
    elif env == "uat":
        return os.environ.get('SAMX_ONE_QUOTE_SERVICE_URL_UAT')
    elif env == "stage":
        return os.environ.get('SAMX_ONE_QUOTE_SERVICE_URL_STAGE')
    else:
        return os.environ.get('SAMX_ONE_QUOTE_SERVICE_URL')

async def get_samx_one_quote(query: dict, projection: dict, env: str):
    url = get_quotes_url(env)
    quote = None
    request_payload = {
        "searchQuery": query,
        "projectQuery": projection
    }
    async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
        response = await client.post(
            url + '/lingo/get/quote',
            json=request_payload,
            headers={"Content-Type": "application/json"}
        )
        if response.status_code == 200:
            try:
                quote = response.json()
                quote = quote.get('result', None)
            except ValueError:
                raise ValueError("Invalid JSON response from SAMX One service")
        else:
            raise ValueError(f"Failed to fetch quote")
        
        return quote

async def update_samx_one_quote(quote_id: str, update_data: dict, env: str):
    url = get_quotes_url(env)
    request_payload = {
        "quoteId": quote_id,
        "updateData": update_data
    }
    async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
        response = await client.put(
            url + '/lingo/update/quote',
            json=request_payload,
            headers={"Content-Type": "application/json"}
        )
        if response.status_code == 200:
            try:
                return response.json()
            except ValueError:
                raise ValueError("Invalid JSON response from SAMX One service")
        else:
            raise ValueError(f"Failed to update quote")
        
async def create_samx_one_quote(quoteData: dict, env: str):
    """Create a new SAMX One quote using the provided data."""

    url = get_quotes_url(env)

    request_payload = {
        "quoteData": quoteData
    }
    async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
        response = await client.post(
            url + '/lingo/create/quote',
            json=request_payload,
            headers={"Content-Type": "application/json"}
        )
        if response.status_code == 200:
            try:
                # Parse the JSON response into a Python dictionary
                result = response.json()
                return result
            except ValueError:
                raise ValueError("Invalid JSON response from SAMX One service")
        else:
            raise ValueError(f"Failed to create quote")

async def generate_caseId(env: str):
    """Create a case tracking entry using the provided data."""
    url = get_quotes_url(env)

    async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
        response = await client.get(
            url + '/lingo/caseId/create',
            headers={"Content-Type": "application/json"}
        )
        if response.status_code == 200:
            try:
                data= response.json()
                return data
            except ValueError:
                raise ValueError("Invalid JSON response from common api service")
        else:
            raise ValueError(f"Failed to generate case ID") 

async def get_zip_info(zipcode: str):
    try:
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
            response = await client.get(
                f"{os.environ.get('SAMX_ONE_COMMON_SERVICE_URL')}/zip-code/get-zip-info/{zipcode}",
                headers={"Content-Type": "application/json"}
            )
            if response.status_code == 200:
                data = response.json()
                if not data or len(data) == 0:
                    return {}
                
                item = data[0]
                mapped_zip_info = {
                    "zipCode": item.get("zipCode"),
                    "city": item.get("city"),
                    "county": item.get("county"),
                    "countyFIPSCode": item.get("countyFIPSCode"),
                    "marketNo": item.get("marketNo"),
                    "stateCode": item.get("stateCode"),
                    "stateFIPSCode": item.get("stateFIPSCode"),
                    "productAvailability": item.get("productAvailability"),
                    "isFundingTypeEnabledLF": item.get("isFundingTypeEnabledLF"),
                }
                return mapped_zip_info
            else:
                return {"zipCode": zipcode}
    except Exception as error:
        print(f"ZIP CODE ERROR: {error}")
        return {"zipCode": zipcode}

async def get_sic_info(sic_code: str, effective_date: str):

    try:
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
            response = await client.get(
                f"{os.environ.get('SAMX_ONE_COMMON_SERVICE_URL')}/sic-code/get-sic-info/{sic_code}?effectiveDate={effective_date}",
                headers={"Content-Type": "application/json"}
            )
            if response.status_code == 200:
                data = response.json()
                if not data or len(data) == 0:
                    return {}
                
                item = data[0]
                mapped_sic_info = {
                    "sicCode": item.get("sicCode"),
                    "sicCodeDescription": item.get("description"),
                    "productAvailability": item.get("productAvailability"),
                }
                print(f"SIC CODE INFO: {mapped_sic_info}")
                return mapped_sic_info
            else:
                return {"sicCode": sic_code}
    except Exception as error:
        print(f"SIC CODE ERROR: {error}")
        return {"sicCode": sic_code}

async def create_case_track_payload_initial(quote, env, quote_id=None):
    try:
        default_case_track_owners = {
            "salesRep": [],
            "producer": [],
            "agency": [],
            "generalAgent": [],
            "gaSalesRep": [],
        }
        
        case_track_payload = {
            "caseId": quote["caseId"],
            "fundingType": ["LF"],
            "businessType": "NB",
            "source": "BNE",
            "userId": "", # TODO: Set userId based on the current user context
            "caseStatus": 100,
            "externalStatusDesc": "Quoting: Application Submission In-Process",
            "internalStatusDesc": "Quoting: Application Submission In-Process",
            "selectedProducts": quote["selectedProducts"],
            "platform": "CIRRUS",
            "companyName": quote["companyProfile"]["companyName"],
            "effectiveDate": quote["companyProfile"]["effectiveDate"],
            "stateCode": quote["companyProfile"]["state"] or "",
            "zipCode": quote["companyProfile"]["zipCode"] or "",
            "totalEligibleEmps": quote["companyProfile"]["totalEligEmployees"],
            "createdBy": "", # TODO: Set userId based on the current user context
            "lastUpdatedPage": "/",
            "owners": default_case_track_owners,
            "vendorSource":"SAMx One"
        }
        
        # Add shopping ID if available
        if quote_id:
            case_track_payload["shoppingId"] = quote_id
        url = get_quotes_url(env)
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
            response = await client.post(
            f"{url}/lingo/casetrack/insert",
            json={"caseTracking": case_track_payload},
            headers={"Content-Type": "application/json"}
            )
            if response.status_code == 200:
                try:
                    return response.json()
                except ValueError:
                    raise ValueError("Invalid JSON response from SAMX One service")
            else:
                raise ValueError(f"Failed to create case track payload: {response.text}")
    except Exception as error:
        print(f"Error creating case track payload: {error}")
        raise ValueError("Failed to create case track payload")
    
def generate_quote_tracking_number(source, business_type):
    timestamp = int(time.time() * 1000)
    random_num = random.randint(0, 99)
    unique_id = f"{timestamp}{random_num:02d}"
    return f"{source}-{business_type}-{unique_id}"

async def extract_census(file):
    try:
        file_content = await file.read()
        files = {"file": (file.filename, file_content, file.content_type)}
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
            response = await client.post(
                "https://qrt-dev-formatter-operations.hcck8s-ctc-np101.optum.com/upload/samx/json",
                files=files, 
            )
            response.raise_for_status()
            return response.json()
    except Exception as e:
        raise Exception(f"Error calling extract census api: {str(e)}")


def is_at_least_one_plan_selected(quote: dict) -> bool:
    plan_fields = [
        "selectedDentalPlans",
        "selectedDepLifePlans",
        "selectedLTDPlans",
        "selectedSTDPlans",
        "selectedSuppLifePlans",
    ]

    if "selectedMedicalPlans" in quote:
        medical_plans = quote["selectedMedicalPlans"]
        if medical_plans.get("FI") or medical_plans.get("LF"):
            return True

    # Check other plan fields
    for field in plan_fields:
        if field in quote and quote[field]:
            return True
    return False

def is_page_allowed_to_update_or_navigate(page_validation: dict[str, bool], page: str) -> tuple[bool, str]:
    if not page_validation:
        return False, "Page validation is not provided."
    if page == "start-quote":
        return True, ""
    if page == "group-details":
        return page_validation.get("isGroupContextValid", False), "Please complete the group context before proceeding to group details."
    if page == "owner-information":
        return page_validation.get("isGroupDetailsValid", False), "Please complete the group details before proceeding to owner information."
    if page == "census":
        return page_validation.get("isGroupDetailsValid", False), "Please complete the group details before proceeding to census."
    if page == "plan-selection":
        return page_validation.get("isCensusValid", False), "Please complete the census before proceeding to plan selection."
    if page == "employer-attestation":
        return page_validation.get("isGroupContextValid", False) and page_validation.get("isGroupDetailsValid", False) and  page_validation.get("isCensusValid", False) and page_validation.get("isCensusValid", False), "Please complete the group context, group details, and census before proceeding to employer attestation."
    if page == "employee-attestation":
        return page_validation.get("isEmployerAttestationValid", False) and page_validation.get("isGroupContextValid", False) and page_validation.get("isGroupDetailsValid", False) and  page_validation.get("isCensusValid", False) and page_validation.get("isCensusValid", False), "Please complete the group context, group details, census and employer attestation before proceeding to employer attestation."
    return False
    
def is_employees_valid(quote: dict) -> bool:
    employees = quote.get("employees", [])
    if not quote or not employees:
        return False
    
    try:
        errors = {}
        for i, emp_data in enumerate(employees):
            try:
                # Validate employee data using the Employee model imported from classes.py
                Employee(**emp_data)
            except ValidationError as e:
                # Store validation errors by employee index
                errors[i] = e.errors()
        
        return  not len(errors) > 0
    except Exception as e:
        # If there's an error during validation, assume employees are invalid
        print(f"Error validating employees: {str(e)}")
        return False
