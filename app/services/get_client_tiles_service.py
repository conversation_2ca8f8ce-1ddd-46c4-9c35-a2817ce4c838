from config.db import db_core
import traceback
from fastapi.responses import JSONResponse
import logging

from graph.auth_nodes.helpers import (
    build_bne_query_variables,
    get_bne_query_template,
    call_bne_graphql_api,
    QUERY_TYPE_PERSONA
)


async def get_user_roles_from_graphql(uuid: str):

    query = get_bne_query_template(QUERY_TYPE_PERSONA)
    variables = build_bne_query_variables(QUERY_TYPE_PERSONA, ohid_uuid=uuid)
    return await call_bne_graphql_api(query, variables)
  
async def get_client_tiles(client_id, uuid):
    try:
        collection = db_core['ClientTiles']
        query = {'client_id': client_id}
        client_tiles = await collection.find_one(query)

        if not client_tiles:
            query = {'client_id': "Internal"}  # Fallback to internal client if no specific client found
            client_tiles = await collection.find_one(query)

        tiles = client_tiles.get("tiles", [])

        if client_tiles.get("authorizationRequired"):
            graphQL_response = await get_user_roles_from_graphql(uuid)
            user_profile = graphQL_response.json().get("data", {}).get("userProfile", {})

            if user_profile is None:
                error_message = "User profile not found or error in GraphQL response."
                logging.warning(f"GraphQL error: {graphQL_response.json().get('errors')}")
                return JSONResponse(
                    content={
                        "status": "success",
                        "tiles": [],
                        "message": error_message,
                    },
                    status_code=200
                )

            user_personas = set(
                persona.get("code", "").upper() for persona in user_profile.get("personas", []) or []
                if persona and persona.get("code")
            )
            user_roles = set(
                edge.get("node", {}).get("code", "").upper() 
                for edge in (user_profile.get("roles", {}) or {}).get("edges", []) or []
                if edge and edge.get("node", {}) and edge.get("node", {}).get("code")
            )

            filtered_tiles = []
            for tile in tiles:
                tile_user_types = set(ut.upper() for ut in tile.get("user_type", []) if ut and isinstance(ut, str))
                tile_role_codes = set(rc.upper() for rc in tile.get("role_code", []) if rc and isinstance(rc, str))
                auth_not_required = set(ut.upper() for ut in tile.get("AuthNotRequired", []) if ut and isinstance(ut, str))

                # If any user persona or user role matches AuthNotRequired, include the tile immediately
                if user_personas & auth_not_required or user_roles & auth_not_required:
                    filtered_tiles.append(tile)
                    continue  # Skip further checks for this tile

                # Otherwise, require both user_type and role_code match
                if user_personas & tile_user_types and user_roles & tile_role_codes:
                    filtered_tiles.append(tile)

            tiles = filtered_tiles

        return JSONResponse(
            content={"status": "success", "tiles": tiles},
            status_code=200
        )
    except Exception as e:
        logging.error("An error occurred in get_client_tiles", exc_info=True)
        return JSONResponse(
            content={
                "status": "error",
                "tiles": [],
                "message": "An unexpected error occurred during getClientTiles. Please try again later.",
            },
            status_code=500
        )
