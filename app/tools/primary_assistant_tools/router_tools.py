from pydantic import BaseModel

class ToPlanRecommendationAssistant(BaseModel):
    """Transfers work to a specialized assistant to handle "Plan recommendation", "Shopping plan recommendation". It can also process pdf data."""
    
class ToCirrusAssistant(BaseModel):
    """Transfers work to a specialized assistant to handle every query related to "Group Inquiry", "Member Inquiry" """ #, "Member enrollment/Installation", "Member status check" in Cirrus. It can also process pdf data."""
    
class ToDataConverterAssistant(BaseModel):
    """Transfers work to a specialized assistant to handle queries related to data conversion. It can also process pdf data."""

class ToSurestAssistant(BaseModel):
    """Transfers all the work to a specialized assistant to handle every question related to surest plans, surest videos, any details related to surest, what can be done with surest, or surest training materials."""

class ToSamxOneAssistant(BaseModel):
    """Transfers all the work to a specialized assistant to handle every request or question related to 
        1. create structure from PDF using schema type.
        2. validation of binder-check.
        3. Verify application docs pdf for a given quote id
        3. available capabilities of samx one.
        4. ask questions related to quote or case, update/modify/add quote or case, handle navigation for quote or case, generate proposal for quote or case, start new quote or case, create quote or case using uploaded documents, quote through separate tool, quote or case through questionnaire, view quote or case, suggest specialty plans.
    """

class ToDocumentSearchAssistant(BaseModel):
    """Transfers all the work to a specialized assistant to handle every request or question related to Broker N Employer(BNE) FAQ queries, get SBC (Summary Benefits Coverage), get Forms, get Employer handbook, get Member handbook, document search, video search or any queries matching the strings `Broker Training`, `Employer Training`, `Internal Training`, or `Find Resources`. This Agent can also answer questions related to enrollment process. This agent can answer any question related to training materials, reports and guides.
    This agent can **ONLY answer report related questions to these **REPORTS: 'Monthly Claims Utilization and Reports Guide | Level Funded | Select markets | 2-100+', 'Oxford FI Sample Report Package','UnitedHealthcare FI Sample Report Package', 'UnitedHealthcare ASO Sample Report Package', 'Claims Experience Reporting for Brokers and Employers (CER Reports)'.
    Use this assistant for questions explicitly asking about 'BNE FAQs', 'document content', 'videos or knowledge stored within searchable repositories.
    (Do not include report names/types to the user in the follow-ups.)"""
    
class ToPortalFormEnrollmentAssistant(BaseModel):
    """
     Transfers all the work to a specialized assistant to handle request strictly related to enrollment via form upload, No follow-ups are required for this agent. 
     Fact: This assistant does not answer questions and instead this assistant is designed just to complete the enrollment process via form upload. 
     If the user query matches the string 'Initiate Enrollment Process', select this Assistant otherwise route to other available Agents.
    """
    
class ToCompensationAssistant(BaseModel):
    """Transfers all the work to a specialized assistant to handle every request or question related to Broker/Producer details, Commission Statements, Broker related Customer Details, Customer wildcard Search with Name,  Compensation Details/summary, Commission Rates, Service Fee Agreement(SFA), Service Fee Agreement Summary, Customer Override, Manual Holds, Compensation Prompts and Filters related to SFA, Compensation Details and Commission Statements or any queries related to compensation. This agent can answer any question related to compensation, producer/broker and customer details."""

class ToServiceTrackerAssistant(BaseModel):
    """Transfers all the work to a specialized assistant to handle every request or question related to Service Tracking, Open Issues, Service Tickets etc. This agent can answer any question related to service tracker, open issues, service tickets, service tracking, service tracker details, service tracker status, service tracker updates, service tracker history, service tracker reports, and service tracker inquiries."""

router_tools = [ToPlanRecommendationAssistant.__name__, ToCirrusAssistant.__name__, ToDataConverterAssistant.__name__, ToSurestAssistant.__name__, ToSamxOneAssistant.__name__, ToDocumentSearchAssistant.__name__, ToPortalFormEnrollmentAssistant.__name__, ToCompensationAssistant.__name__, ToServiceTrackerAssistant.__name__]
