"""
This module provides utility functions for formatting and adjusting JSON payloads used in an API gateway application.

It includes functions for retrieving a default JSON structure, 
rechecking and adjusting JSON payloads, 
booleanizing checkboxes,
making field adjustments, 
converting gender values to letters, 
removing hyphens in physician IDs, 
remapping reasons for application,
converting string values to None, and 
formatting phone numbers and SSNs.
"""

# Rest of the code ...

import re

def recheck_json_payload(gpt_json,json_struct):
    for key in json_struct.keys():  
        if isinstance(json_struct[key], dict):  
            if key in gpt_json and isinstance(gpt_json[key], dict):  
                recheck_json_payload(gpt_json[key],json_struct[key])  
        else:  
            if key in gpt_json:  
                json_struct[key] = gpt_json[key]  
    return json_struct


def booleanize_checkboxes(gptJson):
    positives = ['yes', 'on', 'true', '1', 'active', 'enabled', 'checked', 'present', 'positive', 'up', 'open', 'valid', 'okay', 'go', 'success', 'win', 'pass', 'proceed', 'accepted', 'confirmed']
    negatives = ['no', 'off', 'false', '0', 'inactive', 'disabled', 'unchecked', 'absent', 'negative', 'down', 'closed', 'invalid', 'not okay', 'stop', 'failure', 'lose', 'fail', 'halt', 'rejected', 'denied']
    
    medCovKeyList = ["OtherCVG", "MedicarePartA", "MedicarePartB", "MedicarePartD", "MedicareS/DPartA", "MedicareS/DPartB", "MedicareS/DPartD"]

    for key in medCovKeyList:
        if gptJson["OtherMedCovInfo"][key]:
                    if str(gptJson["OtherMedCovInfo"][key]).lower() in positives:
                        gptJson["OtherMedCovInfo"][key] = True
                    if str(gptJson["OtherMedCovInfo"][key]).lower() in negatives:
                        gptJson["OtherMedCovInfo"][key] = False
        else:
            gptJson["OtherMedCovInfo"][key] = False

    return gptJson


def field_adjustments(adjustedResponse):
    if adjustedResponse["GroupNumber"] != None:
        adjustedResponse["GroupNumber"] = adjustedResponse["GroupNumber"][0:7]
    # adjustedResponse["OtherMedCovInfo"]["NameOfOtherCarrier"] = "abc"
    if adjustedResponse["OtherMedCovInfo"]["NameOfOtherCarrier"] != None:
        adjustedResponse["OtherMedCovInfo"]["OtherCVG"] = True
    return adjustedResponse


def gender_to_letter(gpt_response):
    for key in gpt_response.keys():
        if isinstance(gpt_response[key], dict):
            if "Gender" in gpt_response[key]:
                gender = gpt_response[key]["Gender"]
                if gender and (('female' in (gender.lower() if gender else "Not Specified")) or (gender.upper() == "F")):
                    gpt_response[key]["Gender"] = 'Female'
                elif gender and (('male' in (gender.lower() if gender else "Not Specified")) or (gender.upper() == "M")):
                    gpt_response[key]["Gender"] = 'Male'
                else:
                    gpt_response[key]["Gender"] = None
    return gpt_response


def remove_hyphens_in_physicianId(gpt_response):  
    for key in gpt_response.keys():  
        if isinstance(gpt_response[key], dict):  
            if "PhysicianID" in gpt_response[key] and gpt_response[key]["PhysicianID"] is not None:  
                gpt_response[key]["PhysicianID"] = gpt_response[key]["PhysicianID"].replace('-', '')  
  
    return gpt_response 


def reason_reMapping(adjustedResponse):
    reasons = ["New Group Plan", "New Hire", "Special Enrollment", "Re-enrollment", "Rehire", "Annual Open Enrolment", "Late Enrollee"]
    if adjustedResponse["ReasonForApplication"]["Reason"]:
        matches = [reason for reason in reasons if reason in adjustedResponse["ReasonForApplication"]["Reason"]]
        if len(matches) <= 1:
            adjustedResponse["ReasonForApplication"]["Reason"] = matches[0] if matches else None
        else:
            adjustedResponse["ReasonForApplication"]["Reason"] = None
        # if adjustedResponse["ReasonForApplication"]["Reason"] not in reasons:
        #     adjustedResponse["ReasonForApplication"]["Reason"] = Nonex
    return adjustedResponse


def stringToNone(gptJson):
    memberList = ["EmployeeInfo", "SpouseInfo", "Dependent1Info",
                       "Dependent2Info", "Dependent3Info", "Dependent4Info"]
    for mem in memberList:
        for key in gptJson[mem]:
            gptJson[mem][key] = None if gptJson[mem][key] == "None" else gptJson[mem][key]
    for key in gptJson:
        gptJson[key] = None if gptJson[key] == "None" else gptJson[key]
    return gptJson


def format_phone_and_ssn(adjustedResponse):  
    phnList = ['HomePhone','CellPhone','WorkPhone']
    for item in phnList:
        if adjustedResponse['EmployeeInfo'][item]:
            phone = adjustedResponse['EmployeeInfo'][item]
            digits = re.sub(r'\D', '', phone)   
            if len(digits) != 10:  
                adjustedResponse['EmployeeInfo'][item] = None   
            else:
                adjustedResponse['EmployeeInfo'][item] = digits[:3] + '-' + digits[3:6] + '-' + digits[6:] 

    memberList = ["EmployeeInfo", "SpouseInfo", "Dependent1Info",
                       "Dependent2Info", "Dependent3Info", "Dependent4Info"]
    for member in memberList:
        if adjustedResponse[member]['SSN']:
            ssn = adjustedResponse[member]['SSN']
            adjustedResponse[member]['SSN'] = re.sub(r'\D', '', ssn)
    return adjustedResponse

