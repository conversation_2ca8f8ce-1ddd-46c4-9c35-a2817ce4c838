"""
Azure AI Search Service

This service provides RAG functionality using Azure AI Search as the vector store,
maintaining the same interface as the PostgreSQL implementation for easy A/B testing.
"""

import json
import time
from typing import List, Dict, Any, Optional, Tuple
from config.azure_ai_search import get_async_search_client
from config.openAI import embeddings
from services.event_logger_service import logger
from services.user_info_service import extract_user_info_from_state
import traceback

class AzureAISearchService:
    """Service class for Azure AI Search RAG operations"""
    
    def __init__(self):
        self.embedding_dimension = 1536  # text-embedding-3-small
    
    async def similarity_search(
        self,
        index_name: str,
        query: str,
        top_k: int = 5,
        state: Optional[Dict] = None
    ) -> List[Dict[str, Any]]:
        """
        Perform similarity search using Azure AI Search vector search
        
        Args:
            index_name: Name of the Azure AI Search index
            query: Search query text
            top_k: Number of results to return
            state: Optional state for logging
            
        Returns:
            List of search results with same format as PostgreSQL implementation
        """
        try:
            # Generate embedding for the query
            start_time = time.time()
            question_embedding = await embeddings.aembed_query(query)
            embedding_time = (time.time() - start_time) * 1000
            
            # Get search client
            search_client = get_async_search_client(index_name)
            
            # Perform vector search
            search_start_time = time.time()
            
            # Use vector search with the embedding
            search_results = await search_client.search(
                search_text=None,  # Pure vector search
                vector_queries=[{
                    "vector": question_embedding,
                    "k_nearest_neighbors": top_k,
                    "fields": "embedding"
                }],
                select=["id", "file_name", "file_url", "page_content", "created_at"],
                top=top_k
            )
            
            search_time = (time.time() - search_start_time) * 1000
            
            # Convert results to match PostgreSQL format
            results = []
            async for result in search_results:
                # Calculate similarity score (Azure AI Search returns relevance score)
                score = result.get('@search.score', 0.0)
                # Convert to distance-like score (lower is better, like PostgreSQL cosine distance)
                distance_score = 1.0 - score if score <= 1.0 else 0.0
                
                result_dict = {
                    'file_name': result.get('file_name', ''),
                    'file_url': result.get('file_url', ''),
                    'page_content': result.get('page_content', ''),
                    'created_at': result.get('created_at'),
                    'score': distance_score
                }
                results.append(result_dict)
            
            # Log search operation if state is provided
            if state:
                await self._log_search_operation(
                    state=state,
                    index_name=index_name,
                    query=query,
                    results_count=len(results),
                    embedding_time=embedding_time,
                    search_time=search_time,
                    embedding_length=len(question_embedding)
                )
            
            return results
            
        except Exception as e:
            error_msg = f"Azure AI Search similarity search failed: {str(e)}"
            if state:
                await self._log_search_error(state, index_name, query, error_msg)
            raise Exception(error_msg)
    
    async def hybrid_search(
        self,
        index_name: str,
        query: str,
        top_k: int = 5,
        state: Optional[Dict] = None
    ) -> List[Dict[str, Any]]:
        """
        Perform hybrid search (vector + text) using Azure AI Search
        
        Args:
            index_name: Name of the Azure AI Search index
            query: Search query text
            top_k: Number of results to return
            state: Optional state for logging
            
        Returns:
            List of search results
        """
        try:
            # Generate embedding for the query
            start_time = time.time()
            question_embedding = await embeddings.aembed_query(query)
            embedding_time = (time.time() - start_time) * 1000
            
            # Get search client
            search_client = get_async_search_client(index_name)
            
            # Perform hybrid search
            search_start_time = time.time()
            
            search_results = await search_client.search(
                search_text=query,  # Text search component
                vector_queries=[{
                    "vector": question_embedding,
                    "k_nearest_neighbors": top_k,
                    "fields": "embedding"
                }],
                select=["id", "file_name", "file_url", "page_content", "created_at"],
                top=top_k,
                query_type="semantic",  # Enable semantic search
                semantic_configuration_name="default-semantic-config"
            )
            
            search_time = (time.time() - search_start_time) * 1000
            
            # Convert results to match PostgreSQL format
            results = []
            async for result in search_results:
                score = result.get('@search.score', 0.0)
                distance_score = 1.0 - score if score <= 1.0 else 0.0
                
                result_dict = {
                    'file_name': result.get('file_name', ''),
                    'file_url': result.get('file_url', ''),
                    'page_content': result.get('page_content', ''),
                    'created_at': result.get('created_at'),
                    'score': distance_score,
                    'semantic_captions': result.get('@search.captions', []),
                    'semantic_highlights': result.get('@search.highlights', {})
                }
                results.append(result_dict)
            
            # Log search operation
            if state:
                await self._log_search_operation(
                    state=state,
                    index_name=index_name,
                    query=query,
                    results_count=len(results),
                    embedding_time=embedding_time,
                    search_time=search_time,
                    embedding_length=len(question_embedding),
                    search_type="hybrid"
                )
            
            return results
            
        except Exception as e:
            error_msg = f"Azure AI Search hybrid search failed: {str(e)}"
            if state:
                await self._log_search_error(state, index_name, query, error_msg)
            raise Exception(error_msg)
    
    async def _log_search_operation(
        self,
        state: Dict,
        index_name: str,
        query: str,
        results_count: int,
        embedding_time: float,
        search_time: float,
        embedding_length: int,
        search_type: str = "vector"
    ):
        """Log search operation details"""
        try:
            user_info = extract_user_info_from_state(state)
            
            db_event_details = {
                "operation": "search",
                "collection": index_name,
                "query": f"Azure AI Search {search_type} search",
                "update": None,
                "document_count": results_count,
                "execution_time_ms": search_time,
                "db_name": "azure_ai_search",
                "index_used": index_name,
                "error": None,
                "metadata": {
                    "embedding_length": embedding_length,
                    "embedding_time_ms": embedding_time,
                    "search_type": search_type,
                    "query_text": query[:100] + "..." if len(query) > 100 else query
                }
            }
            
            await logger.info(
                user_info.get("uuid"),
                user_info.get("user_name"),
                user_info.get("session_id"),
                user_info.get("request_id"),
                user_info.get("client_id"),
                "azure_ai_search",
                "search_operation",
                db_event_details,
                None,
                200,
                f"Azure AI Search {search_type} search completed",
                None
            )
            
        except Exception as e:
            print(f"Failed to log Azure AI Search operation: {e}")
    
    async def _log_search_error(
        self,
        state: Dict,
        index_name: str,
        query: str,
        error_msg: str
    ):
        """Log search operation error"""
        try:
            user_info = extract_user_info_from_state(state)
            
            await logger.error(
                user_info.get("uuid"),
                user_info.get("user_name"),
                user_info.get("session_id"),
                user_info.get("request_id"),
                user_info.get("client_id"),
                "azure_ai_search",
                "search_error",
                {"index_name": index_name, "query": query[:100]},
                None,
                500,
                error_msg,
                None
            )
            
        except Exception as e:
            print(f"Failed to log Azure AI Search error: {e}")

# Global service instance
azure_ai_search_service = AzureAISearchService()

# Convenience functions that match the PostgreSQL interface
async def azure_similarity_search(
    index_name: str,
    query: str,
    top_k: int = 5,
    state: Optional[Dict] = None
) -> List[Dict[str, Any]]:
    """Convenience function for similarity search"""
    return await azure_ai_search_service.similarity_search(index_name, query, top_k, state)

async def azure_hybrid_search(
    index_name: str,
    query: str,
    top_k: int = 5,
    state: Optional[Dict] = None
) -> List[Dict[str, Any]]:
    """Convenience function for hybrid search"""
    return await azure_ai_search_service.hybrid_search(index_name, query, top_k, state)

# Index name mappings for easy migration
AZURE_INDEX_MAPPINGS = {
    "surest_training_material": "surest-training-material",
    "surest_training_video": "surest-training-video",
    "bne_training_document": "bne-training-document"
}
