from client_plugins.gco_reporting.gco_reporting.tool import get_report, get_date, get_link, get_group_list, get_new_published_reports, cer_ticket_links_guide, get_top_downloaded_reports, get_recent_downloaded_reports
from graph.state import State
from graph.nodes import Assistant
from config.openAI import model
from langgraph.graph import END, StateGraph, START
from langgraph.prebuilt import tools_condition

from langchain_core.runnables import RunnableLambda
from langgraph.prebuilt import ToolNode
from tools.common_tools import CompleteOrEscalate
from utils.helpers.constants import TOOL_CALL_ERROR_MESSAGE
from client_plugins.gco_reporting.gco_reporting.prompts import gco_reporting_system_prompt
# Import the other required modules

gco_reporting_tools = [get_report, get_date, get_link, get_group_list, get_new_published_reports, cer_ticket_links_guide, get_top_downloaded_reports, get_recent_downloaded_reports] + [CompleteOrEscalate]
# gco_reporting_tool_node = ToolNode(gco_reporting_tools, handle_tool_errors = TOOL_CALL_ERROR_MESSAGE)
gco_reporting_tool_node = ToolNode(gco_reporting_tools, handle_tool_errors = False)
gco_reporting_runnable = gco_reporting_system_prompt | model.bind_tools(gco_reporting_tools)