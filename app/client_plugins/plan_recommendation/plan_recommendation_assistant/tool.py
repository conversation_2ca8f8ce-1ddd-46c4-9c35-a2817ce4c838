from langchain_core.tools import tool
from services import plan_recommendation_service
import traceback
from langgraph.prebuilt.tool_node import InjectedState
from typing import Annotated
import json 
from utils.tool_utils import log_tool_error

@tool    
async def recommend_plans(
    state: Annotated[dict, InjectedState]
   ):
    """Recommends the plan based on a uploaded PDF file.
    """
    try:
        pdf_data = state.get("pdf_data")
        
        if not pdf_data:
            return "Please upload a relevant pdf file to get plan recommendations."
        
        response = await plan_recommendation_service.get_plan_recommendation(pdf_data, state)
        
        response["client"] = "planRecommendations&Quote"
        final_resp = json.dumps(response)  
        return final_resp
    
    except Exception:
        await log_tool_error(state, traceback.format_exc(), "recommend_plans")
        raise Exception (traceback.format_exc())

@tool
async def recommend_shopping_plans(state: Annotated[dict, InjectedState]):
    """Fetches similar plans for renewal shopping or provide recommendations for shopping.
    """
    try:
        response = await plan_recommendation_service.get_shopping_plans(state)
        response["client"] = "planRecommendations&Quote"
        return json.dumps(response)  
    
    except Exception:
        await log_tool_error(state, traceback.format_exc(), "recommend_shopping_plans")
        raise Exception (traceback.format_exc())