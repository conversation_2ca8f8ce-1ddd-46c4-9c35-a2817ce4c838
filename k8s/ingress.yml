apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: magnus-oec-api
  labels:
    app: magnus-oec-api
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    cert-manager.io/cluster-issuer: "optum-app"
    cert-manager.io/common-name: "magnus-oec-api-dev.hcck8s-ctc-np101.optum.com"
spec:
  ingressClassName: nginx
  rules:
  - host: magnus-oec-api-dev.hcck8s-ctc-np101.optum.com
    http:
      paths:
      - pathType: Prefix
        path: "/"
        backend:
          service:
            name: magnus-oec-api
            port:
              number: 8080
  - host: "lingo-api-dev.optum.com"
    http:
      paths:
      - pathType: Prefix
        path: "/"
        backend:
          service:
            name: magnus-oec-api
            port:
              number: 8080
  tls:
    - hosts: 
        - magnus-oec-api-dev.hcck8s-ctc-np101.optum.com
        - lingo-api-dev.optum.com
      secretName: magnus-oec-api-ingress-secret  
