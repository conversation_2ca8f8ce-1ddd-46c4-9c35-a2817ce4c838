import io
import socket
import os
from datetime import datetime
from config.member_enroll_logging_config import member_enroll_logger

import httpx
from services.event_logger_service import logger
from services.member_enroll.mongo_service import (
    find_enrollment_document,
    upsert_enrollment_document,
    upsert_xtractor_document,
)


async def email_automation_api(email_params):
    """Send a POST request to the email automation API with the provided parameters.
    :param email_params: A dictionary containing the email parameters to send.
    :return: A dictionary with the response from the email automation API.
    :raises Exception: If an error occurs during the POST request.
    """
    url = os.getenv("EMAIL_AUTOMATION_ENDPOINT")
    ssl_verify = os.environ.get('VERIFY_SSL') == 'True'

    try:
        async with httpx.AsyncClient(verify=ssl_verify) as client:
            response = await client.post(url, json=email_params)
            status_code = response.status_code
            email_response = response.json()
            
            await logger.info(email_params["uuid"], None, None, None, email_params["clientId"], "api", "document_extraction", email_params, email_response, status_code, None, url)
            return email_response

    except Exception as e:
        error_message = f"Error making POST to Email Automation API: {str(e)}"
        await logger.error(email_params["uuid"], None, None, None, email_params["clientId"], "api", "document_extraction", email_params, None, None, error_message, url)
        return {"error": error_message}


async def bne_callback_api(request_params, callback_url):
    """Send a POST request to the BNE callback API with the provided parameters.
    :param request_params: A dictionary containing the request parameters to send.
    :param callback_url: A string to use for the callback request.
    :return: A dictionary with the response from the BNE callback API.
    :raises Exception: If an error occurs during the POST request.
    """
    ssl_verify = os.environ.get('VERIFY_SSL') == 'True'

    headers = {
        'User-Agent': socket.gethostname()
    }

    try:
        async with httpx.AsyncClient(verify=ssl_verify) as client:
            response = await client.post(callback_url, json=request_params, headers=headers)
            status_code = response.status_code

            if response.headers.get('Content-Type') == 'application/json':
                response_body = response.json()
            else:
                response_body = {}

            await logger.info(request_params["uuid"], None, None, None, request_params["clientID"], "api", "document_extraction", request_params, response_body, status_code, None, callback_url)

            return {
                "status": "Success" if status_code == 200 else "Error",
                "statusCode": status_code,
                "message": response_body.get("message", ""),
                "error": response_body.get("error", ""),
            }
            
    except Exception as e:
        await logger.error(request_params["uuid"], None, None, None, request_params["clientID"], "api", "document_extraction", request_params, None, None, str(e), callback_url)
        return {
            "status": "Error",
            "message": f"Error with BNE callback API: {str(e)}",
        }

async def process_bne_response(callback_response, job_id, initial_status, enrollment_col):
    """Process the BNE callback response and update MongoDB accordingly.
    :param callback_response: The response from the BNE callback API.
    :param job_id: The job ID associated with the callback.
    :param initial_status: The initial status of the process (Success or Error).
    :param enrollment_col: boolean True if it belongs in the enrollment collection, False if not.
    :return: None
    """
    bne_status = callback_response["status"]
    initial_messages = []
    member_enroll_logger.info("Processing callback response: %s", callback_response, extra={'job_id': job_id})

    if initial_status == "Error":
        process_status = "Error"
        mongo_record = await find_enrollment_document(job_id)
        if mongo_record:
            initial_messages = mongo_record.get("messages", [])
    else:
        process_status = "Success" if bne_status == "Success" else "Error"

    mongo_record = {
        "jobStatus": "Completed",
        "processStatus": process_status,
        "bneCallbackResponse": callback_response,
        "endTime": datetime.utcnow().isoformat()
    }

    if bne_status == "Error":
        bne_error_message = {
            "event": "Error",
            "source": "Lingo",
            "message": "Error with BNE callback"
        }
        initial_messages.append(bne_error_message)
        mongo_record["messages"] = initial_messages
        
    if enrollment_col:
        await upsert_enrollment_document(job_id, mongo_record)
    else:
        await upsert_xtractor_document(job_id, mongo_record)


async def xtractor_api(file_content, job_id):
    """Send a POST request to the xtractor API with the provided parameters.
    :param file_content: the file content to send to xtractor API.
    :param job_id: the job ID associated with the API call.
    :return: A dictionary with the response from the xtractor API.
    """
    url = os.getenv("XTRACTOR_ENDPOINT")
    ssl_verify = os.environ.get('VERIFY_SSL') == 'True'

    try:
        pdf_file = io.BytesIO(file_content)
        pdf_file.name = "file.pdf"

        form_data = {
            "pdf_file": (pdf_file.name, pdf_file, "application/pdf")
        }

        async with httpx.AsyncClient(verify=ssl_verify) as client:
            response = await client.post(url, files=form_data)
            status_code = response.status_code
            xtractor_response = response.json()
            await logger.info(None, None, None, None, None, "api", "document_extraction", None, xtractor_response, status_code, None, url)
            return {"status": "Success", "data": xtractor_response}
    
    except Exception as e:
        error_message = f"Error making xtractor POST request: {str(e)}"
        await logger.error(None, None, None, None, None, "api", "document_extraction", None, None, None, error_message, url)
        return {
            "status": "Error",
            "message": error_message,
        }

async def sbc_api(file_content, job_id):
    """Send a POST request to the sbc API with the provided parameters.
    :param file_content: the file content to send to sbc API.
    :param job_id: the job ID associated with the API call.
    :return: A dictionary with the response from the sbc API.
    """
    url = os.getenv("XTRACTOR_SBC_ENDPOINT")
    ssl_verify = os.environ.get('VERIFY_SSL') == 'True'

    try:
        pdf_file = io.BytesIO(file_content)
        pdf_file.name = "file.pdf"

        form_data = {
            "pdf_file": (pdf_file.name, pdf_file, "application/pdf")
        }

        async with httpx.AsyncClient(verify=ssl_verify) as client:
            response = await client.post(url, files=form_data)
            status_code = response.status_code
            xtractor_response = response.json()
            await logger.info(None, None, None, None, None, "api", "document_extraction", None, xtractor_response, status_code, None, url)
            return {"status": "Success", "data": xtractor_response}
    
    except Exception as e:
        error_message = f"Error making SBC xtractor POST request: {str(e)}"
        await logger.error(None, None, None, None, None, "api", "document_extraction", None, None, None, error_message, url)
        return {
            "status": "Error",
            "message": error_message,
        }
