from config.db import db_onboarding
from fastapi.responses import JSONResponse
async def get_feedback_category_service(client_id: str):
    collection = db_onboarding["Configs"]
    client_entry = await collection.find_one(
        {"type": "feedback_category", "list.client_id": client_id},
        {"list.$": 1}
    )
   
    client_feedback_categories = None
    if client_entry and "list" in client_entry and len(client_entry["list"]) > 0:
        client_feedback_categories = client_entry["list"][0].get("categories", [])
    
    if client_feedback_categories:
        return JSONResponse(
            content={"status": "success", "client_id": client_id, "feedback_categories": client_feedback_categories},
            status_code=200
        )
    
    return JSONResponse(
        content={"status": "success", "feedback_categories": [], "message": f"No feedback categories found for client {client_id}"},
        status_code=200
    )