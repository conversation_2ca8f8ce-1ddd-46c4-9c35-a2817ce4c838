from config.db import get_client_database_connection
from utils.general_utils import getTime

async def save_chat_conversation(uuid: str, user_name: str, session_id: str, request_id: str, query: str, response: dict, client_id: str):
    
    client_db = await get_client_database_connection(client_id)
    chat_collection = client_db['ChatConversation']
    
    current_time = getTime()
    message = {
        "requestId": request_id,
        "submittedData": query,
        "chatbotResponse": response,
        "feedback": {}
    }
    existing_chat = await chat_collection.find_one({"session_id": session_id})

    if existing_chat:
        await chat_collection.update_one(
            {"session_id": session_id},
            {
                "$push": {"conversation": message},
                "$set": {"update_timestamp": current_time}
            }
        )
    else:
        await chat_collection.insert_one({
            "create_timestamp": current_time,
            "update_timestamp": current_time,
            "uuid": uuid,
            "user_name": user_name,
            "session_id": session_id,
            "conversation": [message]
        })
