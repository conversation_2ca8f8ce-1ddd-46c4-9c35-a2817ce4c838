from pydantic import BaseModel
from typing import Optional, List
from typing_extensions import TypedDict

class CirrusMemberSearch(BaseModel):
    memGroupID: str
    firstNameStartsWith: Optional[str] = ""
    lastNameStartsWith:  Optional[str] = ""
    SSN: Optional[str] = ""
    clientId: str
    uuid: str
    user_name: Optional[str] = ""
    session_id: Optional[str] = ""
    request_id: Optional[str] = ""

class Plan(BaseModel):
    planName: str
    coverageType: str

class SimilarPlanList(BaseModel):
    similarPlans: List[Plan]