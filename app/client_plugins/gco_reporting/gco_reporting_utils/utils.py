import os
from difflib import SequenceMatcher
import httpx
from routes.gco import fetch_gco_reports_data
from datetime import datetime, timedelta
import random
from rapidfuzz import fuzz
from typing import List, Dict

import re


from symspellpy import SymSpell, Verbosity


# Scores considered high confidence for direct or strong substring matches
HIGH_CONFIDENCE_SCORES = {90, 98}

def normalize_text(text: str) -> str:
    text = text.lower()
    text = re.sub(r'(?<=\d)x(?=\d)', ' ', text)  # 24x7 → 24 7
    text = re.sub(r'[-/]', ' ', text)           # hyphens/slashes → space
    text = re.sub(r"'", '', text)               # remove apostrophes
    text = re.sub(r'&', ' and ', text)          # & → and
    text = re.sub(r'[^\w\s]', '', text)         # remove other punctuation
    text = re.sub(r'\s+', ' ', text).strip()    # normalize whitespace
    return text


def preprocess_group_list(group_list: List[str]) -> List[Dict[str, str]]:
    return [
        {
            "original": group,
            "lower": normalize_text(group),
            "abbr": ''.join(word[0] for word in normalize_text(group).split() if word[0].isalnum())
        }
        for group in group_list
    ]

def remove_stopwords_sentence(sentence: str) -> str:
    """
    Removes stopwords from a sentence.

    Args:
        sentence (str): The input sentence.

    Returns:
        list: The cleaned list of words with stopwords excluded.
    """
    stop_words = {'myself', 'did', "we'll", 'after', "isn't", "you'd", "couldn't", 'mightn', 'he', 'with', 'she', 'same', 'you', 'there', "you'll", 'into', 'and', 'before', 've', 'hadn', 'what',
                  'for', 'aren', 'but', 'once', 'up', "she'll", "mightn't", 'hers', "doesn't", "i'd", 'your', "aren't", 'ours', 'to', 'y', 'is', 'him', "needn't", 'had', 'haven', "they've", 'through',
                  'under', "she'd", 'so', "we're", 'yours', 'does', 'they', 're', "they'd", "don't", 'don', 'when', 'we', 'just', 'mustn', 'my', 'having', 'than', "i'm", 'ourselves', 'while', 'over', 'hasn', 'our',
                  'above', 'her', 'any', "he'd", "i've", "it'd", 'm', 'most', 'here', 'between', 'other', 'down', 'not', 'am', 'off', "we've", "they'll", 'doing', 'of', 'do', 'if', "it's", 'the', 'about', 'should',
                  'needn', "should've", 'who', "he'll", 'on', 'some', 'each', 'shouldn', 'then', 'only', "they're", 'couldn', 'at', 'its', 'more', 'these', 'during', 'how', "hasn't", 'will', 'isn', "it'll", 'shan',
                  'too', 'as', "hadn't", 'wouldn', 'won', 'from', 'herself', 'ma', 'didn', 'those', "haven't", "you're", "she's", 'them', 'own', 'o', 's', 'doesn', 'nor', "won't", 'whom', 'again', 'have', 'because',
                  't', 'below', 'both', "mustn't", 'theirs', 'd', 'his', "wasn't", 'weren', 'themselves', 'no', 'out', 'now', "wouldn't", 'been', 'me', 'such', 'i', "didn't", 'yourselves', "that'll", 'in', 'an', 'being',
                  'or', 'that', 'were', "you've", 'why', 'ain', 'against', 'further', 'are', 'this', 'which', "he's", 'wasn', 'it', "i'll", 'very', 'where', 'few', "shan't", 'their', "shouldn't", 'can', 'has', "weren't",
                  'itself', "we'd", 'all', 'himself', 'was', 'by', 'yourself', 'be', 'until', 'a', 'll'}

    words = sentence.split()
    filtered_words = [word for word in words if word.lower() not in stop_words]
    return filtered_words


def fuzzy_match_group(user_input: str, data_type: str, keyword_list: List[Dict[str, str]], top_n: int = 5) -> List[Dict[str, str]]:
    symspell = SymSpell(max_dictionary_edit_distance=2, prefix_length=7)
    preprocessed_list = preprocess_group_list(keyword_list)

    for entry in preprocessed_list:
        symspell.create_dictionary_entry(' '.join(remove_stopwords_sentence(entry["lower"])), 100)

    
    # Normalize and lowercase user input
    user_input = normalize_text(user_input)

    # Remove generic suffixes if present
    generic_suffixes = {"group", "groups", "employer", "employers", "customer", "customers"}
    user_input_tokens = user_input.split()
    user_input_tokens = [token for token in user_input_tokens if token not in generic_suffixes]
    user_input = ' '.join(user_input_tokens)

    # Remove stopwords
    user_input = ' '.join(remove_stopwords_sentence(user_input))

    suggestions = symspell.lookup(user_input, verbosity=Verbosity.CLOSEST, max_edit_distance=2)
    user_input_after_spell_check = suggestions[0].term if suggestions else user_input
    user_tokens = remove_stopwords_sentence(normalize_text(user_input_after_spell_check))

    matches = []

    for entry in preprocessed_list:
        if user_input == entry["lower"]:
            matches.append((entry["original"], 100))
        elif user_input in entry["lower"].split():
            matches.append((entry["original"], 98))
        elif all(word in entry["lower"] for word in user_input.split()):
            matches.append((entry["original"], 90))
        elif user_input == entry["abbr"]:
            matches.append((entry["original"], 95))
        elif user_input in entry["abbr"]:
            matches.append((entry["original"], 85))

    unique_matches = {}
    for match in matches:
        if match[0] not in unique_matches or match[1] > unique_matches[match[0]]:
            unique_matches[match[0]] = match[1]
    matches = [(key, value) for key, value in unique_matches.items()]

    if not matches:
        for entry in preprocessed_list:
            entry_tokens = set(remove_stopwords_sentence(entry["lower"]))
            token_overlap = len(set(user_tokens) & entry_tokens)
            unmatched_tokens = len(set(user_tokens) - entry_tokens)

            if token_overlap > 0 and unmatched_tokens == 0:
                score = fuzz.token_sort_ratio(user_input_after_spell_check, entry["lower"]) + (token_overlap * 5)
                if score >= 80:
                    matches.append((entry["original"], score))
            

    if matches:
        high_confidence_matches = [match for match in matches if match[1] in HIGH_CONFIDENCE_SCORES]
        matches = sorted(matches, key=lambda x: x[1], reverse=True)[:top_n]
        matches = high_confidence_matches + [match for match in matches if match not in high_confidence_matches]
        return [{"value": match[0], "type": data_type} for match in sorted(matches, key=lambda x: x[1], reverse=True)]


    user_words = [word for word in user_input.split() if len(word) >= 3]
    fallback_matches = []

    for entry in preprocessed_list:
        entry_words = entry["lower"].split()
        all_words_match = True
        match_count = 0

        for user_word in user_words:
            match_found = False
            for entry_word in entry_words:
                prefix_match = user_word[:3] == entry_word[:3] if len(user_word) >= 3 and len(entry_word) >= 3 else False
                suffix_match = user_word[-3:] == entry_word[-3:] if len(user_word) >= 3 and len(entry_word) >= 3 else False

                if prefix_match or suffix_match:
                    match_count += sum(1 for c in user_word if c in entry_word)
                    match_found = True
                    break

            if not match_found:
                all_words_match = False
                break

        if all_words_match:
            fallback_matches.append({
                "value": entry["original"],
                "type": data_type,
                "match_count": match_count
            })

    fallback_matches = sorted(fallback_matches, key=lambda x: x["match_count"], reverse=True)[:top_n]
    return [{"value": match["value"], "type": match["type"]} for match in fallback_matches]
 
 

def fetch_group_list(raw_group_list):
    """
    Fetches the list of groups from the gco_report JSON file.
    Handles JSON structure where group name and report count are in the same hierarchy.

    Args:
        gco_report (list): The JSON data containing report information.

    Returns:
        list: A list of dictionaries with group names and their report counts.
    """
    if raw_group_list is None or not isinstance(raw_group_list, list):
        return []
    
    group_list = []
    for entry in raw_group_list:
        if entry.get("group_name") is not None:
            if entry.get("report_types_count") is not None:
                group_list.append({
                    "value": entry.get("group_name"),
                    "type": "group",
                    "notes": f"{entry.get('report_types_count')} Report Types"
                })
            else:
                group_list.append({
                    "value": entry.get("group_name"),
                    "type": "group"
                })
    # Remove duplicates based on group_name 
    seen = set()
    unique_group_list = []
    for group in group_list:
        if group["value"] not in seen:
            unique_group_list.append(group)
            seen.add(group["value"])

    return sorted(unique_group_list, key=lambda x: x["value"].lower())

def fetch_group_list_suggestive_prompt(gco_report):
    """
    Fetches the list of groups from the gco_report JSON file.
    Returns a list of dictionaries with group names and their report counts.
    """
    if gco_report is None or not isinstance(gco_report, list):
        return []
    group_list = []
    for entry in gco_report:
        if entry.get("group_name"):
            group_list.append({"value": entry.get("group_name")})

    # Remove duplicates based on group_name
    seen = set()
    unique_group_list = []
    for group in group_list:
        if group["value"] not in seen:
            unique_group_list.append(group)
            seen.add(group["value"])
    return sorted(unique_group_list, key=lambda x: x["value"].lower())
 
 

def fetch_report_type_list(gco_report,group: str):
    """
    Fetches the list of report types for a given group from the gco_report JSON file.
    Returns a list of dictionaries with report type names and their descriptions.
    """
    if gco_report is None or not isinstance(gco_report, list):
        return []
    report_type_list = []
    for entry in gco_report:
        if entry.get("group_name") == group:
            for report in entry.get("reports", []):
                report_data = {
                    "value": report.get("report_type"),
                    "notes": report.get("report_desc"),
                    "type": "report_type",
                }
                if not any(item["value"] == report_data["value"] for item in report_type_list):
                    report_type_list.append(report_data)
        else:
            for report in entry.get("reports", []):
                report_data = {
                    "value": report.get("report_type"),
                    "notes": report.get("report_desc"),
                    "type": "report_type",
                }
                if not any(item["value"] == report_data["value"] for item in report_type_list):
                    report_type_list.append(report_data)

    # Remove duplicates from report_type_list based on the "value" key
    seen = set()
    unique_report_type_list = []
    for report_type in report_type_list:
        if report_type["value"] not in seen:
            unique_report_type_list.append(report_type)
            seen.add(report_type["value"])
    return sorted(unique_report_type_list, key=lambda x: x["value"].lower())

def fetch_report_type_list_suggested_prompt(gco_report, group: str = None):
    """
    Fetches a unique and sorted report list based on the group and gco_report.
    If no group is provided, fetches all reports across all groups.

    Args:
        gco_report (list): The JSON data containing report information.
        group (str, optional): The group name to filter reports. Defaults to None.

    Returns:
        list: A list of dictionaries with unique and sorted report details.
    """
    if gco_report is None or not isinstance(gco_report, list):
        return []
    
    report_list = []
    for report in gco_report:
        if not group or report.get("group_name") == group:  # Include all groups if group is not provided
            report_data = {
                "value": report.get("report_type"),
                "notes": report.get("report_desc"),
                "type": "report_type"
            }
            if not any(item["value"] == report_data["value"] for item in report_list):
                report_list.append(report_data)

    # Remove duplicates and sort the report list by value
    seen = set()
    unique_report_list = []
    for report in report_list:
        identifier = report["value"]
        if identifier not in seen:
            unique_report_list.append(report)
            seen.add(identifier)

    return sorted(unique_report_list, key=lambda x: x["value"].lower())

def parse_publish_date(user_date: str, available_dates: List[str] = None) -> List[str]:
    """
    Parses a user-provided date in any format and converts it to mm/dd/yyyy.
    Returns all matching dates based on the given date and month if the year is not provided,
    or the given month and year if the date is not provided.

    Args:
        user_date (str): The user-provided date string.
        available_dates (List[str], optional): A list of available dates in mm/dd/yyyy format.

    Returns:
        List[str]: A list of dates in mm/dd/yyyy format.

    Raises:
        ValueError: If the date cannot be parsed or no matching dates are found.
    """
    if not available_dates:
        return []

    # Sort available dates in descending order
    available_dates = sorted(available_dates, key=lambda x: datetime.strptime(x, "%m/%d/%Y"), reverse=True)

    user_date_lower = user_date.lower()
    # Clean user_date for parsing
    user_date_cleaned = user_date.lower().replace("th", "").replace("st", "").replace("nd", "").replace("rd", "")

    # Handle specific date formats
    date_formats = [
        "%d %B %Y", "%d %b %Y",  # Full and abbreviated month formats with year
        "%d %B", "%d %b", "%B %Y", "%b %Y", "%B %d", "%b %d"  # Without year
    ]
    for date_format in date_formats:
        try:
            parsed_date = datetime.strptime(user_date_cleaned.strip(), date_format)
            day = parsed_date.day if "%d" in date_format else None
            month = parsed_date.month
            year = parsed_date.year if "%Y" in date_format else None

            # Return all matching dates
            return [
                date for date in available_dates
                if (day is None or datetime.strptime(date, "%m/%d/%Y").day == day) and
                   (datetime.strptime(date, "%m/%d/%Y").month == month) and
                   (year is None or datetime.strptime(date, "%m/%d/%Y").year == year)
            ]
        except ValueError:
            continue

    # Handle relative dates
    relative_dates = {
        "recent date": 0,
        "most recent date": 0,
        "recent": 0,
        "most recent": 0,
        "recent one": 0,
        "most recent one": 0,
        "second most recent": 1,
        "third most recent": 2,
        "today": 0,
        "yesterday": -1
    }
    if user_date_lower in relative_dates:
        if user_date_lower in ["today", "yesterday"]:
            target_date = (datetime.now() - timedelta(days=-relative_dates[user_date_lower])).strftime("%m/%d/%Y")
            return [target_date] if target_date in available_dates else []
        return [available_dates[relative_dates[user_date_lower]]] if len(available_dates) > relative_dates[user_date_lower] else []

    # Handle "current month" and "last month"
    if user_date_lower in ["current month", "last month"]:
        target_month = datetime.now().month if user_date_lower == "current month" else (datetime.now().replace(day=1) - timedelta(days=1)).month
        return [
            date for date in available_dates
            if datetime.strptime(date, "%m/%d/%Y").month == target_month
        ]

    # Handle years written as "25" for "2025" in full dates like "1/7/25"
    if "/" in user_date:
        parts = user_date.split("/")
        if len(parts) == 3 and len(parts[2]) == 2:  # Check if the year part is 2 digits
            current_year_prefix = str(datetime.now().year)[:2]  # Get the first two digits of the current year
            parts[2] = current_year_prefix + parts[2]  # Convert "25" to "2025"
            user_date = "/".join(parts)

    # Check if the user provided a full date
    date_formats = [
        "%m/%d/%Y", "%d/%m/%Y", "%Y-%m-%d", "%d-%m-%Y", "%m-%d-%Y",
        "%B %d, %Y", "%d %B %Y", "%b %d, %Y", "%d %b %Y", "%m/%d/%y"
    ]
    for date_format in date_formats:
        try:
            parsed_date = datetime.strptime(user_date, date_format)
            return [parsed_date.strftime("%m/%d/%Y")]
        except ValueError:
            continue

    # Check if the user provided only a month
    user_date_lower = user_date.lower()
    months = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"]
    for month_idx, month in enumerate(months, start=1):
        if (
            month.lower() == user_date_lower or 
            month[:3].lower() == user_date_lower or 
            f"{month_idx}st month" == user_date_lower or 
            f"{month_idx}nd month" == user_date_lower or 
            f"{month_idx}rd month" == user_date_lower or 
            f"{month_idx}th month" == user_date_lower or 
            f"month {month_idx}" == user_date_lower
        ):
            matching_dates = [
                date for date in available_dates
                if datetime.strptime(date, "%m/%d/%Y").month == month_idx
            ]
            if matching_dates:
                return matching_dates
            else:
                return []

    # Check if the user provided only a year or relative year (e.g., "current year", "last year")
    if user_date.isdigit() and len(user_date) == 2:
        current_year_prefix = str(datetime.now().year)[:2]  # Get the first two digits of the current year
        user_date = current_year_prefix + user_date  # Convert "25" to "2025"
    current_year = datetime.now().year
    if user_date.isdigit() and len(user_date) == 4:
        year = int(user_date)
    elif user_date_lower == "current year":
        year = current_year
    elif user_date_lower == "last year":
        year = current_year - 1
    else:
        year = None

    if year:
        matching_dates = [
            date for date in available_dates
            if datetime.strptime(date, "%m/%d/%Y").year == year
        ]
        if matching_dates:
            return matching_dates
        else:
            return []
        # Handle "today" and "yesterday"
    if user_date.lower() == "today":
        today = datetime.now().strftime("%m/%d/%Y")
        return [today] if today in available_dates else []
    elif user_date.lower() == "yesterday":
        yesterday = (datetime.now() - timedelta(days=1)).strftime("%m/%d/%Y")
        return [yesterday] if yesterday in available_dates else []

    # Handle "current month" and "last month"
    if user_date.lower() == "current month":
        current_month = datetime.now().month
        matching_dates = [
            date for date in available_dates
            if datetime.strptime(date, "%m/%d/%Y").month == current_month
        ]
        if matching_dates:
            return matching_dates
        else:
            return []
    elif user_date.lower() == "last month":
        last_month = (datetime.now().replace(day=1) - timedelta(days=1)).month
        matching_dates = [
            date for date in available_dates
            if datetime.strptime(date, "%m/%d/%Y").month == last_month
        ]
        if matching_dates:
            return matching_dates
        else:
            return []
 
def fetch_date_list(gco_report, group: str, report_type: str, publish_date: str = None):
    """
    Fetches the list of report dates for a given group and report type from the gco_report JSON file.
    If a publish_date is provided, filters the report dates based on the parsed publish date.

    Args:
        gco_report (list): The JSON data containing report information.
        group (str): The group name to filter reports.
        report_type (str): The report type to filter reports.
        publish_date (str, optional): The user-provided publish date to filter reports. Defaults to None.

    Returns:
        list: A list of dictionaries with report dates and their recent status.
    """
    if gco_report is None or not isinstance(gco_report, list):
        return []
    
    report_dates_list = []
    available_dates = []

    # Collect all available dates for the given group and report type
    for entry in gco_report:
        if entry.get("group_name") == group:
            for report in entry.get("reports", []):
                if report.get("report_type") == report_type:
                    available_dates.append(report.get("published_at", ""))
                    report_data = {
                        "value": report.get("published_at", ""),
                        "isrecent": 0,
                        "type": "date",
                    }
                    if not any(item["value"] == report_data["value"] for item in report_dates_list):
                        report_dates_list.append(report_data)

    # If a publish_date is provided, filter the report dates based on the parsed publish date
    if publish_date:
        parsed_dates = parse_publish_date(publish_date, available_dates)
        if not parsed_dates:
            report_dates_list = []  # If no matching dates found, return an empty list
        else:
            report_dates_list = [
                report_date for report_date in report_dates_list
                if report_date["value"] in parsed_dates
            ]

    # Sort by date in descending order
    report_dates_list.sort(key=lambda x: datetime.strptime(x["value"], "%m/%d/%Y"), reverse=True)

    # Mark the most recent one
    if len(report_dates_list) > 1:
        report_dates_list[0]["isrecent"] = 1

    # Remove duplicates from report_dates_list based on the "value" key
    seen_dates = set()
    unique_report_dates_list = []
    if len(report_dates_list) > 0:
        for report_date in report_dates_list:
            if report_date["value"] not in seen_dates:
                unique_report_dates_list.append(report_date)
                seen_dates.add(report_date["value"])

    return unique_report_dates_list


def fetch_file_name(gco_report, group: str, report_type: str, publish_date: str):
    """
    Fetches the report link for a given group, report type, and date from the gco_report JSON file.
    Returns a list of dictionaries with all required fields for all matching publish dates, removing duplicates.
    """
    if gco_report is None or not isinstance(gco_report, list):
        return []

    available_dates = [
        report.get("published_at", "")
        for entry in gco_report if entry.get("group_name") == group
        for report in entry.get("reports", [])
        if report.get("report_type") == report_type
    ]
    parsed_dates = parse_publish_date(publish_date, available_dates)  # Handle specific months, years, or exact dates
    if not parsed_dates:
        return []
    report_details = []
    for entry in gco_report:
        if entry.get("group_name") == group:
            for report in entry.get("reports", []):
                if (
                    report.get("report_type") == report_type
                    and report.get("published_at") in parsed_dates
                ):
                    file_name = report.get("file_name", "")  # Check if file_name exists
                    if file_name:
                        report_details.append({
                            "group_id": entry.get("group_id", ""),
                            "group_name": entry.get("group_name", ""),
                            "file_name": file_name,
                            "report_type": report.get("report_type", ""),
                            "published_at": report.get("published_at", ""),
                            "file_path": report.get("file_path", ""),
                            "funding_type": report.get("funding_type", ""),
                            "revenue_arrangement": report.get("revenue_arrangement", ""),
                            "subscriber_count": report.get("subscriber_count", ""),
                            "platform_id": report.get("platform_id", ""),
                            "session_id": report.get("session_id", ""),
                            "current_timestamp": report.get("current_timestamp", ""),
                            "report_link": report.get("report_link", ""),
                            "report_desc": report.get("report_desc", "")
                        })

    return report_details
 



def get_downloaded_report_details(gco_report, group: str = None, report_type: str = None, num_reports: int = 5):
    """
    Fetches a list of report details sorted by the count of downloads in descending order based on the provided filters (group and report type).
    Only includes reports where the download count is greater than 0.

    Args:
        gco_report (list): The JSON data containing report information.
        group (str, optional): The group name to filter reports. Defaults to None.
        report_type (str, optional): The type of report to filter. Defaults to None.
        num_reports (int, optional): The number of reports to return. Defaults to 5.

    Returns:
        list: A list of dictionaries containing report details matching the criteria.
    """

    if gco_report is None or not isinstance(gco_report, list):
        return []
    
    report_details = []

    for report in gco_report:
        if group and report.get("group_name", "") != group:
            continue  # Skip if group filter is provided and doesn't match

        if (
            (not report_type or report.get("report_type", "") == report_type)  # Match report_type if provided
            and report.get("download_count", 0) > 0  # Include only reports with download count > 0
        ):
            report_details.append({
                "group_id": report.get("group_id", ""),
                "group_name": report.get("group_name", ""),
                "file_name": report.get("file_name", ""),
                "report_type": report.get("report_type", ""),
                "published_at": report.get("published_at", ""),
                "file_path": report.get("file_path", ""),
                "funding_type": report.get("funding_type", ""),
                "revenue_arrangement": report.get("revenue_arrangement", ""),
                "subscriber_count": report.get("subscriber_count", ""),
                "platform_id": report.get("platform_id", ""),
                "session_id": report.get("session_id", ""),
                "current_timestamp": report.get("current_timestamp", ""),
                "report_link": report.get("report_link", ""),
                "report_desc": report.get("report_desc", ""),
                "download_count": report.get("download_count", 0)
            })

    # Sort reports by download count in descending order and limit to num_reports
    sorted_reports = sorted(report_details, key=lambda x: x["download_count"], reverse=True)
    return sorted_reports[:num_reports] if sorted_reports else None

def get_recent_report_details(gco_report, group: str = None, report_type: str = None, num_reports: int = None):
    """
    Fetches a list of report details based on the provided filters (group and report type).
    Args:
        gco_report (list): The JSON data containing report information.
        group (str, optional): The group name to filter reports. Defaults to None.
        report_type (str, optional): The type of report to filter. Defaults to None.
        num_reports (int, optional): The number of reports to return. Defaults to None (returns all matching reports).

    Returns:
        list: A list of dictionaries containing report details matching the criteria.
    """
    if gco_report is None or not isinstance(gco_report, list):
        return [] 
    
    report_details = []

    for report in gco_report:
        if group and report.get("group_name", "") != group:
            continue  # Skip if group filter is provided and doesn't match

        if not report_type or report.get("report_type", "") == report_type:  # Match report_type if provided
            timestamp_str = report.get("downloaded_on", "")
            try:
                if timestamp_str is not None:
                    report_details.append({
                        "group_id": report.get("group_id", ""),
                        "group_name": report.get("group_name", ""),
                        "file_name": report.get("file_name", ""),
                        "report_type": report.get("report_type", ""),
                        "published_at": report.get("published_at", ""),
                        "file_path": report.get("file_path", ""),
                        "funding_type": report.get("funding_type", ""),
                        "revenue_arrangement": report.get("revenue_arrangement", ""),
                        "subscriber_count": report.get("subscriber_count", ""),
                        "platform_id": report.get("platform_id", ""),
                        "session_id": report.get("session_id", ""),
                        "current_timestamp": report.get("current_timestamp", ""),
                        "report_link": report.get("download_link", ""),
                        "report_desc": report.get("report_desc", ""),
                        "updated_timestamp": report.get("downloaded_on", "")
                    })
            except ValueError:
                print(f"Skipping report with invalid timestamp format: {timestamp_str}")

    # Limit the number of reports if num_reports is specified
    return report_details[:num_reports] if num_reports else report_details
 

def get_npr_ndr_report_details(gco_report, group: str = None, report_type: str = None):
    """
    Fetches a list of report details based on the provided filters (group, report type, and last login).
    If only last login is provided, fetches all reports published after the last login date.

    Args:
        gco_report (list): The JSON data containing report information.
        group (str, optional): The group name to filter reports. Defaults to None.
        report_type (str, optional): The type of report to filter. Defaults to None.
        last_login (str, optional): The last login date in string format. Defaults to None.

    Returns:
        list: A list of dictionaries containing report details matching the criteria.
    """
    if gco_report is None or not isinstance(gco_report, list):
        return []
    
    report_details = []
    for entry in gco_report:
        if group and entry.get("group_name") != group:
            continue  # Skip if group filter is provided and doesn't match
        for report in entry.get("reports", []):
            if not report_type or report.get("report_type") == report_type:  # Match report_type if provided
                report_details.append({
                    "group_id": entry.get("group_id", ""),
                    "group_name": entry.get("group_name", ""),
                    "file_name": report.get("file_name", ""),
                    "report_type": report.get("report_type", ""),
                    "published_at": report.get("published_at", ""),
                    "file_path": report.get("file_path", ""),
                    "funding_type": report.get("funding_type", ""),
                    "revenue_arrangement": report.get("revenue_arrangement", ""),
                    "subscriber_count": report.get("subscriber_count", ""),
                    "platform_id": report.get("platform_id", ""),
                    "session_id": report.get("session_id", ""),
                    "current_timestamp": report.get("current_timestamp", ""),
                    "report_link": report.get("report_link", ""),
                    "report_desc": report.get("report_desc", "")
                })

    return report_details if report_details else None

def keywords_cer_ticket_guide(query: str):
    """
    Checks if the query contains keywords related to CER, ticket, or feedback.
    Args:
        query (str): The user query to check for keywords.
    
    Returns:
        str: Returns 'cer' if CER-related keywords are found, 'ticket' if ticket-related keywords are found,
             'feedback' if feedback-related keywords are found, or None if no keywords match.
    """
    keywords_cer = ["cer","pvc", "pvci", "mbm", "pvc vs claims", "pvc versus claims", "pvc and claims", "claims vs pvc", "claim lag study",
    "claims expenses by size of payment", "cost and utilization by procedure", "cost and utilization summary report",
    "detail payment report", "distribution of discounts", "distribution of ineligible charges", "distribution of other savings",
    "health care cost management summary", "inpatient utilization and cost by admission type", "inpatient utilization by diagnosis",
    "key generic substitution indicators by month", "large loss claim payments (ecr financial report)",
    "managed pharmacy cost and utilization by month", "managed pharmacy critical indicators",
    "managed pharmacy utilization by age group", "membership by market", "membership by month",
    "membership with demographic and geographic factor", "network utilization", "outpatient utilization by diagnosis",
    "payments by benefit type", "payments by month", "premium vs claim - summary",
    "top drug utilization ranked by total net paid", "top drug utilization ranked by volume",
    "top hospitals ranked-total net paid", "top physicians ranked-total net paid",
    "top therapeutic class utilization ranked by total net paid", "top therapeutic class utilization ranked by volume",
    "utilization and cost by provider type", "utilization by age group", "utilization by diagnosis"]
    
    keywords_ticket = ["submit ticket", "ticket", "ticket submit", "feedback form", "feedback", "form"]
    keywords_guide = ["first time user", "reporting guide", "reporting user guide", "reporting instructions", "reporting support"]
    if any(keyword in query.lower() for keyword in keywords_cer):
        return "cer"
    elif any(keyword in query.lower() for keyword in keywords_ticket):
        return "ticket"
    elif any(keyword in query.lower() for keyword in keywords_guide):
        return "reporting_guide"
    else:
        return None
   


def body_wrapper(state: dict, report: dict, action_type: str = "download", incident_number: str = "", feedback_desc: str = "", feedback_resp: str = "") -> dict:
    """
    Generates a body dictionary with the specified format.

    Args:
        state (dict): The state dictionary containing user information.
        report (dict): The report dictionary containing report details.
        action_type (str): The action type (default is "download").
        incident_number (str): The incident number (default is an empty string).
        feedback_desc (str): Feedback description (default is an empty string).
        feedback_resp (str): Feedback response (default is an empty string).

    Returns:
        dict: The formatted body dictionary.
    """
    body = {
        "user_id": state.get("user_info", {}).get("uuid", ""),
        "user_type": state.get('additional_arg', {}).get("bne_user_type", "External"),
        "user_access_role": state.get("user_info", {}).get("user_access_role", ""),
        "user_ext_type": state.get("user_info", {}).get("user_ext_type", ""),
        "user_name": state.get("user_info", {}).get("user_name", ""),
        "user_email": state.get("user_info", {}).get("user_email", ""),
        "action_type": action_type,
        "incident_number": incident_number,
        "feedback": {
            "feedback_desc": feedback_desc,
            "feedback_resp": feedback_resp
        },
        "group_id": report.get("group_id", ""),
        "group_name": report.get("group_name", ""),
        "file_name": report.get("file_name", ""),
        "report_type": report.get("report_type", ""),
        "report_desc": report.get("report_desc", ""),
        "published_at": report.get("published_at", ""),
        "file_path": report.get("file_path", ""),
        "funding_type": report.get("funding_type", ""),
        "revenue_arrangement": report.get("revenue_arrangement", ""),
        "subscriber_count": report.get("subscriber_count", ""),
        "platform_id": report.get("platform_id", ""),
        "session_id": state.get("user_info", {}).get("session_id", ""),
    }
    return body

