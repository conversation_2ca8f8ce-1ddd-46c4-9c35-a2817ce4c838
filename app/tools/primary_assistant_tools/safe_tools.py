from langchain_core.tools import tool
from langgraph.prebuilt.tool_node import InjectedState
from typing import Annotated
from config.db import db_onboarding
import random

async def map_tools_permission(client_id):
    if client_id == 'samx_one':
        active_cap = ['extract_structure_from_pdf']
        return active_cap
    collection = db_onboarding["ClientPermissions"]
    document = await collection.find_one({"clientId":client_id}) 
    if document:
        # router_tools = document.get('agentRouter', [])
        active_cap = [tool.get('toolDisplayName') for tool in document.get('tools', []) if 'toolDisplayName' in tool]
        return active_cap
    else: 
        raise Exception("ClientId doesn't match.")
    
@tool
async def activeCapabilities(state: Annotated[dict, InjectedState]):
    """Always call this tool whenever the user wants to inquire anything related to capabilities you support,
    what are you capable of; how can you help ;supported capabilities; what you can do; what is supported or similar queries to understand the capabilities of this asistant."""
    user = state.get("user_info")
    clientId = user.get("client_id")
    active_capabilities = await map_tools_permission(clientId)
    messages = [  
    f"Hi, I have {', '.join(map(str, active_capabilities))} as the support for your platform.",  
    f"Hello! Your platform supports {', '.join(map(str, active_capabilities))}.",  
    f"Greetings! The support available for your platform includes {', '.join(map(str, active_capabilities))}.",  
    f"Hi there! The platform provides support for {', '.join(map(str, active_capabilities))}.",  
    f"Hey! {', '.join(map(str, active_capabilities))} are supported on your platform.",  
    f"Hello! We currently offer support for {', '.join(map(str, active_capabilities))} on your platform.",  
    f"Hi, just letting you know that {', '.join(map(str, active_capabilities))} are supported on your platform.",  
    f"Greetings! The platform is equipped to handle {', '.join(map(str, active_capabilities))} for your convenience.",  
    f"Hey there! Your platform includes support for both {', '.join(map(str, active_capabilities))}.",  
    f"Hi! {', '.join(map(str, active_capabilities))} are the types of support available for your platform.",  
    f"Hello! {', '.join(map(str, active_capabilities))} are here to help you with your platform support needs.",  
    f"Hi! We’re happy to let you know that {', '.join(map(str, active_capabilities))} are supported features.",  
    f"Hey! {', '.join(map(str, active_capabilities))} are part of the support provided for your platform.",  
    f"Hello! Rest assured, the platform is designed to support both {', '.join(map(str, active_capabilities))}.",  
    f"Hi, I just wanted to mention that {', '.join(map(str, active_capabilities))} are supported on your platform.",  
    f"Hey there! {', '.join(map(str, active_capabilities))} are the support options available for your platform.",  
    f"Hello! You can rely on {', '.join(map(str, active_capabilities))} as part of your platform support.",  
    f"Hi there! We support {', '.join(map(str, active_capabilities))} to ensure the best experience for your platform.",  
    f"Greetings! With {', '.join(map(str, active_capabilities))}, your platform's support system is complete.",  
    f"Hey! Your platform is equipped with {', '.join(map(str, active_capabilities))} to assist you effectively."  
]  

    random_message = random.choice(messages) 

    return random_message