from client_plugins.bneportal.compensation.utils import check_pcis_id_authorization, validate_cust_crid, get_followup_questions, get_customer_validation_results, get_logger,post_with_cert
import requests
from typing import Annotated
from langgraph.prebuilt.tool_node import InjectedState
import json
import os
from config.openAI import json_model
from langchain_core.tools import tool
from utils.tool_utils import log_tool_error
from datetime import datetime 
import traceback

TRY_ANOTHER_PROMPTS = "View Compensation Prompts"
logger = get_logger(__name__)

@tool
async def compensation_details(state: Annotated[dict, InjectedState], pcis_input: str = None, option_type: str = "transactions", customerId: str = None, 
                         heldInd: str = None, issueInd: str = None, segment: str = None, 
                         status: str = None, payPeriodEndDate: str = None, coverageType: str = None, 
                         compType: str = None, revPeriodStart: str = None, 
                         revPeriodEnd: str = None) -> dict: 
    """
    Use this tool ONLY if the user asks for compensation details transactions or compensation details summary. Do NOT use this tool for commission rates or commission statments.
    1. This tool will retrieve compensation details based on the given ID.
    2. Never prompt the user to provide a PCIS ID. Only if the user has explicitly provided/mentioned a PCIS ID/pcis id in the input without being asked, assign that value to the pcis_input variable. Otherwise, don't do anything.
    3. If the user has explicitly provided a Customer ID in previous message(s) in the chat history, use the most recent Customer ID provided, convert to string, and assign it to the customerId parameter.
    4. If the user enters an ID and does not specify what kind of ID it is, then you must ask the user to specify if Customer ID. Dont just assume what type of ID it is.
    5. Only if multiple ids are provided, then ask the user to specify which is the is a Customer ID if not already specified.
    6. DO NOT ask the user if they want to view all transactions or compensation summary. Always default to "transactions" if the user has not specified. Do not mention to user that it will defaulted to transactions. Only if the user has explicitly asked for "summary", then assign "summary" to the option_type parameter. 
    """ 

    user_type, authorized, pcis_id = check_pcis_id_authorization(state, pcis_input)

    logger.info(f"pcis_id: {pcis_id}, authorized: {authorized}, user_type: {user_type}")

    if not authorized and pcis_id == "no_pcis":
        no_pcis_message = "You have not provided a PCIS ID. Please provide a valid PCIS ID to view Compensation Details."
        return {
            "answer": no_pcis_message,
            "follow_up_questions": [TRY_ANOTHER_PROMPTS]
        }
    elif not authorized:
        no_pcis_auth_message = "You do not have authorization to view Compensation Details for provided PCIS ID. "
        return {
            "answer": no_pcis_auth_message,
            "follow_up_questions": [TRY_ANOTHER_PROMPTS]
        }
    elif customerId is None:
        no_customer_message = "Please provide a Customer ID to view Compensation Details."
        return {
            "answer": no_customer_message,
            "follow_up_questions": ["Please provide a Customer ID to view Compensation Details. XYZ123",TRY_ANOTHER_PROMPTS]
        }
    else:
        validation_results = await get_customer_validation_results(state, customerId, pcis_id)
        logger.info(f"*****After Making Function*******: {validation_results}")

        if "answer" in validation_results:
            return validation_results
        elif validation_results.get("valid_association"):
            logger.info(f"Inside compensation_details tool function...CD, customer_id: {customerId}")
            api_url = os.getenv("COMPENSATION_DETAILS_ENDPOINT")

            # Convert segment to list if provided, otherwise use default
            segmentsList = [segment] if segment and segment.lower() != "all" else ["1", "2", "3"]

            headers = {  
                "Content-Type": "application/json"  
            }    
            payload = {
                "producerId": str(pcis_id),
                "producerName": "",
                "heldInd": str(heldInd) if heldInd else "All",
                "issueInd": str(issueInd) if issueInd else "All",
                "segments": segmentsList,
                "status": str(status) if status else "All",
                "payPeriodId": str(payPeriodEndDate) if payPeriodEndDate else "All",
                "customerNumber": str(customerId) if customerId is not None else "",
                "customerName": "",
                "contractNumber": "",
                "contractName": "",
                "coverageType": str(coverageType) if coverageType else "All",
                "compType": str(compType) if compType else "All",
                "revPeriodStart": str(revPeriodStart) if revPeriodStart else "",
                "revPeriodEnd": str(revPeriodEnd) if revPeriodEnd else "",
                "premiumPostStart": "",
                "premiumPostEnd": "",
                "rows": 10,
                "offset": 0,
                "rowLimit": 5000
            }

            try:  
                response = await post_with_cert(api_url, payload)
                if response.status_code == 200:  
                    compensation_details_data_response = response.json()  
                    logger.info(f"COMPENSATION DETAILS FETCHED: {compensation_details_data_response}")

                    data = compensation_details_data_response.get('payload', {}).get('data', {})
                    details_list = data.get('commListDetailList', [])

                    if data and details_list:
                        name = ""
                        number_id = ""
                        number_type = ""
                        total_records = 0
                        offset = ""
                        rows = ""
                        total_billed_premium = 0
                        total_paid_premium = 0
                        total_paid_compensation = 0 
                        total_held_compensation = 0

                        if payload.get("customerNumber"):
                            number_id = payload.get("customerNumber")
                            number_type = "Customer Number"
                        
                        if payload.get("customerNumber"):
                            name = details_list[0].get("customerName", "")

                        total_records = data.get('totalRecords')
                        offset = data.get('offset')
                        rows = data.get('rows')
                        total_billed_premium = data.get('mTotalBilledPremium', 0)
                        total_paid_premium = data.get('mTotalPaidPremium', 0)
                        total_paid_compensation = data.get('mTotalPaidCompensation', 0)
                        total_held_compensation = data.get('mTotalHeldCompensation', 0)

                        logger.info(f"data: {data}, DETAILS_LIST: {details_list}")

                        questions = [
                            "View Customer Details for Customer Number: xxxxx",
                            "Apply filters on multiple parameters for Compensation Details",
                            "View Compensation Summary",
                            TRY_ANOTHER_PROMPTS
                        ]

                        user_info = {
                            "type": user_type,
                            "pcis": pcis_id,
                        }
                        
                        if (option_type == "summary"):
                            summaryList = [
                                { "description": "Total Billed Premium", "value": total_billed_premium },
                                { "description": "Total Paid Premium", "value": total_paid_premium },
                                { "description": "Total Paid Compensation", "value": total_paid_compensation },
                                { "description": "Total Held Compensation", "value": total_held_compensation }
                            ]

                            compensation_summary_data = {
                                "pcis": payload.get("producerId"),
                                "name": name,
                                "numberId": number_id,
                                "numberType": number_type,
                                "summaryList": summaryList,
                            }

                            return {
                                "answer": {
                                    "client": "bne_client",
                                    "functionality": "compensation", 
                                    "response_type": "compensationSummary", 
                                    "userInfo": user_info,
                                    "compensationSummaryData": compensation_summary_data,
                                },
                                "follow_up_questions": questions,
                            }
                        elif (option_type == "transactions"): 
                            compensation_details_data = {
                                "pcis": payload.get("producerId"),
                                "name": name,
                                "numberId": number_id,
                                "numberType": number_type,
                                "totalRecords": total_records,
                                "offset": offset,
                                "rows": rows,
                                "totalBilledPremium": total_billed_premium,
                                "totalPaidPremium": total_paid_premium,
                                "totalPaidCompensation": total_paid_compensation,
                                "totalHeldCompensation": total_held_compensation,
                                "detailsList": details_list,
                            }

                            return {
                                "answer": {
                                    "client": "bne_client",
                                    "functionality": "compensation", 
                                    "response_type": "compensationDetails",
                                    "userInfo": user_info,
                                    "custValidationResult" : validation_results,
                                    "compensationDetailsData": compensation_details_data,
                                },
                                "follow_up_questions": questions,
                            }
                    else:
                        no_data_message = "No Compensation Details found for given inputs. Please check the Customer ID, or any other parameters provided."
                        return {
                            "answer": no_data_message,
                            "follow_up_questions": [TRY_ANOTHER_PROMPTS]
                        }
                else:  
                    resp_error_message = "I was unable to retrieve compensation details. Please try again or at a later time."
                    return {
                        "answer": resp_error_message,
                        "follow_up_questions": [TRY_ANOTHER_PROMPTS]
                    }  
            except Exception as e:
                await log_tool_error(state, traceback.format_exc(), "compensation_details")
                exception_message = "I am unable to retrieve compensation details at this time. Please try at a later time."
                return {
                    "answer": exception_message,
                    "follow_up_questions": [TRY_ANOTHER_PROMPTS]
                } 
        else:
            status_message = "Can't find valid association of PCIS ID and the Customer ID provided. Please try with different Customer ID"
            return {
                "answer": status_message,
                "follow_up_questions": ["Try with another Customer ID: ",TRY_ANOTHER_PROMPTS]
            }


@tool
def filter_compensation_details() :
    """ 
    This tool renders the micro frontend for filter selection. This will be called ONLY when the user asks to filter compensation details.
    After the user interacts with this filter interface, the system should:
    1. Capture all filter parameters selected by the user (pcis_id, Held Ind, Iss Ind, Status,etc.)
    2. Call the compensation_details tool with those parameters

    """
    return {
        "answer": {
            "client": "bne_client",
            "functionality": "compensation",
            "response_type": "filterCompensationDetails",
        },
        "follow_up_questions": [TRY_ANOTHER_PROMPTS],
    }

@tool
def view_prompts():
    """ This tools returns the list of prompts used in the compensation-related tools. 
    1. If the user asks to view a list of prompts or wants to see prompts available in compensation agent, this tool will be called.
    """
    return {
        "answer": {
            "client": "bne_client",
            "functionality": "compensation",
            "response_type": "viewPrompts"
        },
        "follow_up_questions": [],
    }