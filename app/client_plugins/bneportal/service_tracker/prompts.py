 
from langchain_core.prompts import ChatPromptTemplate
from graph.prompts_and_capabilities import master_prompt
from services.get_prompts_service import get_prompt_by_assistant_name

service_tracker_assistant_prompt_document = get_prompt_by_assistant_name("service_tracker")

if not service_tracker_assistant_prompt_document:
    raise ValueError("service_tracker Assistant Prompt Not Found")

assistant_prompt = service_tracker_assistant_prompt_document.get("assistantPrompt")

system_prompt = ("system", master_prompt + assistant_prompt)
service_tracker_system_prompt = ChatPromptTemplate.from_messages([system_prompt, ("placeholder", "{messages}")]).partial() 
