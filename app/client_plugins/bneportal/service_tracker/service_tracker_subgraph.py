
from client_plugins.bneportal.service_tracker.main import service_tracker_tool_node
from client_plugins.bneportal.service_tracker.main import service_tracker_runnable
from client_plugins.bneportal.service_tracker.router import route_service_tracker_assistant
from graph.nodes import Assistant
from langgraph.pregel import RetryPolicy
from graph.nodes import pop_dialog_state, escalation_fallback, invalid_tool

from langgraph.graph import START
from graph.state import State, get_postgres_checkpointer
from utils.subgraph_builder import BaseSubgraphBuilder
from utils.subgraph_registry import SubgraphRegistry

service_tracker_compiled_graph = None

async def init_service_tracker_graph():
    """
    Initialize the service_tracker graph using the BaseSubgraphBuilder pattern
    and register it with SubgraphRegistry.
    """
    global service_tracker_compiled_graph
    if service_tracker_compiled_graph is None:
        # Create a new builder with fluent interface
        builder = BaseSubgraphBuilder(State)
        
        # Add nodes
        builder.add_node("service_tracker_assistant", Assistant(service_tracker_runnable), retry=RetryPolicy(max_attempts=2))
        builder.add_node("call_service_tracker_tool", service_tracker_tool_node)
        builder.add_node("leave_skill", pop_dialog_state)
        builder.add_node("escalation_fallback", escalation_fallback)
        builder.add_node("tool_disabled_handler", invalid_tool)
        
        # Add edges
        builder.add_edge("leave_skill", "service_tracker_assistant")
        builder.add_edge("escalation_fallback", "service_tracker_assistant")
        builder.add_edge(START, "service_tracker_assistant")
        
        # Add conditional edges
        builder.add_conditional_edges("service_tracker_assistant", route_service_tracker_assistant)
        
        # Compile with async checkpointer
        service_tracker_compiled_graph = builder.compile(checkpointer=await get_postgres_checkpointer())
        
        # Register with SubgraphRegistry for future access
        SubgraphRegistry.register("service_tracker", service_tracker_compiled_graph)
