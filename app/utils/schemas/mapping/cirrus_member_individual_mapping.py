def get_nested(data, keys, default=None):  
    for key in keys:  
        if isinstance(data, list):  
            data = data[0] if data else default  
        if isinstance(data, dict):  
            data = data.get(key, default)  
        else:  
            return default  
    return data
  
def map_member_individual_json(input_json):
    
    product_map = {"MD": "Medical", "VS": "Vision", "DN": "Dental", "AD": "AD&D", "LI": "Life"}

    output_json = {
        "personalInformation": {},
        "contactInformation": {},  
        "memberCoverages": []  
    }

    # Personal Information
    output_json["personalInformation"]["firstName"] = get_nested(input_json, ["responseData", "members", "demographics", "nameFirst"]) 
    output_json["personalInformation"]["middleName"] = get_nested(input_json, ["responseData", "members", "demographics", "nameMiddle"])
    output_json["personalInformation"]["lastName"] = get_nested(input_json, ["responseData", "members", "demographics", "nameLast"])
    output_json["personalInformation"]["suffix"] = get_nested(input_json, ["responseData", "members", "demographics", "nameSuffix"])
    ssn = get_nested(input_json, ["responseData", "members", "demographics", "socialSecurityNumber"])
    output_json["personalInformation"]["socialSecurityNumber"] = ssn[0:3] + '-' + ssn[3:5] + '-' + ssn[5:] if ssn else None
    output_json["personalInformation"]["reasonSsnNotProvided"] = get_nested(input_json, ["responseData", "members", "demographics", "ssnNotProvidedReasonType"])
    output_json["personalInformation"]["gender"] = get_nested(input_json, ["responseData", "members", "demographics", "gender"])
    output_json["personalInformation"]["birthDate"] = get_nested(input_json, ["responseData", "members", "demographics", "birthDate"])
    output_json["personalInformation"]["dateOfHire"] = next((item["employBeginDate"] for item in get_nested(input_json, ["responseData", "members", "memberAffiliation", "subsJobList"], [])), None)
    output_json["personalInformation"]["dateOfQualifyingEvent"] = next((item["qualifyingEventDate"] for item in get_nested(input_json, ["responseData", "members", "memberCoverageList"], [])), None)
    memberDirectBillingInd = next((item["memberDirectBillingInd"] for item in get_nested(input_json, ["responseData", "members", "memberCoverageList", "memberBenefitStatusList"], [])), None)
    output_json["personalInformation"]["directBillIndicator"] = "Yes" if memberDirectBillingInd == True else "No"
    output_json["personalInformation"]["continuation"] = None
    output_json["personalInformation"]["memberEffectiveDate"] = next((item["effectiveDate"] for item in get_nested(input_json, ["responseData", "members", "memberCoverageList", "memberBenefitStatusList"], [])), None)
  
    # Contact Information  
    output_json["contactInformation"]["homeAddresssLine1"] = get_nested(input_json, ["responseData", "members", "addressList", "address1"])  
    output_json["contactInformation"]["homeAddresssLine2"] = get_nested(input_json, ["responseData", "members", "addressList", "address3"])  
    output_json["contactInformation"]["homeZipCode"] = get_nested(input_json, ["responseData", "members", "addressList", "postalCode"])  
    output_json["contactInformation"]["homeCity"] = get_nested(input_json, ["responseData", "members", "addressList", "city"])  
    output_json["contactInformation"]["homeState"] = get_nested(input_json, ["responseData", "members", "addressList", "state"])  
    output_json["contactInformation"]["homePhone"] = None  
    output_json["contactInformation"]["workPhone"] = None  
    output_json["contactInformation"]["mobilePhone"] = None  
    output_json["contactInformation"]["emailAddress"] = next((item["communicationText"] for item in get_nested(input_json, ["responseData", "members", "ecommList"], []) if item.get("communicationType") == "EML"), None)  
    output_json["contactInformation"]["preferredPhone"] = None  
      
    # Member Coverages  
    for item in get_nested(input_json, ["responseData", "members", "memberCoverageList"], []):
        coverageObject = {}
        for product in ["MD", "VS", "DN", "AD", "LI"]:
            if (item.get("coverageOptionType") == product):
                coverageObject["product"] = product_map[product]
                coverageObject["planName"] = f"{item['coverageOptionID']}" if item.get('coverageOptionID') else ""
                if item.get('coverageOptionDescription'):
                    coverageObject["planName"] += f" - {item['coverageOptionDescription']}" if item.get('coverageOptionID') else f"{item['coverageOptionDescription']}"
                coverageObject["coveragePeriod"] = f"{item['benPlanEffDate']}" if item.get('benPlanEffDate') else ""
                if item.get('benPlanExpDate'):
                    coverageObject["coveragePeriod"] += f" - {item['benPlanExpDate']}"
                coverageObject["billingGroup"] = next((f"{item['billGroupReferenceID']} - {item['billGroupDescription']}" for item in get_nested(input_json, ["responseData", "members", "memberAffiliation", "billGroupList"], []) if item.get("coverageOptionType") == product), None)
                break
        if coverageObject != {}:
            output_json["memberCoverages"].append(coverageObject) 
    return output_json
