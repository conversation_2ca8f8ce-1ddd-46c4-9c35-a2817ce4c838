from utils.schemas.mapping.cirrus_contract_options_mapping import map_plan_and_bill_group_lists, map_contract_options_response, get_nested_value
from services.cirrus.cirrus_api_definitions import contract_options_api
from utils.helpers.mappings import PRODUCTS_MAPPING
from utils.schemas.cirrus_classes import SimilarPlanList
from config.openAI import model

def check_plans(final_response, resp_json):
    for key, value in final_response["coverageDetails"].items():
        if len(value) > 0 and key != "billGroupList":
            return final_response

    contract_option_list = []        

    for contract in resp_json.get('memberGroup', {}).get('contractList', []):
        for contract_option in contract.get('contractOptionsList', []):
            coverage_type = get_nested_value(contract_option, ['contractOptType'])
            if coverage_type != "AD" and coverage_type != "SL" and coverage_type != "SA":
                contract_option_effective_date = get_nested_value(contract_option, ['contractOptionEffectiveDate'])
                contract_option_expiration_date = get_nested_value(contract_option, ['contractOptionExpirationDate'])
                contract_option_list.append({"coverageType": PRODUCTS_MAPPING[coverage_type], "effectiveDate": contract_option_effective_date, "expirationDate": contract_option_expiration_date})

    if len(contract_option_list) == 0:
        return "There are no contract options available for the selected Cirrus group."

    return {"contractOptionList": contract_option_list, "chatRespMsg": "The provided effective date is not within the contract option effective date ranges for the selected Cirrus group. The contract option effective date ranges for this group are as follows:"}

async def get_contract_options(memberGroupID, uuid, user_name, session_id, request_id, client_id, medicalPlanBillGroupFlag=False, effective_date=''):
    
    if not memberGroupID or memberGroupID.isspace():
        return f"{memberGroupID} is not a valid ID. Please provide a valid Cirrus group ID."
    
    if len(memberGroupID) != 7 and len(memberGroupID) != 8:
        return f"{memberGroupID} is not a valid ID. A valid Cirrus ID has either seven or eight digits."
    
    response = await contract_options_api(memberGroupID, uuid, user_name, session_id, request_id, client_id)

    if (isinstance(response.get('getEmployerGroupContractOptionResponse'), dict) and isinstance(response.get('getEmployerGroupContractOptionResponse').get('memberGroup'), dict)):
        resp_json = response['getEmployerGroupContractOptionResponse']
        if medicalPlanBillGroupFlag:
            plan_and_bill_group = map_plan_and_bill_group_lists(resp_json, effective_date)
            return check_plans(plan_and_bill_group, resp_json)
        else:
            return map_contract_options_response(resp_json)
    
    elif isinstance(response.get('getEmployerGroupContractOptionResponse'), dict): 
        error_info = response['getEmployerGroupContractOptionResponse'].get('errors')
        if error_info:
            for error in error_info:
                code = error.get('code')
                if code == 404:
                    return f'A Cirrus group with the selected Member group ID: {memberGroupID} was not found.'
    
    return f'Error: No data found for Member group ID: {memberGroupID}.'

async def find_similar_plan_names(contract_options_response, plan_names):
    group_plan_dict = {}
    for plan_list in contract_options_response['coverageDetails'].items():
        if len(plan_list[1]) > 0 and 'PlansList' in plan_list[0]:
            for plan in plan_list[1]:
                if plan_list[0] not in group_plan_dict.keys():
                    group_plan_dict[plan_list[0]] = [plan['coverageOptionID'] + ' - ' + plan['planName']]
                else:
                    group_plan_dict[plan_list[0]].append(plan['coverageOptionID'] + ' - ' + plan['planName'])

    prompt = f"""
    This is the list of available plans for the group which the user wishes to enroll a member: {group_plan_dict}
    This is the list of desired plans extracted from the enrollment PDF or entered by the user during pre-installation: {plan_names}
    For any plan names that are similar across both lists, create an object containing the plan name (taken from {group_plan_dict}) and its coverage type (medical, dental, vision, etc.)
    The plan names need not match exactly, but do not create an object unless their names are very similar. Do not confuse coverage types (medical, dental, vision, etc.) with plan names.
    Do not hallucinate or make up data. Only provide plan names that are in the lists you are given.
    """
    
    llm_str = model.with_structured_output(SimilarPlanList)
    resp = await llm_str.ainvoke(prompt)
    return resp
