
from client_plugins.bneportal.compensation.tool_compensation import compensation_details, filter_compensation_details, view_prompts
from client_plugins.bneportal.compensation.tool_broker import producer_details, customer_search_by_name
from client_plugins.bneportal.compensation.tool_customer import customer_details, customer_override, manual_holds
from client_plugins.bneportal.compensation.tool_sfa import serviceFeeAgreement, filterServiceFeeAgreement, sfa_summary
from client_plugins.bneportal.compensation.tool_commission import commission_statements, commission_rates, filter_commission_statements

from graph.state import State
from graph.nodes import Assistant
from config.openAI import model
from langgraph.graph import END, StateGraph, START
from langgraph.prebuilt import tools_condition

from langchain_core.runnables import <PERSON>na<PERSON>Lamb<PERSON>
from langgraph.prebuilt import ToolNode
from tools.common_tools import CompleteOrEscalate
from utils.helpers.constants import TOOL_CALL_ERROR_MESSAGE
from client_plugins.bneportal.compensation.prompts import compensation_system_prompt
# Import the other required modules


compensation_tools = [compensation_details, commission_statements, commission_rates, filter_commission_statements, filter_compensation_details, customer_details, customer_override, manual_holds, serviceFeeAgreement, filterServiceFeeAgreement, sfa_summary, customer_search_by_name, producer_details,view_prompts]  + [CompleteOrEscalate]
compensation_tool_node = ToolNode(compensation_tools, handle_tool_errors = TOOL_CALL_ERROR_MESSAGE)
compensation_runnable = compensation_system_prompt | model.bind_tools(compensation_tools)
