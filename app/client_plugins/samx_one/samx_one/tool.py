from langgraph.prebuilt.tool_node import InjectedState
from typing import Annotated, Union, Literal
from langchain_core.tools import tool
import os
import httpx
from client_plugins.samx_one.samx_one_utils.utils import create_structure_from_xtractor_response, get_prior_carrier_list, update_schema_type_for_ny_surcharge, get_samx_one_proposal_request_payload, generate_quote_tracking_number, is_at_least_one_plan_selected, is_page_allowed_to_update_or_navigate, is_employees_valid, get_zip_info, get_sic_info, create_case_track_payload_initial
from config.db import get_client_database_connection
import base64
import io
import traceback
from utils.tool_utils import log_tool_error
from config.openAI import model, json_model
from utils.general_utils import get_gpt_message_object
from client_plugins.samx_one.samx_one.classes import BinderCheck
import json
from datetime import datetime
from pydantic import Field
import asyncio
from typing import Literal
import os
from utils.helpers.constants import OEC_SAMXONE_CLIENT_ID
from client_plugins.samx_one.samx_one_utils.samx_one_service import SamxOneService


NO_QUOTE_ID_FALLBACK_MESSAGE = "Please view the case or start a new quote in order to get the response."
UPDATE_QUERY_INVALID_FALLBACK_MESSAGE = "The update query is invalid or currently not supported or disabled. Please update your query and try again."

def datetime_serializer(obj):
    if isinstance(obj, datetime):
        return obj.isoformat()  # Convert datetime to ISO 8601 string
    raise TypeError("Type not serializable")

@tool
async def samx_one_validate_application_documents(
    state: Annotated[dict, InjectedState], 
    quote_id: str,
    document_type: list[Literal[
    "excess_loss_(stop_loss)_application",
    "commissions_billing_and_collections_agreement",
    "employer_application",
    "new_york_surcharge_form_or_applicable_waiver",
    "employer_payment_authorization_form",
    'specialty_employer_application',
    'rate_guarantee_form',
    # ...
    ]]
) -> dict[str, list]:
    """This tool is used to validate application documents (PDF) for a given quote id and list of document types. 
       Allow this tool to be called without user uploading the pdf file.
       
       Args:
        - state: The state of the tool.
        - quote_id: The quote id for which the document needs to be validated. This will be hex string.
        - document_type: The list of document types to be validated. This will be a list of strings.
        
       Returns:
        - A dictionary representation of ValidationResponse object containing validation results for each document type.
          Each validation result includes document_type, a list of fields with validation details, and overall accuracy.
    """
    try:
        service = SamxOneService(state)
        
        pdf_data = state.get("pdf_data")
        if not pdf_data:
            raise Exception("Please upload a relevant pdf file to get structured output.")
        if quote_id is None or quote_id == "":
            raise Exception("Please provide a valid quote id.")
        pdf_data = base64.b64decode(pdf_data)
        pdf_file = io.BytesIO(pdf_data)

        schema_quote_mapping = []
        doh_4402_found=False
        doh_4399_missing_pages_note = None  # Ensure this is always defined before the loop
        for doc_type in document_type:
            if doc_type == "new_york_surcharge_form_or_applicable_waiver":
                # Use OCRmyPDF for text extraction instead of external OCR service
                try:
                    form_data = {
                        'pdf_file': ('document.pdf', pdf_file, 'application/pdf')
                    }

                    async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True', timeout=120.0) as client:
                        response = await client.post(os.environ["XTRACTOR_ENDPOINT"], files=form_data)
                        if response.status_code >= 400:
                            raise Exception(f"Failed to process the PDF file. HTTP Status: {response.status_code}")
                        
                        response_json = response.json()

                    if not response_json:
                        raise Exception("Failed to process the PDF file. Please try again.")

                    print(f"Extracted text for NY surcharge:",  response_json["content"])  # Print first 100 characters for debugging
                except Exception as e:
                    # Log the error and raise an exception
                    await log_tool_error(state, traceback.format_exc(), f"Failed to extract text from PDF: {str(e)}")
                    raise Exception(f"Failed to process the PDF file: {str(e)}")
                
                # Check content and determine updated document type
                [updated_doc_types, doh_4402_found] = update_schema_type_for_ny_surcharge([doc_type], response_json["content"])
                if "ny_surcharge_4399" in updated_doc_types:
                    # Check for the presence of the first 3 pages
                    missing_pages = []
                    for page_num in range(1, 4):
                        marker = f"Page {page_num} of 5"
                        if not any(marker in line for line in response_json["content"]):
                            missing_pages.append(page_num)
                    if missing_pages:
                        doh_4399_missing_pages_note = (
                            f"Page no. {', '.join(str(p) for p in missing_pages)} missing. "
                        )
                print(f"Updated document type for NY surcharge: {updated_doc_types}")
                
                # Process each updated document type
                for updated_doc_type in updated_doc_types:
                    # Use updated document type to get correct structure_schema
                    db = await get_client_database_connection(OEC_SAMXONE_CLIENT_ID)
                    structure_schema = await db["SAMxOneSchemas"].find_one({"schema_type": {"$eq": updated_doc_type}})
                    if not structure_schema:
                        # If schema not found, log for debugging
                        print(f"Schema not found for type: {updated_doc_type}. Available schema types should be checked.")
                        raise Exception(f"No schema found for the document type: {updated_doc_type}")
                    
                    quote = await service.get_quote({"quoteId": quote_id}, structure_schema["projection"])
                    if not quote:
                        raise Exception(f"No quote found for the quote id: {quote_id}")
                    
                    schema_quote_mapping.append({
                        "schema_type": updated_doc_type,
                        "quote": quote,
                        "schema": structure_schema["schema"],
                        "extracted_content": response_json["content"],  # Include extracted content for further processing
                        "doh_4399_missing_pages_note": doh_4399_missing_pages_note  # Include note if applicable
                    })
                
                # Reset the BytesIO position for potential reuse
                pdf_file.seek(0)
            else:
                db = await get_client_database_connection(OEC_SAMXONE_CLIENT_ID)
                structure_schema = await db["SAMxOneSchemas"].find_one({"schema_type": {"$eq": doc_type}})
                if not structure_schema:
                    raise Exception(f"No schema found for the document type: {doc_type}")
                
                quote = await service.get_quote({"quoteId": quote_id}, structure_schema["projection"])
                if not quote:
                    raise Exception(f"No quote found for the quote id: {quote_id}")
                schema_quote_mapping.append({
                    "schema_type": doc_type,
                    "quote": quote,
                    "schema": structure_schema["schema"]
                })
        
        prompt = f"""
            You are an expert in extracting data from PDF documents and validating them against the provided quote.
            You will be provided with a PDF document as images and a schema_quote_mapping which is a list of dictionaries.
            
            The schema_quote_mapping contains:
              - schema_type: The type of document being validated
              - quote: The quote data that needs to be verified against the extracted information
              - schema: The data extraction blueprint
            
            Each field in the schema contains these keys:
              - fieldname: The name of the field to be added to the JSON
              - description: A description of what data to extract for this field
              - path: The JSON path where the field will be added
              - rules: (optional) Specific validation rules or conditions for the field
              - data_type: (optional) Expected data type (string, number, date, etc.)

            Important:
                Always treat each dictionary in schema_quote_mapping as a separate entity.
                Create a JSON object as extracted_data which contains the extracted data from the PDF using the schema. This JSON object should be in accordance with the structure or the path provided for each fieldname.
                The extracted_data should has following nodes:
                1. data node 
                2. arrayFields 
                3. signatureDate
               
            Validation Instructions:
              1. For each dictionary in schema_quote_mapping, extract all fields defined in its schema from the PDF and save it in extracted_data.
              2. For each extracted field, find its corresponding value in the quote using the field's path
              3. Compare the extracted value with the value from the quote at the specified path
              4. Apply fuzzy matching for text comparisons:
                 - "123 Main Street" matches "123 Main St."
                 - "ABC Company Inc." matches "ABC Co. Inc."
                 - "John A. Smith" matches "Smith, John A"
                 - Invalid scenario: "" does not matches with "".
              5. (Important) DO NOT MATCH EMPTY STRINGS. If the quote value is an empty string and the extracted value is empty string, mark "is_valid": false in output for this field.
              6. For numeric values, compare the actual numbers (ignoring formatting)
              7. For dates, normalize to MM/DD/YYYY format before comparing
              8. (Important) instruction for matching within signatureDate node, capture the effective date from quote and fuzzy match withing min/max date ranges given below:
                - The min date range should be 4 months before effective date from quote
                - The max date range should be 1 month after effective date from quote
                - The date should not be in the future
                - "2023-10-01" matches "October 1, 2023"
                - Return true/false based on the match
              9. For matching within arrayFields node, ensure that the extracted value fuzzy matches one of the values in the array
              10. For addresses, match if the key components (number, street name, city, state, zip) match
              11. Mark fields as valid only when the extracted value reasonably matches the quote value
              12. Do not hallucinate or change the schema; always follow the path mentioned in the schema
              13. Always use the quote data as the source of truth for validation. Do not make up the values for extracted data.
              14. Do not mix extracted values from PDF with the quote data. Use the quote data for validation.

            Special Instructions for DOH-4399:
                1. If the schema_quote_mapping entry for "ny_surcharge_4399" contains a key called "doh_4399_missing_pages_note", append the following in the fields of validation_results entry for "ny_surcharge_4399" in the output JSON. 

                    "fields": [
                        {{
                            "fieldname": "DOH_4399",
                            "is_valid": false,
                            "reason": "{doh_4399_missing_pages_note}",
                            "extracted_value": "",
                            "quote_value": ""
                        }},
                        ... other fields   
                    ],
                2. If there is no such key called "doh_4399_missing_pages_note", do not include the above.

            Response Format:
            Return a JSON with the following structure:
            {{
                "validation_results": [
                    {{
                        "document_type": "document_type_here",
                        "fields": [
                            {{
                                "fieldname": "Name of the field in schema",
                                "is_valid": true/false,
                                "reason": "optional_reason_if_invalid",
                                "extracted_value": "value_from_pdf",
                                "quote_value": "value_from_quote"
                            }},
                            
                        ],
                        "accuracy": "percentage_of_correct_matches",
                    }},
                ]
            }}

            Note that the document_type must be one of:
            ['employer_application', 'employer_payment_authorization_form', 'excess_loss_(stop_loss)_application',
            'commissions_billing_and_collections_agreement', 'rate_guarantee_form', 'ny_surcharge_4403', 
            'ny_surcharge_4264', 'ny_surcharge_4399', 'non-participation_election_form', 
            'specialty_employer_application']

            If doh_4402_found is true, then add the following to the output JSON:
            "doh_4402_found": true
            
            schema_quote_mapping: {schema_quote_mapping}
            doh_4402_found: {doh_4402_found}
        """
        messages = await get_gpt_message_object(pdf_data, prompt, 200, True)
        response = await json_model.ainvoke(messages)
        json_response = json.loads(response.content)
        return json_response
    except Exception as e:
        await log_tool_error(state, traceback.print_exc(), "An error occurred while processing the PDF file")
        raise Exception("An error occurred while processing the PDF file") 

@tool
async def create_structure_from_schema(state: Annotated[dict, InjectedState], schema_type: list[str]):
    """Call this tool to create JSON from predefined PDF from schema using schema type without user uploading the pdf."""

    try:
        pdf_data = state.get("pdf_data")
        if not pdf_data:
            raise Exception("Please upload a relevant pdf file to get structured output.")
        
        pdf_data = base64.b64decode(pdf_data)   
        pdf_file = io.BytesIO(pdf_data)  
        pdf_file.filename = 'document.pdf'
        
        form_data = {
            'pdf_file': ('document.pdf', pdf_file, 'application/pdf')
        }

        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True', timeout=120.0) as client:
            response = await client.post(os.environ["XTRACTOR_ENDPOINT"], files=form_data)
            if response.status_code >= 400:
                raise Exception(f"Failed to process the PDF file. HTTP Status: {response.status_code}")
            
            response_json = response.json()

        if not response_json:
            raise Exception("Failed to process the PDF file. Please try again.")
        

        schema_type = update_schema_type_for_ny_surcharge(schema_type, response_json["content"])

        db = await get_client_database_connection(OEC_SAMXONE_CLIENT_ID)
        structure_schemas_cursor = db["SAMxOneSchemas"].find({'schema_type': {"$in": schema_type}})
        final_structure_schemas = []
        async for structure_schema in structure_schemas_cursor:
            for schema in structure_schema["schema"]:
                if structure_schema["schema_type"] == "express_automation_quote" and (schema["fieldname"] == "selectCarrier" or schema["fieldname"] == "otherCarrier"):
                    schema["values"] = await get_prior_carrier_list()

                schema["path"] = structure_schema["schema_type"] + "." + schema["path"]
                final_structure_schemas.append(schema)
        
        if len(final_structure_schemas) == 0:
            raise Exception("No schemas found for the provided schema type.")
 
        structured_response = await create_structure_from_xtractor_response(response_json["content"], response_json["key_value_pairs"], final_structure_schemas)
        
        return structured_response
    except Exception as e:
        await log_tool_error(state, traceback.print_exc(), "An error occurred while processing the PDF file")
        raise Exception("An error occurred while processing the PDF file")
    
@tool
async def samx_one_available_capabilities():
    """This tool is called when user whats to know what can they do or what are the capabilities or list of features available"""
    capabilities = [
        {
            "name": "Ask questions related to case",
            "description": "If you are viewing a case in SAMx One then you can inquire about any information like 'How many employees do I have and what are the name and age of them?' or 'What is my company name and selected products?'."
        },
        {
            "name": "Update quote",
            "description": "If you are viewing a case in SAMx One then you can update the quote by asking questions like 'Update my company name to ABC Company' or 'Update my tax id to *********'."
        },
        {
            "name": "Navigation",
            "description": "If you are viewing a case in SAMx One then you can navigate to a specific section of the quote by asking questions like 'Navigate to group details' or 'Navigate to group context'."
        },
        {
            "name": "Quick glance of the case",
            "description": "You can get a quick glance of the case by asking questions like 'Give me a quick glance of the case' or 'What is the overview of the case?'."
        },
        {
            "name": "Generate proposal",
            "description": "You can generate the proposal for the case by asking questions like 'Generate proposal for the case' or 'Create proposal for the case'."
        },
        {
            "name": "Start a new quote",
            "description": "You can start a new quote by asking questions like 'Start a new quote' or 'Create a new quote'."
        }
    ]
    capabilities_list = "\n".join([f"{i+1}. **{capability['name']}**: {capability['description']}" for i, capability in enumerate(capabilities)])
    return "The available capabilities for the SAMx One client as follows:\n\n" + capabilities_list + "\n\n"

@tool
async def validate_binder_check(state: Annotated[dict, InjectedState]):
    """This tool is used to validate the binder-check, if the query consists of validate binder-check then this tool should be tagged with it.
    The pdf is present in state. no need to pass the pdf file as an argument."""

    try:
        pdf_data = state.get("pdf_data")
        if not pdf_data:
            raise Exception("Please upload a relevant pdf file to get structured output.")
        pdf_data = base64.b64decode(pdf_data)   

        prompt =f"""
            you are an expert in extracting data from binder-check images.
            here are the fields you need to extract from the binder-check image.
            1. company_name: The name of the company.
            2. signature: A boolean value indicating whether the document has a signature or not.
            3. check_number: A boolean value indicating if the check number exists.
            4. routing_number: A boolean value indicating if the routing number is nine digits.
            5. valid_binder_check: A boolean value indicating whether the document is a valid binder check or not.
            6. reason: If the document is not a valid binder check, please provide the reason for it.
        """
        messages = await get_gpt_message_object(pdf_data,prompt,200,True)
        llm_str = model.with_structured_output(BinderCheck)

        response = await llm_str.ainvoke(messages)
        if not isinstance(response, BinderCheck):
            raise Exception("An error occurred while processing the PDF file")

        # Convert the response to a dictionary if it's a BinderCheck object
        response = response.__dict__

        json_response = json.dumps(response)
        return json_response
    except Exception as e:
        await log_tool_error(state, traceback.print_exc(), "An error occurred while processing the PDF file")
        raise Exception("An error occurred while processing the PDF file")

@tool
async def samx_one_quote_query(state: Annotated[dict, InjectedState], question: str):
    """
    This tool will provide answers to query related to there quote like 'what is my company name' or 'what is my tax id'. 
    Do not confuse with the samx_one_update_quote tool which is used to update the quote.
    Use this tool only to answer queries related to the quote where user is asking for some information and not modifing anything.

    Args: 
        state: The state of the conversation, which contains the user information and other context.
        question: (required) string
    """
    try:
        service = SamxOneService(state)
        
        if not service.has_quote_id():
            return service.get_fallback_message()
        
        db = await get_client_database_connection(OEC_SAMXONE_CLIENT_ID)
        quote_shape_schema = await db["SAMxOneSchemas"].find_one({"schema_type": "ask_query"})
        quote_shape = quote_shape_schema["schema"]
        
        query_builder_prompt = f"""
        Follow the below steps to build the mongodb project query:
        1. Use the quote_shape: {quote_shape} to build the mongodb project query. quote_shape has path of the field and the description of the field.
        2. Use the description of the field and the question: {question} to build the project query.
        3. Do not hellocinate. Do not make any assumptions.
        4. This is strict instruction. If the field is not present in quote_shape then do not include that field in the query. Do not create any new fields. If only the parent node is present in quote_shape then keep that only.
        5. Return in the JSON format
        e.g.
        {{
            "caseId": 1,
            "companyProfile.companyName": 1,
            "companyProfile.coverage.domesticPartnerCoverage": 1,
            "encoding.underwritingMethodology": 1
        }}
        """

        project_query = json.loads((await json_model.ainvoke(query_builder_prompt)).content)
        fields = [item for item in quote_shape if item["path"] in project_query.keys()]
        filtered_project_query = {}
        removed_fields = []
        
        for field in fields:
            page_name = field.get("page_name")
            if page_name:
                valid_to_update, reason_for_not_allowed = is_page_allowed_to_update_or_navigate(service.get_page_validations(), page_name)
                if valid_to_update:
                    filtered_project_query[field["path"]] = project_query[field["path"]]
                else:
                    removed_fields.append({"field": field["path"], "reason": reason_for_not_allowed})

        if not filtered_project_query:
            return "Please complete the required pages"
        
        quote = await service.get_quote_by_id(projection=filtered_project_query)

        if not quote:
            return "No quote found. Please try again with a valid quote ID."
        
        question_prompt = f"""
        Follow the below steps to answer the question:
        1. Use the json context {quote} to answer the question: {question}.
        2. Refer to the shape of the quote {quote_shape} to answer the question.
        3. Do not make any assumptions. Do not hallucinate. Do not make up any information.
        4. Do not use words like JSON, query, etc.
        5. Answer should be in a tone of a conversation like a chat assistant.
        6. If removed_fields is not empty then return a disclaimer that mentions why those fields are not present in the response. craft this disclaimer using removed_fields and question and quote_shape.
        """

        response = (await model.ainvoke(question_prompt)).content
        return response
    except Exception as e:
        print(f"An error occurred while processing the query: {str(e)}")
        await log_tool_error({}, traceback.format_exc(), "samx_one_quote_query")
        return "Something went wrong. Please try again later."

@tool
async def samx_one_update_quote(state: Annotated[dict, InjectedState], question: str):
    """
    This tool will be responsible for updating the quote or modifing the quote or adding information to the quote.
    
    Args: 
        state: The state of the conversation, which contains the user information and other context.
        question: (required) string
    """
    try:
        service = SamxOneService(state)
        
        if not service.has_quote_id():
            return service.get_fallback_message()
        
        quote = await service.get_quote_by_id(projection={"caseStatus": 1})

        # Get schemas from database
        db = await get_client_database_connection(OEC_SAMXONE_CLIENT_ID)
        quote_update_rules_schema = await db["SAMxOneSchemas"].find_one({"schema_type": "update_quote"})
        quote_shape_schema = await db["SAMxOneSchemas"].find_one({"schema_type": "ask_query"})
        
        quote_update_rules = quote_update_rules_schema["schema"]
        quote_shape = quote_shape_schema["schema"]

        # Generate update query based on user question
        quote_update_query_prompt = f"""
        Follow the below steps to build the mongodb update query:
        1. Use the quote_update_rules: {quote_update_rules} to build the mongodb update query. quote_update_rules has path of the field and the description of the field.
        2. Use the description of the field and the question: {question} to build the update query.
        3. Do not hallucinate. Do not make any assumptions.
        4. If the "is_allowed" field is Ture then include that field in the  $set query. else do not include that field in the $set query.
        5. Follow the "rules" to validate the query for that field. If "is_allowed" field is False then return the "rules" in the response.
        6. Use case status {quote.get("caseStatus", 100)} to enforce the rules.
        7. Return the fields that are not part of quote_update_rules as not_allowed. only include those feilds that are asked in question. no need to include all the fields that are not part of quote_update_rules. not_allowed should be a list of field names not field objects.
        8. Strict instruction: Always return the response in the JSON format mentioned below. in $set include the mongodb update query and not_allowed should contain the field_names that are not part of update.
            "$set": {{
                "companyProfile.companyName": "ABC Company",
                "companyProfile.coverage.domesticPartnerCoverage": "Yes",
                "encoding.underwritingMethodology": "Manual",
                ...
            }}, 
            "not_allowed": [...]
        """

        # Process update query
        update_query_response = await json_model.ainvoke(quote_update_query_prompt)
        update_query = json.loads(update_query_response.content)
        
        # Filter fields by page permissions
        filtered_update_query = {}
        pages = []
        removed_fields = []
        not_allowed = update_query.get("not_allowed", [])

        # If nothing to update, return error message
        if not update_query.get("$set"):
            return UPDATE_QUERY_INVALID_FALLBACK_MESSAGE
        
        # Get all fields that match paths in the update query
        fields = [item for item in quote_shape if item["path"] in update_query.get("$set", {}).keys()]
        
        # Process each field
        for field in fields:
            page_name = field.get("page_name")
            if page_name:
                valid_to_update, reason_for_not_allowed = is_page_allowed_to_update_or_navigate(
                    service.get_page_validations(), page_name
                )
                if valid_to_update:
                    filtered_update_query[field["path"]] = update_query["$set"][field["path"]]
                    pages.append(page_name)
                else:
                    removed_fields.append({"field": field["path"], "reason": reason_for_not_allowed})
        
        # If nothing to update, return error message
        if not filtered_update_query and not not_allowed:
            return UPDATE_QUERY_INVALID_FALLBACK_MESSAGE
        
        # Find fields that can be updated on current page
        same_page_updates = {}
        current_page = service.get_current_page()
        for field in fields:
            page_name = field.get("page_name")
            if (
                page_name and page_name in current_page and 
                field["path"] in filtered_update_query and 
                "ui_update_path" in field and field["ui_update_path"]
            ):
                same_page_updates[field["ui_update_path"]] = filtered_update_query[field["path"]]

        # Get unique pages for navigation suggestions
        follow_up_questions = []
        unique_pages = list(set(pages))
        for page in unique_pages:
            if page not in current_page:
                if page in "start-quote":
                    follow_up_questions.append("Navigate to group context")
                else:
                    follow_up_questions.append(f"Navigate to {' '.join(page.split('-'))}")
        
        # Update the quote using service method
        if filtered_update_query:
            await service.update_quote(filtered_update_query)
            custom_chatbot_json_message = "Your query has been updated successfully."
        if len(follow_up_questions) > 0:
            custom_chatbot_json_message += " Please navigate to the following pages to validate your changes."
        
        if len(not_allowed) > 0 or removed_fields:
            custom_chatbot_json_message += "\n\nNote: The following fields were not updated due to the following reasons:\n"
        
        for field in not_allowed:
            if field == "employees":
                custom_chatbot_json_message += "- **Employees**: Not allowed to update. Please navigate to the census page to update employees.\n"
                follow_up_questions.append("Navigate to census")
            elif field == "owners":
                custom_chatbot_json_message += "- **Owners**: Not allowed to update. Please navigate to the owner information page to update owners.\n"
                follow_up_questions.append("Navigate to owner information")
            else:
                custom_chatbot_json_message += f"- **{field}**: Not allowed to update.\n"
        
        for field in removed_fields:
            custom_chatbot_json_message += f"- **{field['field']}**: {field['reason']}\n"
        
        return {
            "answer": {
                "client": "samx_one",
                "response_type": "update_quote",
                "pages": unique_pages,
                "should_navigate": False,
                "showUI": False,
                "same_page_updates": same_page_updates,
            },
            "follow_up_questions": follow_up_questions,
            "custom_chatbot_json_message": custom_chatbot_json_message
        }
    except Exception as e:
        print(e)
        await log_tool_error({}, traceback.format_exc(), "samx_one_update_quote")
        return "Something went wrong. Please try again later."

@tool
async def samx_one_handle_navigation(state: Annotated[dict, InjectedState], question):
    """This tool is responsible for user navigation in the quote. If user wants to navigate to a specific section of the quote, this tool will handle that.

    Args: 
        state: The state of the conversation, which contains the user information and other context.
        question: (required) string
    """

    try:
        service = SamxOneService(state)
        
        if not service.has_quote_id():
            return service.get_fallback_message()

        db = await get_client_database_connection(OEC_SAMXONE_CLIENT_ID)
        navigation_path_schema = await db["SAMxOneSchemas"].find_one({"schema_type": "navigation_path"})
        navigation_path = navigation_path_schema["schema"]

        prompt = f"""
        Follow the below steps to navigate to the specific section of the quote:
        1. Use the navigation_path: {navigation_path} to analyse the description and respond with the appropriate page.
        2. Only return the page.
        3. Use the question: {question} to navigate to the specific section.
        4. Do not make any assumptions. Do not hallucinate. Do not make up any information.
        """

        page = (await model.ainvoke(prompt)).content

        valid_to_navigate, reason_for_not_allowed = is_page_allowed_to_update_or_navigate(service.get_page_validations(), page)
        if not valid_to_navigate:
            return reason_for_not_allowed

        return {
            "client": "samx_one",
            "response_type": "navigation",
            "page": "/" + page,
            "showUI": False
        }
    except Exception as e:
        await log_tool_error({}, traceback.format_exc(), "samx_one_handle_navigation")
        return "Something went wrong. Please try again later."

@tool
async def samx_one_quote_glance(state: Annotated[dict, InjectedState], caseId: int):
    """
    This tool is responsible for quick preview or quick glance of the case. It will use caseId to fetch the case information.
    Args:
        state: The state of the conversation, which contains the user information and other context.
        caseId: (required) id of the case in integer format
    """
    try:
        service = SamxOneService(state)
        
        if not caseId:
            return "Please provide the Case ID of the quote to get the quick glance."
            
        overview_fields = [
            "owners.producer", 
            "owners.salesRep", 
            "owners.agency",
            "companyProfile.companyName",
            "companyProfile.administrativeContact.primaryEmail",
            "companyProfile.administrativeContact.phoneNumber",
            "companyProfile.executiveContact.executiveEmail",
            "selectedMedicalPlans", 
            "selectedDentalPlans", 
            "selectedVisionPlans",
            "selectedBasicLifePlans",
            "selectedDepLifePlans",
            "selectedLTDPlans",
            "selectedSTDPlans",
            "selectedSuppLifePlans",
            "employees",
            "companyProfile.taxId", 
            "businessType"
        ]
        project_query = {field: 1 for field in overview_fields}
        
        quote = await service.get_quote_by_case_id(int(caseId), project_query)
        
        if not quote:
            return "No quote found with the provided Case ID."
        return {
            "client": "samx_one", 
            "response_type": "quick_preview", 
            "case": {
                "caseId": caseId,
                "companyName": quote.get("companyProfile", {}).get("companyName", ""),
                "producer": quote.get("owners", {}).get("producer", []),
                "salesRep": quote.get("owners", {}).get("salesRep", []),
                "agency": quote.get("owners", {}).get("agency", []),
                "administrativeContact": quote.get("companyProfile", {}).get("administrativeContact", {}),
                "executiveContact": quote.get("companyProfile", {}).get("executiveContact", {}),
                "selectedMedicalPlans": quote.get("selectedMedicalPlans", {"FI": [], "LF": []}),
                "selectedDentalPlans": quote.get("selectedDentalPlans", []),
                "selectedVisionPlans": quote.get("selectedVisionPlans", []),
                "selectedBasicLifePlans": quote.get("selectedBasicLifePlans", []),
                "selectedDepLifePlans": quote.get("selectedDepLifePlans", []),
                "selectedLTDPlans": quote.get("selectedLTDPlans", []),
                "selectedSTDPlans": quote.get("selectedSTDPlans", []),
                "selectedSuppLifePlans": quote.get("selectedSuppLifePlans", []),
                "employees": quote.get("employees", []),
                "taxId": quote.get("companyProfile", {}).get("taxId", ""),
                "businessType": quote.get("businessType", "")
            }
        }
    except Exception as e:
        print(e)
        await log_tool_error({}, traceback.format_exc(), "samx_one_quote_glance")
        return {"error": "Something went wrong. Please try again later."}

@tool
async def samx_one_generate_proposal(state: Annotated[dict, InjectedState], case_id: int):
    """This tool is responsible for generating the proposal for the case. It will use caseId to fetch the case information.
    Args:
        caseId: (optional) Case ID or Group ID of the case. This is always in interger format
    """

    try:
        service = SamxOneService(state)
        
        if not (case_id or service.has_quote_id()):
            return "Please provide the Case ID or Quote ID of the quote to view the quote."
            
        quote = None
        if service.has_quote_id():
            quote = await service.get_quote_by_id()
        else:
            quote = await service.get_quote_by_case_id(int(case_id))
            
        if not quote:
            return "No quote found with the provided Case ID."
        if not is_at_least_one_plan_selected(quote):
            return "Please select at least one plan to generate the proposal."
        req_payload = get_samx_one_proposal_request_payload(quote)
        return {
            "client": "samx_one",
            "response_type": "generate_proposal",
            "showUI": False,
            "req_payload": json.loads(json.dumps(req_payload, default=datetime_serializer)),
        }
    except Exception as e:
        print(e)
        await log_tool_error({}, traceback.format_exc(), "samx_one_generate_proposal")
        return "Something went wrong. Please try again later."
    
@tool
async def samx_one_start_new_quote():
    """This tool is responsible for starting a new quote"""
    return {
        "answer": "Thanks for starting a new quote with us. We can help you create a quote quickly. \n\n If you have some documents, we can prepopulate your case by extracting information from documents you may have. Do you have any of these types of documents? \n\n Group/Employee Information: [List: Ex:  Employer Application, Employee Census, etc] \n\n Plan/Benefit Information: [List: Ex: SBC Documents, Competitor Renewal Proposal, etc]",
        "follow_up_questions": ["Yes, I have some of these documents", "Create quote without documents using questionnaire"]
    }

@tool
async def samx_one_create_quote_using_uploaded_documents(
    state: Annotated[dict, InjectedState], 
    funding_type: Literal["Fully-Insured", "Level-Funded", "Both"], 
    selected_products: list[Literal["Medical", "Dental", "Vision", "Life", "LTD", "STD"]] = Field(..., description="The list of products for which the quote is being created"),
    created_quote_id: Union[str, None] = None,
    steps: Literal[
        "start_quote_select_funding_type", 
        "start_quote_select_products",
        "start_quote_upload_benefit_documents",
        "start_quote_upload_census",
        "start_quote_finalize_quote",
        ] = "start_quote_select_funding_type"
    ):
    """
    This tool will be called only if the user has selected the option 'Yes, I have some of these documents' in the samx_one_start_new_quote tool.
    It will help the user to create a new quote through the new quote tool by uploading the documents and extracting the information from the documents.
    Follow all the steps in the process to create a new quote through the new quote tool.
    args:
        state: The state of the conversation, which contains the user information and other context.
        funding_type: (required) funding type for the quote. It can be Fully-Insured, Level-Funded or Both.
        selected_products: (optional) list of products for which the quote is being created. It can be Medical, Dental, Vision, Life, LTD, STD.
        created_quote_id: (optional) This is the ID of the quote that is created after start_quote_upload_benefit_documents step is called. This will be used in later steps. This ID will be hex decimal string.
        steps: (optional) steps to create a new quote through the new quote tool
            start_quote_select_funding_type: Initiate the quote creation process through the new quote. Ask the user for selecting the funding type. Should be called first.
            start_quote_select_products: This step is called after start_quote_select_funding_type is called. This will allow user to select the products for quote creation. This will be the second step in the process.
            start_quote_upload_benefit_documents: This step is called afer the user has selected funding type. This all allow user to upload documents for quote creation. This will be the third step in the process.
            start_quote_upload_census: This step is called after start_quote_upload_benefit_documents is called. This will allow user to upload the census document for quote creation. This will be the fourth step in the process.
            start_quote_finalize_quote: This step is called after start_quote_upload_benefit_documents is called. This is the final step in the process.
    """
    try:
        service = SamxOneService(state)
        
        if steps == "start_quote_select_funding_type":
            return {
                "answer": "Please select the funding type for your quote. We support both Fully-Insured and Level-Funded funding types.",
                "follow_up_questions": ["Fully-Insured", "Level-Funded", "Both"]
            }
        if steps == "start_quote_select_products":
            if funding_type not in ["Fully-Insured", "Level-Funded", "Both"]:
                return "Please select a valid funding type to proceed."
            
            return "Please select the products for your quote. You can select multiple products. We support Medical, Dental, Vision, Life, LTD, and STD products."
        if steps == "start_quote_upload_benefit_documents":
            if selected_products is None or len(selected_products) == 0:
                return "Please select at least one product to proceed with the quote creation."
            return {
                "client": "samx_one",
                "response_type": "start_quote_upload_benefit_documents",
                "custom_chatbot_json_message": "Great! Please upload the documents that you have. We will extract the information from the documents and create a quote for you."
            }
        if steps == "start_quote_upload_census":
            pdf_data = state.get("pdf_data")
            if not pdf_data:
                raise Exception("Please upload a relevant pdf file to get structured output.")
            pdf_data = base64.b64decode(pdf_data)   
            schema_type = None
            if funding_type == "Fully-Insured":
                schema_type = "samxone_fi"
            elif funding_type == "Level-Funded":
                schema_type = "samxone_lf"
            elif funding_type == "Both":
                schema_type = "samxone_both"
            db = await get_client_database_connection(OEC_SAMXONE_CLIENT_ID)
            schema = await db["SAMxOneSchemas"].find_one({"schema_type": {"$eq": schema_type}})
            samxone_schema = schema["schema"]
            prompt = f"""
                System: You are an expert in encoding data from health insurance documents for creating quote structures.
                
                Task Overview:
                You have been given a task to extract structured data from a document and create a comprehensive health insurance quote based on a predefined schema.
                
                Input Data:
                1. Schema: A blueprint that defines the structure, field types, descriptions, and validation rules for the quote data. This schema is the definitive guide for how the output should be structured.
                2. Images : The images of the document that was uploaded by the user.
                
                Schema Structure:
                The schema contains fields with the following properties:
                - fieldname: The name of the field to be added to the JSON structure
                - description: A detailed explanation of what the field represents
                - path: The hierarchical location where the field should be placed in the output JSON
                - rules: Any specific validation or formatting rules that need to be applied
                - data_type: The expected data type (string, number, boolean, array, etc.)
                - values: For enumerated fields, the list of acceptable values
                
                Instructions:
                1. Analyze the schema thoroughly to understand the required structure and field relationships.
                2. Extract relevant information from the document content that matches fields in the schema.
                3. Apply any transformation rules specified in the schema (date formatting, numeric conversions, etc.).
                4. For fields with enumerated values, ensure the extracted data matches one of the allowed options.
                5. Organize the data hierarchically according to the path specifications in the schema.
                6. For fields where information cannot be confidently extracted, use empty strings rather than making assumptions.
                7. Ensure all required fields are populated if the information is available in the document.
                8. Do not add fields that aren't defined in the schema.
                9. Do not alter the schema structure or field definitions.
                10. Format dates consistently according to schema rules (MM/DD/YYYY unless otherwise specified).
                11. Very Important: For state, stateFIPSCode, city, county, safesCounty, countyFIPSCode if the values are not available in the document, Try to use the zip code to get that information.
                Accuracy Guidelines:
                - Be precise in extracting numerical values like employee counts, zip codes, and tax IDs.
                - Pay special attention to contact information like phone numbers and email addresses.
                - Carefully identify enumerated values like "Yes"/"No" options and match them exactly.
                - For complex fields like coverage details, ensure the hierarchical structure follows the schema.
                - Double-check date fields to ensure proper formatting.
                
                Output:
                Provide a complete, well-structured JSON object that follows the schema exactly and contains all extractable information from the document.
                
                Schema: {samxone_schema}

                Please provide the structured data as a JSON object.
            """
            messages = await get_gpt_message_object(pdf_data,prompt,pdf_data=True)
            response = await json_model.ainvoke(messages)
            json_response = json.loads(response.content)
            caseId_resp = await service.generate_case_id()
            caseId = caseId_resp["caseId"]
            # Get zip code and SIC code information using traditional dictionary access with square brackets
            zip_code = json_response["companyProfile"]["zipCode"]
            sic_code = json_response["companyProfile"]["sicCode"]
            effective_date = json_response["companyProfile"]["effectiveDate"]
            
            zip_info, sic_info = await asyncio.gather(
                get_zip_info(zip_code),
                get_sic_info(sic_code, effective_date)
            )
            
            updated_selected_products = []
            for product in selected_products:
                if product == "Medical":
                    updated_selected_products.append("M")
                if product == "Dental":
                    updated_selected_products.append("D")
                if product == "Vision":
                    updated_selected_products.append("V")
                if product == "Life":
                    updated_selected_products.append("BL")
                if product == "LTD":
                    updated_selected_products.append("LTD")
                if product == "STD":
                    updated_selected_products.append("STD")
            
            updated_funding_type = []
            if funding_type == "Fully-Insured":
                updated_funding_type.append("FI")
            elif funding_type == "Level-Funded":
                updated_funding_type.append("LF")
            elif funding_type == "Both":
                updated_funding_type.append("FI")
                updated_funding_type.append("LF")

            quote = json_response.copy()
            quote.update({
                "caseId": caseId,
                "quoteTrackingNumber": generate_quote_tracking_number("BNE", "NB"),
                "fundingType": updated_funding_type,
                "companyProfile": {
                    **quote["companyProfile"],
                    "sicCodeDescription": sic_info["sicCodeDescription"],
                    "sicCodeDivisionDescription": sic_info["sicCodeDescription"],
                },
                "selectedProducts": updated_selected_products,
                "zipCodeInfo": zip_info,
                "sicCodeInfo": sic_info,
                "directSale": False,
                "caseStatus": 100,
                "owners": {
                    "producer": [],
                    "agency": [],
                    "salesRep": [],
                    "generalAgent": [],
                    "gaSalesRep": []
                },
            })
            res = await service.create_quote(quote)
            quote_id = res["quoteId"]
            await create_case_track_payload_initial(quote, service.get_env(), quote_id)
            
            return {
                "client": "samx_one",
                "response_type": "start_quote_upload_census",
                "quote_id": quote_id,
                "custom_chatbot_json_message": f"Your Quote is ready with Quote ID: {quote_id}. Please upload the census document. We will extract the census information from the document for your quote. You can submit without uploading the census document, but it is recommended to upload it. You can view plans and generate proposal if census document is uploaded.",
            }
        if steps == "start_quote_finalize_quote":
            quote = await service.get_quote_by_id(created_quote_id, projection={
                "caseId": 1,
                "companyProfile.companyName": 1,
                "companyProfile.state": 1,
                "companyProfile.effectiveDate": 1,
                "employees": 1,
            })
            
            follow_up_questions = ["View my case"]
            if quote and quote.get("employees") and len(quote["employees"]) > 0:
                follow_up_questions.append("Browse Plans")
                follow_up_questions.append("Generate Proposal")
            return {
                "answer": {
                    "client": "samx_one",
                    "response_type": "view_created_quote",
                    "company_information": {
                        "companyName": quote["companyProfile"]["companyName"],
                        "groupId": quote["caseId"],
                        "fundingType": "Fully Insured/Level Funded" if funding_type == "Both" else funding_type,
                        "state": quote["companyProfile"]["state"],
                        "effectiveDate": quote["companyProfile"]["effectiveDate"],
                        "quoteId": created_quote_id
                    }
                },
                "follow_up_questions": ["View my case", "Browse Plans", "Generate Proposal"],
                "custom_chatbot_json_message": f"Your quote has been created successfully using the uploaded documents with Case ID: {quote['caseId']}. You can view the quote details below.",
            }
        return "Invalid step provided. Please try again with a valid step."
    except Exception as e:
        print(f"Error in samx_one_create_quote_using_uploaded_documents: {e}")
        print(traceback.format_exc())
        await log_tool_error({}, traceback.format_exc(), "samx_one_create_quote_using_uploaded_documents")
        return "Something went wrong. Please try again later."

@tool
async def samx_one_quote_through_separate_tool(state: Annotated[dict, InjectedState], tool_name: Union[str, None] = None, transaction_id: Union[str, None] = None, steps: Literal["quote_through_separate_tool", "quote_through_separate_tool_with_transaction_id"] = "quote_through_separate_tool"):
    """This tool is responsible if user has created a quote through a separate tool or platform. This tool should be called after samx_one_start_new_quote is called.
    This tool will be called only if the user has selected the option 'No, I have created a quote through a seprate tool/platform' in the samx_one_start_new_quote tool.
        args:
            state: The state of the conversation, which contains the user information and other context.
            tool_name: (optional) name of the tool/platform or the partner name
            transaction_id: (optional) transaction id or group name
            steps: (optional) steps to create a new quote through a separate tool
                quote_through_separate_tool: Initiate the quote creation process through a separate tool without transaction id. Should be called first.
                quote_through_separate_tool_with_transaction_id: Initiate the quote creation process through a separate tool with transaction id. Should be called after quote_through_separate_tool.
    """
    try:
        if steps == "quote_through_separate_tool":
            if not tool_name:
                return "That's great. What platform or tool did you create this quote on?"
            return f"{tool_name} is one of our preferred partners. We can import your quote directly from them. What is your transaction ID?"
        
        if steps == "quote_through_separate_tool_with_transaction_id":
            if not transaction_id:
                return "Please provide the transaction ID to proceed."
        return {
            "answer": {
                "client": "samx_one",
                "response_type": "view_created_quote",
                "transaction_id": transaction_id,
                "company_information": {
                    "companyName": "New Shopping Experience",
                    "groupId": "1048476",
                    "fundingType": "Fully Insured/Level Funded",
                    "state": "MA",
                    "effectiveDate": "09/01/2025",
                    "quoteId": "68545e4967b0d1404e144669",
                }
            },
            "follow_up_questions": ["View my case", "Browse Plans", "Upload additional documents"],
            "custom_chatbot_json_message": "Your quote has been created successfully with Case ID 1006685. You can view the quote details below.",
        }
    except Exception as e:
        print(f"Error in samx_one_quote_through_separate_tool: {e}")
        print(traceback.format_exc())  # Log the full traceback
        await log_tool_error({}, traceback.format_exc(), "samx_one_quote_through_separate_tool")
        return "Something went wrong. Please try again later."
    
@tool
async def samx_one_quote_through_questionnaire(
    state: Annotated[dict, InjectedState],
    group_name: str = Field(..., description="The name of the group for which the quote is being created."),
    zip_code: str = Field(..., description="The zip code of the group for which the quote is being created. Must be a valid US zip code of 5 digits."),
    effective_date: str = Field(..., description="The effective date of the quote in MM/DD/YYYY format."),
    funding_type: Literal["Fully-Insured", "Level-Funded", "Both"] = Field(..., description="The funding type for the quote. It can be Fully-Insured, Level-Funded or Both."),
    group_size: int = Field(..., description="The size of the group for which the quote is being created. This is an integer value."),
    counting_methodology: int = Field(..., description="The counting methodology for the quote. This is an integer value representing the counting methodology. Must be between 1 and 50."),
    sic_code: str = Field(..., description="The SIC code for the group. This is a string value representing the SIC code. Must be a valid SIC code of 4 digits."),
    products: list[Literal["Medical", "Dental", "Vision", "Life", "LTD", "STD"]] = Field(..., description="The list of products for which the quote is being created")
):
    """This tool is responsible for creating a new quote through the questionnaire. This tool should be called after samx_one_start_new_quote is called.
    This tool will be called only if the user has selected the option 'Create quote without documents using questionnaire' in the samx_one_start_new_quote tool.
    args:
        state: The state of the conversation, which contains the user information and other context.
    """
    try:
        service = SamxOneService(state)
        
        if not (group_name and zip_code and effective_date and funding_type and group_size and counting_methodology and sic_code and products):
            return "Please provide all the required details to proceed with the quote creation."
        
        db = await get_client_database_connection(OEC_SAMXONE_CLIENT_ID)
        schema = await db["SAMxOneSchemas"].find_one({"schema_type": {"$eq": "samx_one_initial_quote"}})
        samxone_schema = schema["schema"]
        prompt = f"""
            System: You are an expert in encoding data from health insurance questionnaires for creating quote structures.
            Task Overview:
            You have been given a task to extract structured data from a questionnaire and create a comprehensive health insurance quote based on a predefined schema.  
            Input Data:
            1. Schema: A blueprint that defines the structure, field types, descriptions, and validation rules for the quote data. This schema is the definitive guide for how the output should be structured.
            2. Questionnaire Responses: The responses provided by the user in the questionnaire.
            Schema Structure:
            The schema contains fields with the following properties:
            - fieldname: The name of the field to be added to the JSON structure
            - description: A detailed explanation of what the field represents
            - path: The hierarchical location where the field should be placed in the output JSON
            - rules: Any specific validation or formatting rules that need to be applied
            - data_type: The expected data type (string, number, boolean, array, etc
            - values: For enumerated fields, the list of acceptable values
            Instructions:
            1. Analyze the schema thoroughly to understand the required structure and field relationships.
            2. Extract relevant information from the questionnaire responses that matches fields in the schema.
            3. Apply any transformation rules specified in the schema (date formatting, numeric conversions, etc.).
            4. For fields with enumerated values, ensure the extracted data matches one of the allowed options.
            5. Organize the data hierarchically according to the path specifications in the schema.
            6. For fields where information cannot be confidently extracted, use empty strings rather than making assumptions.
            7. Ensure all required fields are populated if the information is available in the questionnaire.
            8. Do not add fields that aren't defined in the schema.
            9. Do not alter the schema structure or field definitions.
            10. Format dates consistently according to schema rules (MM/DD/YYYY unless otherwise specified).
            11. Very Important: For state, stateFIPSCode, city, county, safesCounty, countyFIPSCode, Always use the zip code: {zip_code} to get that information.
            Accuracy Guidelines:
            - Be precise in extracting numerical values like employee counts, zip codes, and tax IDs.
            - Pay special attention to contact information like phone numbers and email addresses.
            - Carefully identify enumerated values like "Yes"/"No" options and match them exactly.
            - For complex fields like coverage details, ensure the hierarchical structure follows the schema.
            - Double-check date fields to ensure proper formatting.
            Output:
            Provide a complete, well-structured JSON object that follows the schema exactly and contains all extractable information from the questionnaire.
            Schema: {samxone_schema}
            Questionnaire Responses:
            - groupName: {group_name}
            - zipCode: {zip_code}
            - effectiveDate: {effective_date}
            - fundingType: {funding_type}
            - groupSize: {group_size}
            - countingMethodology: {counting_methodology}
            - sicCode: {sic_code}
            - products: {products}
            Please provide the structured data as a JSON object.
        """
        response = await json_model.ainvoke(prompt)
        json_response = json.loads(response.content)
        caseId_resp = await service.generate_case_id()
        caseId = caseId_resp["caseId"]
        # Get zip code and SIC code information using traditional dictionary access with square brackets
        zip_code = json_response["companyProfile"]["zipCode"]
        sic_code = json_response["companyProfile"]["sicCode"]
        effective_date = json_response["companyProfile"]["effectiveDate"]
        
        zip_info, sic_info = await asyncio.gather(
            get_zip_info(zip_code),
            get_sic_info(sic_code, effective_date)
        )
        
        updated_selected_products = []
        for product in products:
            if product == "Medical":
                updated_selected_products.append("M")
            if product == "Dental":
                updated_selected_products.append("D")
            if product == "Vision":
                updated_selected_products.append("V")
            if product == "Life":
                updated_selected_products.append("BL")
            if product == "LTD":
                updated_selected_products.append("LTD")
            if product == "STD":
                updated_selected_products.append("STD")
            
        updated_funding_type = []
        if funding_type == "Fully-Insured":
            updated_funding_type.append("FI")
        elif funding_type == "Level-Funded":
            updated_funding_type.append("LF")
        elif funding_type == "Both":
            updated_funding_type.append("FI")
            updated_funding_type.append("LF")
        # Update quote with case ID, zip code info, and SIC code info
        quote = json_response.copy()
        quote.update({
            "caseId": caseId,
            "quoteTrackingNumber": generate_quote_tracking_number("BNE", "NB"),
            "fundingType": updated_funding_type,
            "selectedProducts": updated_selected_products,
            "companyProfile": {
                **quote["companyProfile"],
                "sicCodeDescription": sic_info["sicCodeDescription"],
                "sicCodeDivisionDescription": sic_info["sicCodeDescription"],
            },
            "zipCodeInfo": zip_info,
            "sicCodeInfo": sic_info,
            "directSale": False,
            "caseStatus": 100,
            "owners": {
                "producer": [],
                "agency": [],
                "salesRep": [],
                "generalAgent": [],
                "gaSalesRep": []
            },
        })
        res = await service.create_quote(quote)
        quote_id = res["quoteId"]
        await create_case_track_payload_initial(quote, service.get_env(), quote_id)
        
        return {
            "answer": {
                "client": "samx_one",
                "response_type": "view_created_quote",
                "company_information": {
                    "companyName": group_name,
                    "groupId": caseId,
                    "fundingType": "Fully Insured/Level Funded" if funding_type == "Both" else funding_type,
                    "state": quote["companyProfile"]["state"],
                    "effectiveDate": effective_date,
                    "quoteId": quote_id
                }
            },
            "follow_up_questions": ["View my case"],
            "custom_chatbot_json_message": f"Your quote has been created successfully using the questionnaire with Case ID: {caseId}. You can view the quote details below.",
        }
    except Exception as e:
        print(f"Error in samx_one_quote_through_questionnaire: {e}")
        print(traceback.format_exc())
        await log_tool_error({}, traceback.format_exc(), "samx_one_quote_through_questionnaire")
        return "Something went wrong. Please try again later."
    
@tool
async def samx_one_view_quote(state: Annotated[dict, InjectedState], case_id: int, is_browse_plans: bool = False):
    """This tool is responsible for viewing the quote or browsing the plan. It will use case_id or quote_id to fetch the case information.
    Args:
        caseId: (optional) Case ID or Group ID of the case. This is always in interger format
        is_browse_plans: (optional) boolean value to indicate if the user wants to browse the plans or view the quote. Default is False. Should be True if the user wants to browse the plans or view plans or shop plans.
    """

    try:
        service = SamxOneService(state)
        
        if not (case_id or service.has_quote_id()):
            return "Please provide the Case ID or Quote ID of the quote to view the quote."
        
        quote = None
        if case_id:
            quote = await service.get_quote_by_case_id(int(case_id))
        elif service.has_quote_id():
            quote = await service.get_quote_by_id()
            
        if is_browse_plans and not is_employees_valid(quote):
            return {
                    "answer": "Please add valid employees before proceeding to shopping plans.",
                    "follow_up_questions": ["Navigate to employee census"]
                    }
        return {
            "client": "samx_one",
            "response_type": "view_quote" if is_browse_plans is False else "browse_plans",
            "quote_id": str(quote.get("_id", "")),
            "showUI": False,
        }
    except Exception as e:
        await log_tool_error({}, traceback.format_exc(), "samx_one_view_quote")
        return "Something went wrong. Please try again later."

@tool
async def samx_one_suggest_specialty_plans():
    """This tool is responsible for suggesting the specialty plans to the user. This tool should be called when the user asks for the specialty plans suggestion"""

    return {
        "answer": {
            "client": "samx_one",
            "response_type": "suggest_specialty_plans",
            "suggested_plans": {
                "dental": ["A8011"],
                "vision": ["S1012"]
            },
        },
        "showUI": False,
        "follow_up_questions": ["Re-Generate Proposal with Specialty Plans"],
        "custom_chatbot_json_message": "We have selected the specialty plans based on your medical plans selection. For dental, we have selected the plan A8011 and for vision, we have selected the plan S1012. You can re-generate the proposal with these plans.",
    }
