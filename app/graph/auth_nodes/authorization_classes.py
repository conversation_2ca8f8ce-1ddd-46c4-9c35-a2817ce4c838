from __future__ import annotations
from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field, model_validator
from graph.auth_nodes.constants import GENERAL_NO_AUTH_INTENT_CATEGORIES
from dataclasses import dataclass
from graph.auth_nodes.context_vars import client_id_var
from graph.auth_nodes.helpers import get_client_auth_config

class Subcategory(BaseModel):
    confidence: float = Field(
        ..., 
        description="Confidence score for this intent and details."
    )
    name: str = Field(
        ...,
        description="The name of the subcategory assigned to the user's query."
    )
    details_map: Optional[Dict[str, str]] = Field(  
        ...,
        description="A dictionary of required details for this operation in camel-casing and the values that have been provided by the user. This applies only if is_data_required_from_user is False." 
    )
    is_auth_required: bool = Field(..., description="Whether authorization is required for the assigned subcategory.")
    
class OperationIntent(BaseModel):
    category: str = Field(
        ...,
        description="The intent category assigned to the user's query. Must be one of the allowed values."
    )
    subcategories: List[Subcategory] = Field(
        ...,
        description="A list of assigned subcategories under the category."
    )

    @model_validator(mode="after")
    def check_subcategories(self) -> "OperationIntent":
        if self.category in GENERAL_NO_AUTH_INTENT_CATEGORIES:
            return self
            
        client_id = client_id_var.get()
        auth_map = get_client_auth_config(client_id).get("auth_map", None)
    
        allowed_categories = set()
        allowed_subcategories = {}
        for category, category_data in auth_map.items():
            if "subcategories" in category_data:
                allowed_categories.add(category)
                allowed_subcategories[category] = set(category_data["subcategories"].keys())
        
        if self.category not in allowed_categories:
            raise ValueError(f"Category '{self.category}' is not allowed. Allowed categories: {list(allowed_categories)}")
        
        category_subcategories = allowed_subcategories.get(self.category, set())
        
        invalid_subcategories = [sub.name for sub in self.subcategories if sub.name not in category_subcategories]
        if invalid_subcategories:
            raise ValueError(f"Subcategories {invalid_subcategories} are not allowed for category '{self.category}'. Allowed: {list(category_subcategories)}")
        
        return self

class AuthorizationDetails(BaseModel):
    confidence: float = Field(..., description="Overall confidence score of the response.")

    intents: Optional[List[OperationIntent]] = Field(None, description="A list of parsed intents. This applies only if the response is valid.")
    
    is_auth_required: Optional[bool] = Field(None, description="Whether authorization is required for any of the assigned subcategories.")
    
    is_data_required_from_user: Optional[bool] = Field(None, description="Whether the user is required to provide any more data or if all required data has already been provided for every assigned subcategory.")
    
    message: Optional[str] = Field(None, description="A message that informs the user about the required data they need to provide and specifies which functionality uses that data. Mention only the missing fields that the user has not provided but are required. This applies only if is_data_required_from_user is True.")
    
    error: Optional[str] = Field(None, description="An error message if the user's intent cannot be determined.")
    
    warning: Optional[str] = Field(None, description="A warning message if some low-confidence intents were filtered out.")
    

@dataclass  
class Subcategory: 
    confidence: float 
    name: str  
    details_map: Optional[Dict[str, Any]]  
    is_auth_required: bool 
  
@dataclass  
class OperationIntent:    
    category: str  
    subcategories: List[Subcategory]  
  
@dataclass  
class Result:  
    confidence: float 
    intents: List[OperationIntent]  
    is_auth_required: Optional[bool]  
    is_data_required_from_user: Optional[bool]  
    message: Optional[str]  
    error: Optional[str]  
    warning: Optional[str]
