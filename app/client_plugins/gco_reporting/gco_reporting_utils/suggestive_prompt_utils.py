import httpx
import os

async def get_npr(uuid: str, user_type: str = "External", group_id_list: list[str] = None):
    """
    Fetches data from the NPR API for the given user ID.

    Args:
        user_id (str): The user ID to fetch data for.

    Returns:
        dict: The JSON response from the API or None if an error occurs.
    """
    try:
        base_url = os.environ.get('GCO_PARSER_URL')
        endpoint = base_url + "getusertrackinginfofornpr"
        payload = {"uuid": uuid,
                   "user_type": user_type,
                   "group_id_list": group_id_list}
        client = httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True')
        response = await client.post(endpoint, json=payload)
        if response.status_code == 200:
            return response.json() 
        else:
            return []
    except Exception as e:
        print(f"Error fetching new published report API: {e}")
        return None
    

async def get_mdr(uuid: str):
    """
    Fetches data from the MDR API for the given user ID.

    Args:
        user_id (str): The user ID to fetch data for.

    Returns:
        dict: The JSON response from the API or None if an error occurs.
    """
    try:
        base_url = os.environ.get('GCO_PARSER_URL')
        endpoint = base_url + "getusertrackinginfoformdr"
        payload = {"uuid": uuid}
        client = httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True')
        response = await client.post(endpoint, json=payload)
        if response.status_code == 200:
            return response.json() 
        else:
            return []
    except Exception as e:
        print(f"Error fetching new published report API: {e}")
        return None
    
async def get_rdr(uuid: str):
    """
    Fetches data from the RDR API for the given user ID.

    Args:
        user_id (str): The user ID to fetch data for.

    Returns:
        dict: The JSON response from the API or None if an error occurs.
    """
    try:
        base_url = os.environ.get('GCO_PARSER_URL')
        endpoint = base_url + "getusertrackinginfoforrdr"
        payload = {"uuid": uuid}
        client = httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True')
        response = await client.post(endpoint, json=payload)
        if response.status_code == 200:
            return response.json() 
        else:
            return []
    except Exception as e:
        print(f"Error fetching new published report API: {e}")
        return None
    
 
        