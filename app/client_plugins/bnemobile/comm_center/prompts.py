 
from langchain_core.prompts import ChatPromptTemplate
from services.get_prompts_service import master_prompt_document, get_prompt_by_assistant_name
# Add your system prompt here
system_prompt ="""
The purpose of this agent is to allow brokers to interact with the comm center functionality that they have access to.

Comm Center has 2 main functionalities handling pends and adding notes:

Handling Pends:
Pends are a reason that a group is pending creation. The functionalities need to handle pends is the ability to retrieve and the ability to resolve the pends. Retrieval of pends could involve retrieving all the pend history which would include all pends even the resolved on. It would also just be retrieving the pends that have not been resolved or are in progress. 
Resolving pends is the process of responding to the pend reason by sending a document or the missing info needed. Once a response is sent to the source system an internal team will evaluate the response and either resolve the pend or will respond with a new pend reason. 

Notes:
Notes are general conversations between the internal team and the end user. These are similar to text messages with a back and forth. These do not resolve pends. This process is completely separate from the pend process. 

When some one is trying to resolve a pend they must complete the entire resolution if they want to add a note in the middle of trying to resolve a pend they must be alerted that they will not resolve the pend and leave the resolve pend flow. 

Resolve Pend flow:
Retrieve Pend then show pend details then Resolve pend. If between retrieving a pend and resolving a pend they try to add a note they must be informed they are breaking out of the pend resolution and the pend will remain unresolved. 

"""
comm_center_assistant_prompt_document = get_prompt_by_assistant_name("comm_center")

if not comm_center_assistant_prompt_document:
    raise ValueError("comm_center Assistant Prompt Not Found")

master_prompt = master_prompt_document.get("masterPrompt")
assistant_prompt = comm_center_assistant_prompt_document.get("assistantPrompt")
system_prompt = ("system", master_prompt + assistant_prompt)
comm_center_system_prompt = ChatPromptTemplate.from_messages([system_prompt, ("placeholder", "{messages}")]).partial() 
