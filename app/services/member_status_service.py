from config.db import get_client_database_connection

async def get_member_status(group_id: str, first_name: str, last_name: str, member_id: str, client_id: str):
    db = await get_client_database_connection(client_id)
    collection = db['UspTransactions']
  
    query = {  
        "member_group_id": group_id,  
        "members_status_in_Cirrus.response.messageList.data.nameFirst": {  
        "$regex": f"^{first_name}$",  
        "$options": "i"  
    },  
        "members_status_in_Cirrus.response.messageList.data.nameLast": {  
        "$regex": f"^{last_name}$",  
        "$options": "i"  
    }   
    }  
  
    if member_id and not member_id.isspace():  
        query["members_status_in_Cirrus.response.messageList.data.memberID"] = member_id  
  
    projection = {  
    "members_status_in_Cirrus.employee_status": 1,  
    "members_status_in_Cirrus.response.messageList.data.nameFirst": 1,  
    "members_status_in_Cirrus.response.messageList.data.nameLast": 1,  
    "members_status_in_Cirrus.response.messageList.data.memberID": 1,  
    "members_status_in_Cirrus.response.messageList.data.relationshipCode": 1,  
    "members_status_in_Cirrus.response.messageList.messages": 1  
}  
  
    document = await collection.find(query, projection).to_list(length=None)
    
    if len(document) == 0:
        return "There is no records found for the combination you provided."
    
    if len(document) > 1:
        return "Multiple employees have been found in Cirrus with given combination. Please ask for a member Id to get the information of specific member."
    
    members_status = document[0]['members_status_in_Cirrus']  
    message_list = members_status['response']['messageList']  
    response = [  
        {  
            "memberId": member['data']['memberID'],  
            "firstName": member['data']['nameFirst'],  
            "lastName": member['data']['nameLast'],  
            "type": "Employee" if member['data']['relationshipCode'] == "18" else ("Spouse" if member['data']['relationshipCode'] == "01" else "Child"),  
            "employeeStatus": members_status['employee_status'].capitalize(),  
            "messages": [  
                f"{msg['type'].capitalize()}: {msg['message']}"  
                for msg in member['messages'] if msg['type'] in ['ERROR', 'WARNING']
            ]  
        }  
        for member in message_list  
    ]  
    
    return f"Here is the status of the employee in Cirrus, along with information about their dependents if applicable: {response}. Please note that this is just a status update; no actions can be taken for errors, so such queries will not be entertained. Keep the same response structure every time."