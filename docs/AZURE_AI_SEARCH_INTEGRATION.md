# Azure AI Search Integration

This document provides a comprehensive overview of the Azure AI Search integration for your multi-tenant AI assistant platform, including configuration, usage, and comparison with your existing PostgreSQL setup.

## Quick Start

### 1. Install Dependencies
```bash
pip install azure-search-documents==11.6.0 azure-identity==1.19.0
```

### 2. Configure Environment Variables
```bash
export AZURE_AI_SEARCH_ENDPOINT=https://zkm8aewixx3d7er-acss.search.windows.net
export AZURE_AI_SEARCH_AUTH_METHOD=TOKEN
export AZURE_AI_SEARCH_API_VERSION=2024-07-01
```

### 3. Create Indexes and Migrate Data
```bash
# Create indexes
python scripts/azure_ai_search_migration.py --indexes-only

# Migrate data
python scripts/azure_ai_search_migration.py

# Validate migration
python scripts/azure_ai_search_migration.py --validate-only
```

### 4. Run Performance Comparison
```bash
# Quick test
python scripts/rag_performance_test.py --iterations 3

# Or use API
curl -X POST "http://localhost:8000/rag-comparison/quick-test"
```

## Architecture Overview

### Current PostgreSQL Setup
```
Query → Embedding (text-embedding-3-small) → PostgreSQL pgvector → Cosine Similarity → Results → GPT-4o
```

### New Azure AI Search Setup
```
Query → Embedding (text-embedding-3-small) → Azure AI Search → Vector/Hybrid Search → Results → GPT-4o
```

## Key Components

### 1. Configuration (`config/azure_ai_search.py`)
- **AzureAISearchConfig**: Main configuration class
- **TokenCredential**: Reuses your existing OpenAI token provider
- **AsyncSearchClient**: Async wrapper for Azure SDK
- **AsyncIndexClient**: Async index management

### 2. Index Schemas (`config/azure_ai_search_schemas.py`)
- **Vector Search Configuration**: HNSW algorithm with cosine similarity
- **Semantic Search**: Enhanced search with AI understanding
- **Index Definitions**: Schema for Surest training materials and videos

### 3. Search Service (`services/azure_ai_search_service.py`)
- **Vector Similarity Search**: Direct replacement for PostgreSQL queries
- **Hybrid Search**: Combines vector and text search
- **Performance Logging**: Detailed metrics and monitoring

### 4. Comparison Framework (`services/rag_comparison_service.py`)
- **Side-by-side Testing**: Compare PostgreSQL vs Azure AI Search
- **Performance Metrics**: Response time, accuracy, error rates
- **Quality Analysis**: Result overlap, relevance scoring

## API Endpoints

### Comparison Endpoints
- `POST /rag-comparison/compare` - Single query comparison
- `POST /rag-comparison/batch-compare` - Multiple query comparison
- `GET /rag-comparison/performance-summary` - Performance metrics
- `POST /rag-comparison/quick-test` - Quick validation test

### Example Usage
```bash
# Single comparison
curl -X POST "http://localhost:8000/rag-comparison/compare" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "What are the benefits of Surest health plans?",
    "top_k": 5,
    "include_quality_analysis": true
  }'

# Performance summary
curl "http://localhost:8000/rag-comparison/performance-summary?limit=20"
```

## Tool Integration

### Azure AI Search Tools
New Azure AI Search versions of your existing tools:

- `surest_training_material_qa_azure`
- `surest_training_video_qa_azure`
- `surest_training_material_compare_features_azure`
- `surest_training_material_benefits_or_features_azure`
- `surest_training_material_price_or_percentage_qa_azure`

### Usage Example
```python
from client_plugins.surest.surest.azure_ai_search_tool import surest_training_material_qa_azure

# Use Azure AI Search version
result = await surest_training_material_qa_azure(state, "What are Surest benefits?")
```

## Performance Testing

### Automated Testing
```bash
# Performance test with 5 iterations
python scripts/rag_performance_test.py --test-type performance --iterations 5

# Load test with 10 concurrent requests for 60 seconds
python scripts/rag_performance_test.py --test-type load --concurrent 10 --duration 60
```

### Manual Testing
```python
from services.rag_comparison_service import compare_rag_providers

# Compare providers for a single query
result = await compare_rag_providers("What are Surest health plan benefits?")

print(f"PostgreSQL time: {result.postgresql_metrics.total_time_ms}ms")
print(f"Azure AI Search time: {result.azure_metrics.total_time_ms}ms")
print(f"Result overlap: {result.quality_metrics['result_overlap']['jaccard_similarity']}")
```

## Migration Strategy

### Phase 1: Parallel Testing (Recommended)
1. Keep PostgreSQL as primary
2. Run Azure AI Search in parallel
3. Compare results and performance
4. Gather metrics for decision making

### Phase 2: Gradual Rollout
1. Implement feature flag
2. Route 10% of traffic to Azure AI Search
3. Monitor performance and quality
4. Gradually increase percentage

### Phase 3: Full Migration
1. Switch to Azure AI Search as primary
2. Keep PostgreSQL as fallback
3. Monitor for issues
4. Remove PostgreSQL after validation

## Advanced Features

### Semantic Search
```python
from services.azure_ai_search_service import azure_hybrid_search

# Use hybrid search for better results
results = await azure_hybrid_search(
    index_name="surest-training-material",
    query="Surest health plan benefits",
    top_k=5
)
```

### Custom Scoring
```python
# Search with custom scoring profile
search_results = await search_client.search(
    search_text=query,
    scoring_profile="custom-scoring",
    scoring_parameters=["freshness-2024"]
)
```

### Faceted Search
```python
# Add faceting for filtering
search_results = await search_client.search(
    search_text=query,
    facets=["file_name", "created_at"],
    filter="created_at ge 2024-01-01"
)
```

## Monitoring and Metrics

### Key Performance Indicators
1. **Response Time**: Average, 95th percentile
2. **Throughput**: Requests per second
3. **Error Rate**: Failed requests percentage
4. **Quality Score**: Result relevance and overlap

### Monitoring Setup
```python
# Built-in logging in search service
from services.azure_ai_search_service import azure_similarity_search

# Automatically logs performance metrics
results = await azure_similarity_search(
    index_name="surest-training-material",
    query="benefits",
    state=state  # Enables logging
)
```

## Cost Considerations

### Azure AI Search Pricing
- **Basic Tier**: Good for development and testing
- **Standard Tier**: Production workloads
- **Storage Tier**: Optimized for large datasets

### Cost Optimization Tips
1. Use appropriate field types (searchable vs retrievable)
2. Implement query caching
3. Monitor and optimize index size
4. Use filters to reduce search scope

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   ```bash
   # Check token provider
   python -c "from auth.openAI_auth import get_openai_token; print(get_openai_token())"
   ```

2. **Index Creation Failures**
   ```bash
   # Verify endpoint and credentials
   python scripts/azure_ai_search_migration.py --indexes-only
   ```

3. **Search Performance Issues**
   - Check index size and complexity
   - Monitor query patterns
   - Adjust HNSW parameters

### Debug Mode
```python
# Enable detailed logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Run comparison with debug info
result = await compare_rag_providers("test query")
```

## Best Practices

### Index Design
1. Use appropriate field types
2. Minimize stored fields for performance
3. Configure proper analyzers for text fields
4. Set up semantic search for better relevance

### Query Optimization
1. Use filters to narrow search scope
2. Implement query caching
3. Monitor slow queries
4. Use hybrid search for complex queries

### Error Handling
1. Implement proper fallback mechanisms
2. Log errors with context
3. Monitor error rates
4. Set up alerts for critical failures

## Support and Resources

- **Azure AI Search Documentation**: https://docs.microsoft.com/en-us/azure/search/
- **Python SDK Reference**: https://docs.microsoft.com/en-us/python/api/azure-search-documents/
- **Performance Tuning**: https://docs.microsoft.com/en-us/azure/search/search-performance-optimization

## Next Steps

1. **Review Configuration**: Ensure all environment variables are set
2. **Run Initial Tests**: Use the quick test endpoint
3. **Performance Comparison**: Run comprehensive performance tests
4. **Quality Assessment**: Compare search result quality
5. **Migration Planning**: Plan your migration strategy based on results

For detailed migration instructions, see [AZURE_AI_SEARCH_MIGRATION_GUIDE.md](./AZURE_AI_SEARCH_MIGRATION_GUIDE.md).
