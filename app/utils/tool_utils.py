from langchain_core.messages import SystemMessage, ToolMessage
from services import log_error_to_db_service
from services.event_logger_service import logger
from utils.general_utils import extract_values

def _print_event(event: dict, _printed: set, max_length=1500):
    current_state = extract_values(event.get("dialog_state"))
    if current_state:
        print("Currently in: ", current_state[-1])
    messages = event.get("messages")
    responses = [] 
    if messages:
        for msg in messages:
            if msg.id not in _printed and not isinstance(msg, SystemMessage) and not (isinstance(msg, ToolMessage) and msg.additional_kwargs.get("tool_escalation", False)):  
                msg_repr = msg.pretty_repr(html=True)  
                if len(msg_repr) > max_length:  
                    msg_repr = msg_repr[:max_length] + " ... (truncated)"  
                print(msg_repr)  
                _printed.add(msg.id)  
                responses.append(msg.content)
        # return responses
        return messages[-1].content if isinstance(messages, list) else messages.content
    
async def log_tool_error(state, error, tool_name):
    user = state.get("user_info")
    await logger.error(user.get("uuid"), user.get("user_name"), user.get("session_id"), user.get("request_id", None), user.get("client_id"), "tool", tool_name, str(state["messages"][-1]), None, None, str(error))
    await log_error_to_db_service.log_error_to_db(str(error), user["client_id"], user["session_id"], user["user_name"], f'Tool Error - {tool_name}', None, user["request_id"])
