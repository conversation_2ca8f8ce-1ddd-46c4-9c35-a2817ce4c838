
from langchain_core.tools import tool
from typing import Annotated
import os
import httpx
from datetime import datetime
from typing import Annotated
from utils.tool_utils import log_tool_error
from client_plugins.bnemobile.bnemobile_utils.objects.pend_objects import PendReply,CreatePendNote
from client_plugins.bnemobile.bnemobile_utils.utils import create_header
from langgraph.prebuilt.tool_node import InjectedState
import traceback
from client_plugins.bnemobile.bnemobile_utils.objects.pend_objects import PendReply,CreatePendNote,NewBusinessGroupsFilter
from client_plugins.bnemobile.bnemobile_utils.utils import create_header,createFilters
import traceback
from utils.tool_utils import log_tool_error
from langgraph.prebuilt.tool_node import InjectedState

samx_base_url = os.getenv("SAMX_COMM_CENTER_STARGATE_BASE_URL")
samx_nb_url = os.getenv("SAMX_COMM_CENTER_NB_URL")

@tool 
async def retrieve_quoteId(state: Annotated[dict, InjectedState], pcisIds:list, userInput:NewBusinessGroupsFilter):
    '''
    If a quote id is needed to call a tool but a description of the group is provided this can be used to 
    retrieve the quoteId which is the same as a group id.
    Args:
        pcisIds: pcisIds which is a list of string identifiers for the user not required
        userInput: a message that the user send describing the group and the it must contain the group name, which should be parsed into the 
        NewBusinessGroupFilter object
        NewBusinessGroupFilter:
            caseId: case Identifier not required 
            companyName: group name or company name field not required
            effectiveDate: date that group is effective not required
            groupStatus: status of the group not required
            planType: the type of plan not required
            states: the state code not required
    '''
    try:
        api_url=samx_nb_url
        headers = create_header()

        variables = {
            "page": 0,
            "pageSize":10,
            "bulkFilters":createFilters(userInput.dict()),
            "globalOperator":"AND",
            "pcisIds":pcisIds,

            }
        body={
            "query": "query NewBusinessGroups($page: Int!, $pageSize: Int!, $bulkFilters: [BulkFilter!], $globalOperator: Operator!, $pcisIds: [String!]) { getNbCases(page: $page, pageSize: $pageSize, bulkFilters: $bulkFilters, globalOperator: $globalOperator, pcisIds: $pcisIds) { count cases { bnePlatform externalStatusDesc caseId caseStatus companyName effectiveDate memberGroupId platform userId zipCode fundingType stateCode businessType shoppingId internalStatusDesc} } }",
            "variables": variables
        }
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:

            response = await client.post(api_url,json=body,headers=headers)

            if response.status_code == 200:
                try:
                    return {"toolName": "retrieve_quoteId", "response": response.json()} 
                except ValueError:
                    return "No such groups"
        
    except Exception as e:
        await log_tool_error(state, traceback.format_exc(), "retrieve_pend_reasons")
        raise Exception (traceback.format_exc())

@tool
async def retrieve_pend_reasons(state: Annotated[dict, InjectedState],quoteId:str): # add arguments if required
    '''
    User wants to retrieving pend reasons for current quoteId provided. Will return a list of pend reasons that are associated with that quoteId. 
    If instead of a quote Id the user provides a description of the group which may contain values such as:
    caseId: case Identifier not required 
    companyName: group name or company name field not required
    effectiveDate: date that group is effective not required
    groupStatus: status of the group not required
    planType: the type of plan not required
    states: the state code not required
    then call retrieve_quoteId tool and use the shoppingId in the return of the tool as the quote id for this tool call.
    Args:
        quoteId: this is the group identifier that is unique to the group that exists. The quoteId is a random assortment of letters. 
    '''
    try:
        api_url = samx_base_url+"/getPendGroupReasons"  
        headers = create_header()

        params = {  
            "quoteId": quoteId,  
        }  
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:

            response = await client.get(api_url,params=params,headers=headers )  
        
            if response.status_code == 200:
                try:
                    return {"toolName": "retrieve_pend_reasons", "response": response.json()} 
                except ValueError:
                    return "No pends exist"
        
    except Exception as e:
        await log_tool_error(state, traceback.format_exc(), "retrieve_pend_reasons")
        raise Exception (traceback.format_exc())

    

@tool
async def resolve_pend_reason(state: Annotated[dict, InjectedState],quoteId:str, pend_reply:PendReply): # add arguments if required
    '''
    This is used to resolve a pend for the current user. This could involve submitting a message or file that will 
    resolve the pend reason on the a reason_id. When a user wants to resolve a pend they must provide a reply message and the specific pend they want to address.
    This tool will generally follow a previous tool call that will retrieve the pends. A pend must be selected if there are multiple and the quoteId can be used
    from the previous call. 
    Args:
        quoteId: current quoteId that is being used and looked at.
        pend_reply: this is and object that will be constructed from:
            pend_reason_id: a unique id which is connected to a specific pend with in a group of pends for a given quoteId. 
            reply_message: the response to the pend reason which will be provided by the user
            replied_by: is the current users name or identifier.
    '''
    try:
        api_url = samx_base_url+"/postGroupPendReasonReply" 
        headers = create_header()

        params = {  
            "quote_id": quoteId,
        }
        body = {
            "pend_reason_id":pend_reply.pend_reason_id,
            'reply_message': pend_reply.reply_message, 
            'replied_by': pend_reply.replied_by,
            'replied_on': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        }
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:

            response = await client.post(api_url, json=body, headers=headers, params=params)

            if response.status_code == 200: 
                return {"toolName": "resolve_pend_reason", "response": response.text}

    except Exception as e:
        await log_tool_error(state, traceback.format_exc(), "resolve_pend_reason")
        raise Exception (traceback.format_exc())


@tool
async def retrieve_pend_notes(state: Annotated[dict, InjectedState],quoteId:str): # add arguments if required
    '''
    User wants to retrieving pend notes for current quoteId provided. Will return a list of pend notes that are associated with that quoteId. 
    Args:
        quoteID: The quote id which the pend reasons belong to. 
    '''
    try:
        api_url = samx_base_url+"/getGroupPendNotes"  
        headers = create_header()

        params = {  
            "quote_id": quoteId,  
        }  
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
            response = await client.get(api_url, params=params, headers=headers) 
        
            if response.status_code == 200:
                try:
                    return {"toolName": "retrieve_pend_notes", "response": response.json()}  
                except ValueError:
                    return "No notes exist"

    except Exception as e:
        await log_tool_error(state, traceback.format_exc(), "resolve_pend_reason")
        raise Exception (traceback.format_exc())    

@tool
async def create_pend_note(state: Annotated[dict, InjectedState],pend_note:CreatePendNote): # add arguments if required
    '''
    This is used to create a pend Note for a quote ID. The quoteid will exist in the body of the request
    Args
    pend_note: this is and object that will be constructed from:
        quoteId: the quoteId of that the note will be created for 
        notes: Notes
            Notes is an object composed of:
                    username: the identifier of the current user 
                    noteText: the text for the note that will be created
    '''
    try:
        api_url = samx_base_url+"/postGroupPendNotes" 
        headers = create_header()

        params= {
            "quoteId":pend_note.quoteId
        }
        body = {
            "notes":{
                "username":pend_note.notes.username,
                "noteText":pend_note.notes.noteText,
                "createdAt":datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "submitted":'yes',
            },
            "oldDate": 0,
            "isSubmitted":True
        }

        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
    
            response = await client.post(api_url, params=params, json=body, headers=headers) 
        
            if response.status_code == 200:  
                return {"toolName": "create_pend_note", "response": response.json()}

    except Exception as e:
        await log_tool_error(state, traceback.format_exc(), "create_pend_note")
        raise Exception (traceback.format_exc())
    