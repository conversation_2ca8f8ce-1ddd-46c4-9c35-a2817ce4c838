from langgraph.graph import StateGraph
from graph.state import State, get_postgres_checkpointer
from graph.nodes import primary_assistant_tool_node, pop_dialog_state, restrict_parallel_agents_run, escalation_fallback
from graph.nodes_and_edges_registry import assistants_registry, edges_registry
from utils.graph_utils import add_assistant_nodes, add_edges
from utils.agent_utils import create_permission_node
from graph.auth_nodes.auth_nodes import authorize_and_route_to_workflow
builder = StateGraph(State)

#NOTE: THIS GRAPH IS NOT BEING USED. THIS IS A LEGACY GRAPH

# Define nodes
for assistant_name, assistant_runnable, tool_node, add_extra_tool_node, add_entry_node in assistants_registry:
    extra_tool_suffix = "_with_assistant" if add_extra_tool_node else ""
    add_assistant_nodes(builder, assistant_name, assistant_runnable, tool_node, add_extra_tool_node, extra_tool_suffix, add_entry_node)

# Invalid tool fallback
builder.add_node("invalid_tool", primary_assistant_tool_node)
builder.add_node("escalation_fallback", escalation_fallback)
builder.add_node("leave_skill", pop_dialog_state)
builder.add_node("restrict_parallel_agents_run", restrict_parallel_agents_run)

builder.add_node("permission_check", create_permission_node())
builder.add_node("authorize_and_route_to_workflow", authorize_and_route_to_workflow)
#Define edges

builder.set_entry_point('authorize_and_route_to_workflow')
# builder.set_conditional_entry_point(route_to_workflow)

add_edges(builder, edges_registry)

primary_assistant_graph = None

async def init_graph():
    global primary_assistant_graph
    # Compile the graph with the initialized checkpointer
    primary_assistant_graph = builder.compile(checkpointer=await get_postgres_checkpointer())
