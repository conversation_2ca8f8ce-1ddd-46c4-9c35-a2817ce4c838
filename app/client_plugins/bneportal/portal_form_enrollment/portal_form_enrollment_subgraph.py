from client_plugins.bneportal.portal_form_enrollment.main import portal_form_enrollment_tool_node
from client_plugins.bneportal.portal_form_enrollment.main import portal_form_enrollment_runnable
from client_plugins.bneportal.portal_form_enrollment.router import route_portal_form_enrollment_assistant
from graph.nodes import Assistant
from langgraph.pregel import RetryPolicy
from graph.nodes import pop_dialog_state, escalation_fallback, invalid_tool

from langgraph.graph import START
from graph.state import State, get_postgres_checkpointer
from utils.subgraph_builder import BaseSubgraphBuilder
from utils.subgraph_registry import SubgraphRegistry

portal_form_enrollment_compiled_graph = None

async def init_portal_form_enrollment_graph():
    """
    Initialize the portal form enrollment graph using the BaseSubgraphBuilder pattern
    and register it with SubgraphRegistry.
    """
    global portal_form_enrollment_compiled_graph
    if portal_form_enrollment_compiled_graph is None:
        # Create a new builder with fluent interface
        builder = BaseSubgraphBuilder(State)
        
        # Add nodes
        builder.add_node("portal_form_enrollment_assistant", Assistant(portal_form_enrollment_runnable), retry=RetryPolicy(max_attempts=2))
        builder.add_node("call_portal_form_enrollment_tool", portal_form_enrollment_tool_node)
        builder.add_node("leave_skill", pop_dialog_state)
        builder.add_node("escalation_fallback", escalation_fallback)
        builder.add_node("tool_disabled_handler", invalid_tool)  
        
        # Add edges
        builder.add_edge("leave_skill", "portal_form_enrollment_assistant")
        builder.add_edge("escalation_fallback", "portal_form_enrollment_assistant")
        builder.add_edge(START, "portal_form_enrollment_assistant")
        
        # Add conditional edges
        builder.add_conditional_edges("portal_form_enrollment_assistant", route_portal_form_enrollment_assistant)
        
        # Compile with async checkpointer
        portal_form_enrollment_compiled_graph = builder.compile(checkpointer=await get_postgres_checkpointer())
        
        # Register with SubgraphRegistry for future access
        SubgraphRegistry.register("portal_form_enrollment", portal_form_enrollment_compiled_graph)