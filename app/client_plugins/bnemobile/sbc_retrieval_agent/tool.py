import httpx
from langchain_core.tools import tool
from langgraph.prebuilt.tool_node import InjectedState
import traceback
from typing import Annotated
import json
from config.jwt import get_token
from utils.tool_utils import log_tool_error
import os
generic_error_message = "I'm sorry, I am unable to retrieve that SBC at the moment. Please choose a different plan to or visit the website for help."


@tool("get_sbc")
async def get_sbc(state: Annotated[dict, InjectedState], planId:str, planName:str, groupId: str) -> str:
    """
    This tool is to retrieve the SBC for a user using the corresponding plan and group information. 
    If the user is requesting an sbc for a group, this tool can only be called once a user has selected the plan for the group.
    Plan Ids are different from group IDs.
    
    Args:
        planId: The ID of the plan the user wants to retrieve the SBC for.
        planName: The name of the plan the user wants to retrieve the SBC for.
        groupId: The ID of the group the user is retrieving the SBC for.
    """
    try:
        token = get_token()  
        headers = {  
            "Content-Type": "application/json",  
            "Authorization": "Bearer " + token, 
        }  
        payload = {
            "indexName": "u_cirr_sbc_doc",
            "criteria": {
                "filterClauses": [
                    {
                        "type": "equal",
                        "name": "u_grp_contr_ben_bndl_opt_id",
                        "value": planId
                    },
                    {
                        "type": "equal",
                        "name": "u_grp_id",
                        "value": groupId
                    }
                ]
            }
        }
        find_url = os.environ["DOC360_FIND_URL"]
        
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:  
            find_resp = await client.post(find_url, headers=headers, json=payload, timeout=15)  
            find_resp.raise_for_status()  
            find_doc = find_resp.json()  
                
        records_list = find_doc.get("recordsList", [])  
        if not records_list:  
            return f"I'm sorry, there is no SBC available for plan Id **{planId}**. Please choose a different plan or visit the website for help."  
  
        document_id = records_list[0].get("objectId")   
        if not document_id:  
            return generic_error_message  
  
        # getting document content
        content_id = document_id.split("|")[0].strip()  
        content_url = (  
            os.environ["DOC360_GET_URL"].rstrip("/")  
            + f"/{content_id}?type-name=u_cirr_sbc_doc&enable-base64-output=true" 
        )   
  
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:  
            content_resp = await client.get(content_url, headers=headers, timeout=15)   
            content_resp.raise_for_status()  
            content_doc = content_resp.json()  
  
        content_base64 = content_doc.get("contentStream")  
        if not content_base64:  
            return generic_error_message 
        payload = {  
            "planId": planId,  
            "planName": planName, 
            "temp_data": content_base64,  
            "toolName": "get_sbc"
        }  
        return json.dumps(payload)  
    
    except Exception as e:
        await log_tool_error(state, traceback.print_exc(), "get_sbc")
        raise Exception (traceback.format_exc())

