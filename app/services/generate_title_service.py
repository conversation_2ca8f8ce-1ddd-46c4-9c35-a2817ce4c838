from config.db import get_client_database_connection
from config.openAI import model
import os
from utils.schemas.classes import generateTitleInterface
from langchain_core.messages import HumanMessage
from utils.general_utils import getTime
    
async def summarize_text(user_input, chatbot_response, filename=None):
    if not user_input.strip() or user_input.strip() == "File uploaded!":
        if filename:
            name = os.path.basename(filename)
            base = os.path.splitext(name)[0].replace("_", " ").title()
            return f"Uploaded: {base}"
        else:
            return "File Uploaded"
    shortened_response = " ".join(chatbot_response.split()[:100] ) 
    prompt = f"Generate a title from this query and response in 3-4 words without quotes around title: '{user_input} - {shortened_response}'"
    message = HumanMessage(content=prompt)
    response = await model.ainvoke([message])
    return response.content

async def save_title(input: generateTitleInterface, summary):
    client_db = await get_client_database_connection(input.client_id)
    titles_collection = client_db["ChatTitles"]
    current_time = getTime()
    filter_query = {"uuid": input.uuid, "titles.session_id": input.session_id}
    update_data = {"$set": {"titles.$.title": summary, "update_timestamp": current_time}}
    # Try to update existing title
    result = await titles_collection.update_one(filter_query, update_data)
    # If no document was updated, insert a new one
    if result.matched_count == 0:
        new_title = {
            "session_id": input.session_id,
            "title": summary
        }
        # Try to add to existing user document
        user = await titles_collection.find_one({"uuid": input.uuid})
        if user:
            await titles_collection.update_one(
                {"uuid": input.uuid},
                {
                    "$push": {"titles": new_title},
                    "$set": {"update_timestamp": current_time}
                }
            )
        else:
            # Create a new user document
            new_user = {
                "uuid": input.uuid,
                "titles": [new_title],
                "create_timestamp": current_time,
                "update_timestamp": current_time
            }
            await titles_collection.insert_one(new_user)
