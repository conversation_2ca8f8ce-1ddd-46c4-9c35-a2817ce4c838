from utils.schemas.mapping.cirrus_member_group_mapping import mapping
from services.cirrus.cirrus_api_definitions import group_search_api

async def get_member_group(memberGroupID, uuid, user_name, session_id, request_id, client_id): 
    if not memberGroupID or memberGroupID.isspace():
        return f"{memberGroupID} is not a valid group ID. Please provide a valid Cirrus group ID."
    
    if len(memberGroupID) != 7 and len(memberGroupID) != 8:
        return f"{memberGroupID} is not a valid group ID. A valid Cirrus ID has either seven or eight digits."
    
    response = await group_search_api(memberGroupID, uuid, user_name, session_id, request_id, client_id)

    if (isinstance(response.get('responseData'), dict) and isinstance(response.get('responseData').get('memGroup'), dict)):
        mem_group_dict = response['responseData']['memGroup']
        final_response = mapping(mem_group_dict)          
    else: 
        meta_info = response.get('metaInformation')
        if (isinstance(meta_info.get('responseMessageList'), list)):
            error_data =meta_info.get('responseMessageList') 
            if (len(error_data)>=1 and error_data[0].get('type')=='ERROR'):
                final_response = error_data[0].get('message') + f" for Member group ID: {memberGroupID}"
        else:
            final_response = f'No matches found for Member group ID: {memberGroupID}'
    return final_response