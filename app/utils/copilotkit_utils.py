from copilotkit import CopilotKitContext, CopilotKitRemoteEndpoint, LangGraphAgent
from copilotkit.integrations.fastapi import add_fastapi_endpoint
from fastapi import FastAPI
from utils.helpers.constants import OEC_BNEMOBILE_CLIENT_ID, OEC_SAMXONE_CLIENT_ID
from utils.subgraph_registry import SubgraphRegistry

def get_copilotkit_agents_from_context(context: CopilotKitContext):
    """
    Returns a list of LangGraphAgent instances based on the client_id found in the context properties.

    Args:
        context (dict): The context dictionary, expected to contain a 'properties' key with a 'client_id'.

    Returns:
        list[LangGraphAgent]: A list of LangGraphAgent objects corresponding to the client_id.
    """
    client_id = context.get("properties", {}).get("client_id")
    if client_id == OEC_BNEMOBILE_CLIENT_ID:
        return [
            LangGraphAgent(
                name="bnemobile",
                graph=SubgraphRegistry.get("bnemobile"),
            ),
            LangGraphAgent(
                name="comm_center",
                graph=SubgraphRegistry.get("comm_center"),
            ),
            LangGraphAgent(
                name="sbc_retrieval_agent",
                graph=SubgraphRegistry.get("sbc_retrieval_agent"),
            ),
        ]
    elif client_id == OEC_SAMXONE_CLIENT_ID:
        return [
            LangGraphAgent(
                name="samxone_multiagent",
                graph=SubgraphRegistry.get("samxone_multiagent"),
            ),
            LangGraphAgent(
                name="samx_one",
                graph=SubgraphRegistry.get("samx_one"),
            ),
            LangGraphAgent(
                name="surest",
                graph=SubgraphRegistry.get("surest"),
            ),
        ]
    else:
        return []

def add_copilotkit_endpoint(app: FastAPI):
    """
    Adds the CopilotKit endpoint to the provided FastAPI app.

    The endpoint is mounted at '/copilotkit' and uses the default async handler (use_thread_pool=False).
    This function enables the FastAPI app to serve CopilotKit requests with agent selection based on context.

    Args:
        app (FastAPI): The FastAPI application instance to which the CopilotKit endpoint will be added.
    """
    copilotkit_endpoint = CopilotKitRemoteEndpoint(
        agents=get_copilotkit_agents_from_context,
    )
    add_fastapi_endpoint(app, copilotkit_endpoint, "/copilotkit", use_thread_pool=False)
