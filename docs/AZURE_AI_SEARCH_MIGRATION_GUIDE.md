# Azure AI Search Migration Guide

This guide provides a comprehensive strategy for migrating from PostgreSQL pgvector to Azure AI Search for your multi-tenant AI assistant platform.

## Overview

Your current setup uses:
- PostgreSQL with pgvector for vector storage
- Azure OpenAI (text-embedding-3-small + GPT-4o)
- Cosine similarity search with embedding <-> vector queries

The migration to Azure AI Search will provide:
- Enhanced search capabilities with hybrid and semantic search
- Better scalability and managed infrastructure
- Advanced features like faceting, filtering, and autocomplete
- Integrated AI capabilities

## Prerequisites

### Environment Variables

Add these environment variables to your configuration:

```bash
# Azure AI Search Configuration
AZURE_AI_SEARCH_ENDPOINT=https://zkm8aewixx3d7er-acss.search.windows.net
AZURE_AI_SEARCH_API_VERSION=2024-07-01
AZURE_AI_SEARCH_AUTH_METHOD=TOKEN  # or KEY
AZURE_AI_SEARCH_API_KEY=your_api_key_if_using_key_auth
```

### Dependencies

The following packages have been added to `requirements.txt`:
- `azure-search-documents==11.6.0`
- `azure-identity==1.19.0`

Install them:
```bash
pip install azure-search-documents==11.6.0 azure-identity==1.19.0
```

## Migration Strategy

### Phase 1: Setup and Index Creation

1. **Create Azure AI Search Indexes**
   ```bash
   python scripts/azure_ai_search_migration.py --indexes-only
   ```

2. **Verify Index Creation**
   - Check Azure portal for created indexes
   - Verify schema matches your data structure

### Phase 2: Data Migration

1. **Full Data Migration**
   ```bash
   python scripts/azure_ai_search_migration.py
   ```

2. **Validate Migration**
   ```bash
   python scripts/azure_ai_search_migration.py --validate-only
   ```

### Phase 3: A/B Testing

1. **Start Comparison Testing**
   ```bash
   python scripts/rag_performance_test.py --test-type performance --iterations 5
   ```

2. **Use API Endpoints for Testing**
   ```bash
   # Quick test
   curl -X POST "http://localhost:8000/rag-comparison/quick-test"
   
   # Custom comparison
   curl -X POST "http://localhost:8000/rag-comparison/compare" \
     -H "Content-Type: application/json" \
     -d '{"query": "What are the benefits of Surest health plans?", "top_k": 5}'
   ```

### Phase 4: Gradual Rollout

1. **Feature Flag Implementation**
   - Add feature flag to control which search provider to use
   - Start with 10% traffic to Azure AI Search
   - Monitor performance and quality metrics

2. **Monitor Key Metrics**
   - Response time
   - Search quality (result overlap)
   - Error rates
   - User satisfaction

### Phase 5: Full Migration

1. **Switch Primary Provider**
   - Update tool implementations to use Azure AI Search
   - Keep PostgreSQL as fallback initially

2. **Cleanup**
   - Remove PostgreSQL vector search code after validation
   - Update documentation and monitoring

## Implementation Details

### 1. Azure AI Search Configuration

The configuration is handled in `config/azure_ai_search.py`:

```python
from config.azure_ai_search import get_async_search_client

# Get search client for an index
client = get_async_search_client("surest-training-material")
```

### 2. Index Schemas

Index schemas are defined in `config/azure_ai_search_schemas.py`:
- `surest-training-material`: Main training content
- `surest-training-video`: Video content
- `bne-training-document`: BNE documents

### 3. Search Service

The search service in `services/azure_ai_search_service.py` provides:
- Vector similarity search
- Hybrid search (vector + text)
- Semantic search capabilities
- Performance logging

### 4. Tool Integration

Azure AI Search versions of tools are in `client_plugins/surest/surest/azure_ai_search_tool.py`:
- `surest_training_material_qa_azure`
- `surest_training_video_qa_azure`
- `surest_training_material_compare_features_azure`
- And more...

## Performance Comparison

### Running Comparisons

1. **Single Query Comparison**
   ```python
   from services.rag_comparison_service import compare_rag_providers
   
   result = await compare_rag_providers("What are Surest benefits?")
   ```

2. **Batch Testing**
   ```bash
   python scripts/rag_performance_test.py --iterations 10
   ```

3. **Load Testing**
   ```bash
   python scripts/rag_performance_test.py --test-type load --concurrent 10 --duration 120
   ```

### Key Metrics to Monitor

1. **Performance Metrics**
   - Average response time
   - 95th percentile response time
   - Throughput (requests per second)
   - Error rates

2. **Quality Metrics**
   - Result overlap (Jaccard similarity)
   - Relevance scores
   - User feedback scores

3. **Cost Metrics**
   - Azure AI Search costs vs PostgreSQL hosting costs
   - Operational overhead

## Migration Checklist

### Pre-Migration
- [ ] Set up Azure AI Search service
- [ ] Configure authentication (token-based preferred)
- [ ] Install required dependencies
- [ ] Create and test indexes
- [ ] Migrate sample data for testing

### During Migration
- [ ] Run comprehensive performance tests
- [ ] Compare search quality between providers
- [ ] Test error handling and fallback scenarios
- [ ] Validate all tool integrations
- [ ] Monitor system performance

### Post-Migration
- [ ] Update monitoring and alerting
- [ ] Document new search capabilities
- [ ] Train team on Azure AI Search features
- [ ] Plan for advanced features (semantic search, etc.)
- [ ] Clean up old PostgreSQL vector code

## Advanced Features

### Semantic Search

Azure AI Search provides semantic search capabilities:

```python
# Enable semantic search in queries
search_results = await search_client.search(
    search_text=query,
    query_type="semantic",
    semantic_configuration_name="default-semantic-config"
)
```

### Hybrid Search

Combine vector and text search for better results:

```python
from services.azure_ai_search_service import azure_hybrid_search

results = await azure_hybrid_search(
    index_name="surest-training-material",
    query="Surest benefits",
    top_k=5
)
```

### Faceted Search

Add faceting for better filtering:

```python
search_results = await search_client.search(
    search_text=query,
    facets=["file_name", "created_at"]
)
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify token provider is working
   - Check Azure AD permissions
   - Validate endpoint URL

2. **Index Creation Failures**
   - Check field definitions
   - Verify vector dimensions (1536 for text-embedding-3-small)
   - Ensure proper permissions

3. **Search Performance Issues**
   - Monitor index size and complexity
   - Adjust HNSW parameters if needed
   - Consider index partitioning for large datasets

4. **Data Migration Issues**
   - Verify embedding format conversion
   - Check for null/empty values
   - Monitor batch upload limits

### Monitoring and Alerts

Set up monitoring for:
- Search latency
- Index size and document count
- Error rates
- Cost metrics

## Cost Optimization

1. **Index Optimization**
   - Use appropriate field types
   - Minimize stored fields
   - Consider index partitioning

2. **Query Optimization**
   - Use filters to reduce search scope
   - Optimize vector dimensions if possible
   - Cache frequent queries

3. **Scaling Strategy**
   - Start with basic tier for testing
   - Scale up based on performance requirements
   - Monitor usage patterns

## Support and Resources

- Azure AI Search Documentation: https://docs.microsoft.com/en-us/azure/search/
- Performance Best Practices: https://docs.microsoft.com/en-us/azure/search/search-performance-optimization
- Pricing Calculator: https://azure.microsoft.com/en-us/pricing/calculator/

## Next Steps

1. Review this migration guide with your team
2. Set up development environment for testing
3. Run initial performance comparisons
4. Plan migration timeline based on results
5. Implement gradual rollout strategy

For questions or issues during migration, refer to the troubleshooting section or consult the Azure AI Search documentation.
