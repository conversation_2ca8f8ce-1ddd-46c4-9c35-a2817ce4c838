from services.get_prompts_service import master_prompt_document, get_prompt_by_assistant_name
from langchain_core.prompts import ChatPromptTemplate

nonauth_documents_search_prompt_document = get_prompt_by_assistant_name("nonauth_documents_search")

if not nonauth_documents_search_prompt_document:
    raise ValueError("BNE Non Auth Document Search Agent: Prompt Not Found")

master_prompt = master_prompt_document.get("masterPrompt")
assistant_prompt = nonauth_documents_search_prompt_document.get("assistantPrompt")

# Add your system prompt here
nonauth_documents_search_system_prompt = ChatPromptTemplate.from_messages(
    [
        ("system", f"{master_prompt}\n\n{assistant_prompt}"),
        ("placeholder", '{messages}'),
    ]
).partial()
