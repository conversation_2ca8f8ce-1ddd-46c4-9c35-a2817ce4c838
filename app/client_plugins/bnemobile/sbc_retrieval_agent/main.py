from graph.state import State
from graph.nodes import Assistant
from config.openAI import model
from langgraph.graph import StateGraph, START
from langgraph.prebuilt import tools_condition
from typing import Literal
from langchain_core.messages import ToolMessage

from langgraph.prebuilt import ToolNode
from tools.common_tools import CompleteOrEscalate
from utils.helpers.constants import TOOL_CALL_ERROR_MESSAGE
from client_plugins.bnemobile.sbc_retrieval_agent.prompts import sbc_retrieval_agent_system_prompt
from tools.cirrus_assistant_tools.safe_tools import fetch_group_contract_details, fetch_member_details
from client_plugins.bnemobile.sbc_retrieval_agent.tool import get_sbc

sbc_retrieval_agent_tools = [fetch_group_contract_details, fetch_member_details, get_sbc] + [CompleteOrEscalate]
sbc_retrieval_agent_tool_node = ToolNode(sbc_retrieval_agent_tools, handle_tool_errors = TOOL_CALL_ERROR_MESSAGE)

sbc_retrieval_agent_runnable = sbc_retrieval_agent_system_prompt | model.bind_tools(sbc_retrieval_agent_tools)