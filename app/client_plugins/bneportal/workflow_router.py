from utils.general_utils import extract_values
from tools.primary_assistant_tools.router_tools import ToDocumentSearchAssistant, ToPortalFormEnrollmentAssistant, ToCompensationAssistant, ToServiceTrackerAssistant
from client_plugins.gco_reporting.gco_reporting.gco_reporting_router_tools import ToGCOReportingAssistant
from graph.state import State
from typing import Literal
from services.event_logger_service import logger
from utils.general_utils import validate_tool_call
from langgraph.prebuilt import tools_condition
from langgraph.graph import END
from tools.primary_assistant_tools.router_tools import router_tools
from services.get_client_ids_and_assistants_service import validate_assistant

async def route_to_workflow(
    state: State,
) -> Literal[
    "primary",
    "portal_form_enrollment_subgraph",
    "document_search_subgraph",
    "gco_reporting_subgraph",
    "compensation_subgraph",
    "service_tracker_subgraph"
]:
    """If we are in a delegated state, route directly to the appropriate assistant."""
    dialog_state = state.get("dialog_state")
    dialog_state = extract_values(dialog_state)
    #INFO: Log agent traversal
    uuid = state.get("user_info")["uuid"]
    user_name = state.get("user_info").get("user_name", None)
    session_id = state.get("user_info")["session_id"]
    request_id = state.get("user_info").get("request_id", None)
    client_id = state.get("user_info")["client_id"]
    await logger.info(uuid, user_name, session_id, request_id, client_id, "router", "route_to_workflow", "State", dialog_state[-1] if dialog_state else "primary", None, None)
    if not dialog_state:
        return "primary"
    
    return dialog_state[-1]

async def route_bne_portal_assistant(
    state: State,
):  
    bne_user_type = state.get("additional_arg", {}).get("bne_user_type")
    available_tools = [ToDocumentSearchAssistant, ToPortalFormEnrollmentAssistant, ToGCOReportingAssistant, ToCompensationAssistant, ToServiceTrackerAssistant]
    route = tools_condition(state)
    if route == END:
        return END
    if route == "tools" and validate_tool_call(state["messages"][-1].tool_calls, available_tools):
        tool_calls = state["messages"][-1].tool_calls
        if len(tool_calls) > 1:
            return "restrict_parallel_agents_run"
        
        if not await validate_assistant(state.get("user_info")["client_id"], tool_calls[0]["name"]):
            uuid = state.get("user_info")["uuid"]
            user_name = state.get("user_info").get("user_name", None)
            session_id = state.get("user_info")["session_id"]
            request_id = state.get("user_info").get("request_id", None)
            client_id = state.get("user_info")["client_id"]
            await logger.error(uuid, user_name, session_id, request_id, client_id, "router", "route_bne_portal_assistant", "State", tool_calls[0]["name"], None, 400, "Routed agent is disabled.")
            return "invalid_assistant"
        
        if tool_calls[0]["name"] == ToGCOReportingAssistant.__name__ and bne_user_type.lower() != "internal":
            return "invalid_user_type_fallback"
        
        if tool_calls[0]["name"] in router_tools:
            uuid = state.get("user_info")["uuid"]
            user_name = state.get("user_info").get("user_name", None)
            session_id = state.get("user_info")["session_id"]
            request_id = state.get("user_info").get("request_id", None)
            client_id = state.get("user_info")["client_id"]
            await logger.info(uuid, user_name, session_id, request_id, client_id, "router", "route_bne_portal_assistant", "State", tool_calls[0]["name"], None, None)
        if tool_calls[0]["name"] == ToDocumentSearchAssistant.__name__ :
            return "enter_document_search_assistant"
        elif tool_calls[0]["name"] == ToPortalFormEnrollmentAssistant.__name__ :
            return "enter_portal_form_enrollment_assistant"
        elif tool_calls[0]["name"] == ToGCOReportingAssistant.__name__ :
            return "enter_gco_reporting_assistant"
        elif tool_calls[0]["name"] == ToCompensationAssistant.__name__ :
            return "enter_compensation_assistant"
        elif tool_calls[0]["name"] == ToServiceTrackerAssistant.__name__ :
            return "enter_service_tracker_assistant"
    elif route == "tools":
        return "invalid_tool"
    return "primary"