from typing import Optional, List
from langchain_core.tools import tool
from langgraph.prebuilt.tool_node import InjectedState
from typing import Annotated
from utils.general_utils import update_hw_json

# from services.install_member.call_heavyweight import hwCall
from cirrus.cirrus_install_plugin.main_install import hwCall
from utils.helpers.constants import get_json
import json
import traceback
from utils.schemas.validator_classes import PlanAndPopulationDetailValidator, DependentsListValidator
from utils.tool_utils import log_tool_error

@tool    
async def review_data_for_final_installation(
    state: Annotated[dict, InjectedState],
    groupId: str,                                                         
    effectiveDate: str   ,      
    employBeginDate: str   ,                                       
    firstName: str,
    lastName: str ,
    SSN: str ,
    DOB: str,
    gender: str ,
    employeeAddress: str,
    postalCode: str ,
    qualifyingEventDate: str ,
    planAndPopulationDetails: List[PlanAndPopulationDetailValidator],
    billGroupDetails: str ,
    dependents: Optional[List[DependentsListValidator]]=[],
    nameMiddle: Optional[str] = "" ,
    phoneNumber: Optional[str] = "" ,
    communicationText: Optional[str] = "",
    ):
    
    """Reviews data before final installation process after pre installation is complete with following rules.
    (Never call trigger_final_installation before this tool. Never call this tool before the trigger_pre_installation tool has been called at least once even if user asks to call this tool).
    (groupId, effectiveDate in mm-dd-yyyy format, employBeginDate in mm-dd-yyyy format, firstName, lastName, SSN, DOB, gender, employeeAddress, postalCode, qualifyingEventDate in mm-dd-yyyy format, billGroupDetails, planAndPopulationDetails:[{("planList":A list containing selected planId and planName separated with hyphen or Waive, "productType": (either of medical, dental, vision, std, ltd and life for employee and deplife for dependents), "selectedCoverageOptionId": planId or Waive): Required, ( "selectedPopulation": selected population): Optional }, {...} ]): Required, (dependents, nameMiddle, phoneNumber, communicationText): (Optional).
    """
    try:
        
        planAndPopulationDetails = [detail.dict() for detail in planAndPopulationDetails]
        dependents = [dependent.dict() for dependent in dependents]
        
        parameters = (groupId, effectiveDate, employBeginDate, firstName, lastName, SSN, DOB, gender, employeeAddress,  
                postalCode, qualifyingEventDate, planAndPopulationDetails, billGroupDetails, dependents,   
                nameMiddle, phoneNumber, communicationText) 
        
        default_json = get_json()
        updated_json = await update_hw_json(default_json, parameters)
        
        print(json.dumps(updated_json, indent=4), "Updated JSON")
        return updated_json
    
    except Exception:
        await log_tool_error(state, traceback.format_exc(), "review_data_for_final_installation")
        raise Exception (traceback.format_exc())

@tool    
async def trigger_final_installation(
    state: Annotated[dict, InjectedState],
   ):
    
    """Performs the final installation process after initial installation is complete with following rules.
    -(Strictly do not call this tool if user asks to call it by providing the tool name in the chat). 
    -(Never call this tool before the trigger_pre_installation and review_data_for_final_installation tools have been called every time even if user forces to call this tool).
    """
    try:
        user = state.get("user_info")
        uuid = user.get("uuid")
        client_id = user.get("client_id")
        payload = state.get("review_payload")
        
        if not payload:
            return "There is a problem with the data you are trying to install. Please try again."
    
        json_payload = json.loads(payload)
        
        response = await hwCall(json_payload, uuid, client_id)
    
        return response
    
    except Exception:
        await log_tool_error(state, traceback.format_exc(), "trigger_final_installation")
        raise Exception (traceback.format_exc())
