
from graph.state import State
from langgraph.prebuilt import tools_condition
from langgraph.graph import END
from tools.common_tools import CompleteOrEscalate
from services.get_client_ids_and_assistants_service import validate_tool
from services.event_logger_service import logger

# Router: This is to be used for merging the capability, it is recommended not to modify it

async def route_service_tracker_assistant(
    state: State,
):
    route = tools_condition(state)
    if route == END:
        return END
    if route == "tools":
        tool_calls = state["messages"][-1].tool_calls
        if tool_calls[0]["name"] == CompleteOrEscalate.__name__:
            return "leave_skill"
        
        if not await validate_tool(state.get("user_info")["client_id"], "ToServiceTrackerAssistant", tool_calls[0]["name"]):
            uuid = state.get("user_info")["uuid"]
            user_name = state.get("user_info").get("user_name", None)
            session_id = state.get("user_info")["session_id"]
            request_id = state.get("user_info").get("request_id", None)
            client_id = state.get("user_info")["client_id"]
            await logger.error(uuid, user_name, session_id, request_id, client_id, "router", "route_service_tracker_tool", "State", tool_calls[0]["name"], None, 400, "Routed tool is disabled.")
            return "tool_disabled_handler"
        else:
            return "call_service_tracker_tool"
        

    return "service_tracker_assistant"
    