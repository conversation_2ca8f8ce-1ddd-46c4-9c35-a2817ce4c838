#!/usr/bin/env python3
"""
Azure AI Search Migration Script

This script handles:
1. Creating Azure AI Search indexes
2. Migrating data from PostgreSQL to Azure AI Search
3. Validating the migration
"""

import asyncio
import sys
import os
import json
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional
import traceback

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'app'))

from config.azure_ai_search import get_async_index_client, get_async_search_client
from config.azure_ai_search_schemas import get_all_index_schemas, get_index_schema
from config.postgres import get_pg_connection, return_pg_connection
from client_plugins.surest.surest_consts import (
    SUREST_TRAINING_MATERIAL_TABLE_NAME,
    SUREST_TRAINING_MATERIAL_VIDEO_TABLE_NAME
)

class AzureAISearchMigrator:
    """Handles migration from PostgreSQL to Azure AI Search"""
    
    def __init__(self):
        self.index_client = get_async_index_client()
        self.migration_stats = {
            'indexes_created': 0,
            'documents_migrated': 0,
            'errors': []
        }
    
    async def create_all_indexes(self, force_recreate: bool = False):
        """Create all Azure AI Search indexes"""
        print("Creating Azure AI Search indexes...")
        
        schemas = get_all_index_schemas()
        
        for index_name, index_schema in schemas.items():
            try:
                print(f"Creating index: {index_name}")
                
                if force_recreate:
                    try:
                        await self.index_client.delete_index(index_name)
                        print(f"Deleted existing index: {index_name}")
                    except Exception as e:
                        print(f"Index {index_name} doesn't exist or couldn't be deleted: {e}")
                
                result = await self.index_client.create_or_update_index(index_schema)
                print(f"Successfully created/updated index: {index_name}")
                self.migration_stats['indexes_created'] += 1
                
            except Exception as e:
                error_msg = f"Failed to create index {index_name}: {str(e)}"
                print(error_msg)
                self.migration_stats['errors'].append(error_msg)
    
    async def migrate_surest_training_material(self):
        """Migrate Surest training material from PostgreSQL to Azure AI Search"""
        print("Migrating Surest training material...")
        
        pg_connection = None
        try:
            # Get PostgreSQL connection
            pg_connection = await get_pg_connection()
            cursor = pg_connection.cursor()
            
            # Query all documents from PostgreSQL
            query = f"""
            SELECT file_name, file_url, page_content, created_at, embedding
            FROM {SUREST_TRAINING_MATERIAL_TABLE_NAME}
            ORDER BY created_at DESC
            """
            
            await cursor.execute(query)
            results = await cursor.fetchall()
            
            if not results:
                print("No documents found in PostgreSQL table")
                return
            
            print(f"Found {len(results)} documents to migrate")
            
            # Get Azure AI Search client
            search_client = get_async_search_client("surest-training-material")
            
            # Prepare documents for Azure AI Search
            documents = []
            for row in results:
                file_name, file_url, page_content, created_at, embedding = row
                
                # Convert embedding from PostgreSQL format to list
                if isinstance(embedding, str):
                    # Handle string representation of vector
                    embedding_list = json.loads(embedding.replace('[', '[').replace(']', ']'))
                else:
                    # Handle direct list/array
                    embedding_list = list(embedding)
                
                doc = {
                    "id": str(uuid.uuid4()),
                    "file_name": file_name,
                    "file_url": file_url or "",
                    "page_content": page_content or "",
                    "created_at": created_at.isoformat() if created_at else datetime.now().isoformat(),
                    "embedding": embedding_list,
                    "score": 0.0  # Default score
                }
                documents.append(doc)
            
            # Upload documents in batches
            batch_size = 100
            for i in range(0, len(documents), batch_size):
                batch = documents[i:i + batch_size]
                try:
                    result = await search_client.upload_documents(batch)
                    print(f"Uploaded batch {i//batch_size + 1}: {len(batch)} documents")
                    self.migration_stats['documents_migrated'] += len(batch)
                except Exception as e:
                    error_msg = f"Failed to upload batch {i//batch_size + 1}: {str(e)}"
                    print(error_msg)
                    self.migration_stats['errors'].append(error_msg)
            
        except Exception as e:
            error_msg = f"Failed to migrate Surest training material: {str(e)}"
            print(error_msg)
            self.migration_stats['errors'].append(error_msg)
            traceback.print_exc()
        finally:
            if pg_connection:
                await return_pg_connection(pg_connection)
    
    async def migrate_surest_training_videos(self):
        """Migrate Surest training videos from PostgreSQL to Azure AI Search"""
        print("Migrating Surest training videos...")
        
        pg_connection = None
        try:
            # Get PostgreSQL connection
            pg_connection = await get_pg_connection()
            cursor = pg_connection.cursor()
            
            # Query all documents from PostgreSQL
            query = f"""
            SELECT file_name, file_url, page_content, created_at, embedding
            FROM {SUREST_TRAINING_MATERIAL_VIDEO_TABLE_NAME}
            ORDER BY created_at DESC
            """
            
            await cursor.execute(query)
            results = await cursor.fetchall()
            
            if not results:
                print("No video documents found in PostgreSQL table")
                return
            
            print(f"Found {len(results)} video documents to migrate")
            
            # Get Azure AI Search client
            search_client = get_async_search_client("surest-training-video")
            
            # Prepare documents for Azure AI Search
            documents = []
            for row in results:
                file_name, file_url, page_content, created_at, embedding = row
                
                # Convert embedding from PostgreSQL format to list
                if isinstance(embedding, str):
                    embedding_list = json.loads(embedding.replace('[', '[').replace(']', ']'))
                else:
                    embedding_list = list(embedding)
                
                doc = {
                    "id": str(uuid.uuid4()),
                    "file_name": file_name,
                    "file_url": file_url or "",
                    "page_content": page_content or "",
                    "created_at": created_at.isoformat() if created_at else datetime.now().isoformat(),
                    "embedding": embedding_list,
                    "score": 0.0
                }
                documents.append(doc)
            
            # Upload documents in batches
            batch_size = 100
            for i in range(0, len(documents), batch_size):
                batch = documents[i:i + batch_size]
                try:
                    result = await search_client.upload_documents(batch)
                    print(f"Uploaded video batch {i//batch_size + 1}: {len(batch)} documents")
                    self.migration_stats['documents_migrated'] += len(batch)
                except Exception as e:
                    error_msg = f"Failed to upload video batch {i//batch_size + 1}: {str(e)}"
                    print(error_msg)
                    self.migration_stats['errors'].append(error_msg)
            
        except Exception as e:
            error_msg = f"Failed to migrate Surest training videos: {str(e)}"
            print(error_msg)
            self.migration_stats['errors'].append(error_msg)
            traceback.print_exc()
        finally:
            if pg_connection:
                await return_pg_connection(pg_connection)
    
    async def validate_migration(self):
        """Validate the migration by comparing document counts"""
        print("Validating migration...")
        
        pg_connection = None
        try:
            # Get PostgreSQL connection
            pg_connection = await get_pg_connection()
            cursor = pg_connection.cursor()
            
            # Count documents in PostgreSQL
            await cursor.execute(f"SELECT COUNT(*) FROM {SUREST_TRAINING_MATERIAL_TABLE_NAME}")
            pg_material_count = (await cursor.fetchone())[0]
            
            await cursor.execute(f"SELECT COUNT(*) FROM {SUREST_TRAINING_MATERIAL_VIDEO_TABLE_NAME}")
            pg_video_count = (await cursor.fetchone())[0]
            
            print(f"PostgreSQL - Training Material: {pg_material_count}, Videos: {pg_video_count}")
            
            # Count documents in Azure AI Search
            material_client = get_async_search_client("surest-training-material")
            video_client = get_async_search_client("surest-training-video")
            
            # Note: Azure AI Search doesn't have a direct count API, so we'll search with empty query
            material_results = await material_client.search("*", include_total_count=True, top=0)
            video_results = await video_client.search("*", include_total_count=True, top=0)
            
            azure_material_count = material_results.get_count() if hasattr(material_results, 'get_count') else 0
            azure_video_count = video_results.get_count() if hasattr(video_results, 'get_count') else 0
            
            print(f"Azure AI Search - Training Material: {azure_material_count}, Videos: {azure_video_count}")
            
            # Validation summary
            if pg_material_count == azure_material_count and pg_video_count == azure_video_count:
                print("✅ Migration validation successful - document counts match!")
            else:
                print("⚠️  Migration validation warning - document counts don't match")
                
        except Exception as e:
            error_msg = f"Failed to validate migration: {str(e)}"
            print(error_msg)
            self.migration_stats['errors'].append(error_msg)
            traceback.print_exc()
        finally:
            if pg_connection:
                await return_pg_connection(pg_connection)
    
    async def run_full_migration(self, force_recreate: bool = False):
        """Run the complete migration process"""
        print("Starting Azure AI Search migration...")
        start_time = datetime.now()
        
        try:
            # Step 1: Create indexes
            await self.create_all_indexes(force_recreate)
            
            # Step 2: Migrate data
            await self.migrate_surest_training_material()
            await self.migrate_surest_training_videos()
            
            # Step 3: Validate migration
            await self.validate_migration()
            
        except Exception as e:
            print(f"Migration failed: {str(e)}")
            traceback.print_exc()
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # Print migration summary
        print("\n" + "="*50)
        print("MIGRATION SUMMARY")
        print("="*50)
        print(f"Duration: {duration}")
        print(f"Indexes created: {self.migration_stats['indexes_created']}")
        print(f"Documents migrated: {self.migration_stats['documents_migrated']}")
        print(f"Errors: {len(self.migration_stats['errors'])}")
        
        if self.migration_stats['errors']:
            print("\nErrors encountered:")
            for error in self.migration_stats['errors']:
                print(f"  - {error}")
        
        print("="*50)

async def main():
    """Main function to run the migration"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Azure AI Search Migration Tool')
    parser.add_argument('--force-recreate', action='store_true', 
                       help='Force recreate indexes (deletes existing)')
    parser.add_argument('--indexes-only', action='store_true',
                       help='Only create indexes, skip data migration')
    parser.add_argument('--validate-only', action='store_true',
                       help='Only run validation, skip creation and migration')
    
    args = parser.parse_args()
    
    migrator = AzureAISearchMigrator()
    
    if args.validate_only:
        await migrator.validate_migration()
    elif args.indexes_only:
        await migrator.create_all_indexes(args.force_recreate)
    else:
        await migrator.run_full_migration(args.force_recreate)

if __name__ == "__main__":
    asyncio.run(main())
