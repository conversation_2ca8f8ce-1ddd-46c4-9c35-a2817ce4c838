import base64
import json
import os
import httpx
import logging
import time
import jwt
from fastapi import HTTPException
from utils.helpers.constants import OEC_DEFAULT_CLIENT_ID, OEC_OPTUMAI_CLIENT_ID
from utils.helpers.external_client_public_key import EXTERNAL_CLIENT_PUBLIC_KEY, OPTUMAI_CLIENT_PUBLIC_KEY

def decode_jwt(token: str) -> dict:
    try:
        header, payload, signature = token.split(".")
        header_decoded = base64.urlsafe_b64decode(header + "==").decode('utf-8')
        payload_decoded = base64.urlsafe_b64decode(payload + "==").decode('utf-8')
        return json.loads(payload_decoded)
    except Exception as e:
        raise Exception("Invalid JWT Token")

async def ohid_authenticate_user(code: str, grant_type: str) -> dict:
    try:
        payload = {
            "grant_type": grant_type,
            "code": code,
            "redirect_uri": os.getenv('OIDC_REDIRECT_URI'),
            "client_id": os.getenv('OHID_CLIENT_ID'),
            "client_secret": os.getenv('OHID_SECRET'),
        }
        
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
            response = await client.post(os.getenv('OPTUM_OHID_SSO_URL'), data=payload)
            if response.status_code == 200:
                token_data = response.json()

                access_token = token_data.get('access_token')
                id_token = token_data.get('id_token')
                if not access_token:
                    raise HTTPException(status_code=401, detail="Invalid token data")
                return await generate_token(OEC_DEFAULT_CLIENT_ID, id_token)

            else:
                error_message = response.text
                logging.error(f"An unexpected error occurred: {response.status_code}, {error_message}")
                return {"isAuthorized": False}

    except httpx.HTTPStatusError as http_err:
        raise HTTPException(status_code=500, detail=f"HTTP error occurred: {http_err}")
    except httpx.RequestError as req_err:
        raise HTTPException(status_code=500, detail=f"Request error occurred: {req_err}")
    except Exception as e:
        raise HTTPException(status_code=500, detail="An unexpected error occurred")

async def generate_token(client_id: str, token: str):
    if client_id != OEC_DEFAULT_CLIENT_ID: 
        if not await validate_lingo_token(token, client_id):
            return { "isAuthorized": False }
    
    decoded_id_token = decode_jwt(token)
    issue_time = int(time.time())
    expiry_time = issue_time + 1500
    uuid = None
    if client_id == OEC_DEFAULT_CLIENT_ID:
        if 'sub' in decoded_id_token:
            uuid = decoded_id_token.get('sub')
        elif 'user_info' in decoded_id_token and decoded_id_token.get('user_info', {}).get('sub'):
            uuid = decoded_id_token.get('user_info', {}).get('sub')
    else:
        if 'user_info' in decoded_id_token and decoded_id_token.get('user_info', {}).get('uuid'):
            uuid = decoded_id_token.get('user_info', {}).get('uuid')
    
    if not uuid:
        return { "isAuthorized": False }
    
    payload = {
        "user_info": {
            "uuid": uuid,
            "given_name": decoded_id_token.get('given_name') or decoded_id_token.get('user_info', {}).get('given_name'),
            "preferred_username": decoded_id_token.get('preferred_username') or decoded_id_token.get('user_info', {}).get('preferred_username'),
            "email": decoded_id_token.get('email') or decoded_id_token.get('user_info', {}).get('email'),
        },
        "client_id": client_id,
        "exp": expiry_time,
        "iat": issue_time
    }

    jwt_token = jwt.encode(payload, os.getenv("OHID_JWT_SECRET"), algorithm="HS256")
    return { "isAuthorized": True, "token" : jwt_token }

async def validate_lingo_token(token: str, client_id: str):
    try:
        if client_id == OEC_DEFAULT_CLIENT_ID:
            jwt_secret = os.getenv("OHID_JWT_SECRET")
            jwt.decode(token, jwt_secret, algorithms=["HS256"], options={"verify_exp": True})
        elif client_id == OEC_OPTUMAI_CLIENT_ID:
            jwt_secret = OPTUMAI_CLIENT_PUBLIC_KEY
            jwt.decode(token, jwt_secret, algorithms=["RS256"], options={"verify_exp": True})
        else:
            jwt_secret = EXTERNAL_CLIENT_PUBLIC_KEY
            jwt.decode(token, jwt_secret, algorithms=["RS256"], options={"verify_exp": True})
    except (jwt.ExpiredSignatureError, jwt.InvalidSignatureError, jwt.InvalidTokenError) as err:
        logging.error(f"Token validation error: {str(err)}")
        return False
    return True


async def refresh_token(client_id: str, token: str):
    if client_id != OEC_DEFAULT_CLIENT_ID:
        return { "isAuthorized": False }
    try:
        jwt_secret = os.getenv("OHID_JWT_SECRET")
        decoded_token = jwt.decode(token, jwt_secret, algorithms=["HS256"], options={"verify_exp": False, "verify_signature": True})
        
        issue_time = int(time.time())
        expiry_time = issue_time + 1500

        payload = {
            "user_info": {
                "uuid": decoded_token.get('uuid'),
                "given_name": decoded_token.get('given_name'),
                "preferred_username": decoded_token.get('preferred_username'),
                "email": decoded_token.get('email'),
            },
            "client_id": client_id,
            "exp": expiry_time
        }

        new_jwt_token = jwt.encode(payload, jwt_secret, algorithm="HS256")

        return { "isAuthorized": True, "token" : new_jwt_token }
    except (jwt.ExpiredSignatureError, jwt.InvalidSignatureError, jwt.InvalidTokenError) as err:
        logging.error(f"Token refresh error: {str(err)}")
        return { "isAuthorized": False }