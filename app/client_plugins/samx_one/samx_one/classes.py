from pydantic import BaseModel, Field, ValidationError, field_validator
from typing import List, Optional,Dict, Any
from enum import Enum
from datetime import datetime

# Define BinderCheck class that appears to be used in tool.py
class BinderCheck(BaseModel):
    company_name: str
    signature: bool
    check_number: bool
    routing_number: bool
    valid_binder_check: bool
    reason: str

# Define enums for validation
class Gender(str, Enum):
    MALE = "male"
    FEMALE = "female"
    NON_BINARY = "non-binary"
    EMPTY = ""

class CoverageOption(str, Enum):
    EE = "EE"  # Employee
    SP = "SP"  # Spouse
    CH = "CH"  # Child
    FAM = "FAM"  # Family
    W = "W"    # Waive
    EL = "EL"   # Employee + Child

class Relationship(str, Enum):
    SPOUSE = "spouse"
    CHILD = "child"
    DOMESTIC_PARTNER = "domestic partner"
    CIVIL_UNION_PARTNER = "civil union partner"
    EMPTY = ""

# Define models
class CoverageType(BaseModel):
    medical: Optional[CoverageOption] = None
    dental: Optional[CoverageOption] = None
    vision: Optional[CoverageOption] = None

class Employee(BaseModel):
    firstName: str 
    lastName: str 
    dob: str
    gender: Gender
    coverageType: CoverageType
