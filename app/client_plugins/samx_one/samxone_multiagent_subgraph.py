from langgraph.graph import START
from graph.state import State, get_postgres_checkpointer
from graph.nodes import Assistant
from langgraph.pregel import RetryPolicy
from graph.chains import fetch_primary_assistant_runnable
from langgraph.prebuilt import ToolNode
from utils.helpers.constants import TOOL_CALL_ERROR_MESSAGE
from graph.nodes import restrict_parallel_agents_run
from client_plugins.samx_one.workflow_router import route_to_workflow, route_samx_one_assistant
from utils.subgraph_utils import create_entry_message
from utils.helpers.constants import OEC_SAMXONE_CLIENT_ID
from utils.subgraph_builder import BaseSubgraphBuilder
from utils.subgraph_registry import SubgraphRegistry

samxone_multiagent_compiled_graph = None

async def init_samxone_multiagent_graph():
    """
    Initialize the samx one multiagent graph using the BaseSubgraphBuilder pattern
    and register it with SubgraphRegistry.
    """
    global samxone_multiagent_compiled_graph
    if samxone_multiagent_compiled_graph is None:
        # Get the compiled subgraphs from the registry
        samx_one_subgraph = SubgraphRegistry.get("samx_one")
        surest_subgraph = SubgraphRegistry.get("surest")
        # Create a new builder with fluent interface
        builder = BaseSubgraphBuilder(State)
        
        # Get tools and assistant
        primary_runnable, primary_tools = fetch_primary_assistant_runnable(OEC_SAMXONE_CLIENT_ID)
        primary_assistant_tool_node = ToolNode(primary_tools, handle_tool_errors=TOOL_CALL_ERROR_MESSAGE)
        
        # Add nodes
        builder.add_node("primary", Assistant(primary_runnable), retry=RetryPolicy(max_attempts=2))
        builder.add_node("enter_samx_one_assistant", create_entry_message('samx one assistant', 'samx_one_subgraph'))
        builder.add_node("samx_one_subgraph", samx_one_subgraph)
        builder.add_node("enter_surest_assistant", create_entry_message('surest assistant', 'surest_subgraph'))
        builder.add_node("surest_subgraph", surest_subgraph)
        builder.add_node("invalid_tool", primary_assistant_tool_node)
        builder.add_node("restrict_parallel_agents_run", restrict_parallel_agents_run)  
        
        # Add edges
        builder.add_edge("enter_samx_one_assistant", "samx_one_subgraph")
        builder.add_edge("enter_surest_assistant", "surest_subgraph")
        builder.add_edge("invalid_tool", "primary")
        builder.add_edge("restrict_parallel_agents_run", "primary")
        
        # Set conditional entry point
        builder.add_conditional_edges(START, route_to_workflow)
        
        # Add conditional edges for routing
        builder.add_conditional_edges("primary", route_samx_one_assistant)
        
        # Compile with async checkpointer
        samxone_multiagent_compiled_graph = builder.compile(checkpointer=await get_postgres_checkpointer())
        
        # Register with SubgraphRegistry for future access
        SubgraphRegistry.register("samxone_multiagent", samxone_multiagent_compiled_graph)