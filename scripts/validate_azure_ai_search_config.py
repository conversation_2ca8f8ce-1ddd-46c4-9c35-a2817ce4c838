#!/usr/bin/env python3
"""
Azure AI Search Configuration Validation Script

This script validates your Azure AI Search configuration and tests basic connectivity.
"""

import asyncio
import sys
import os
from typing import Dict, Any

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'app'))

try:
    from config.azure_ai_search import (
        azure_ai_search_config,
        get_async_search_client,
        get_async_index_client
    )
    from config.azure_ai_search_schemas import get_all_index_schemas
    from auth.openAI_auth import get_openai_token
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the project root directory")
    sys.exit(1)

class AzureAISearchValidator:
    """Validates Azure AI Search configuration and connectivity"""
    
    def __init__(self):
        self.validation_results = {
            "environment_variables": False,
            "authentication": False,
            "connectivity": False,
            "index_schemas": False,
            "search_functionality": False
        }
        self.errors = []
    
    async def validate_all(self) -> Dict[str, Any]:
        """Run all validation checks"""
        print("Starting Azure AI Search configuration validation...\n")
        
        # Check environment variables
        await self.validate_environment_variables()
        
        # Check authentication
        await self.validate_authentication()
        
        # Check connectivity
        await self.validate_connectivity()
        
        # Check index schemas
        await self.validate_index_schemas()
        
        # Check search functionality (if indexes exist)
        if self.validation_results["connectivity"]:
            await self.validate_search_functionality()
        
        # Print summary
        self.print_validation_summary()
        
        return {
            "validation_results": self.validation_results,
            "errors": self.errors,
            "overall_status": all(self.validation_results.values())
        }
    
    async def validate_environment_variables(self):
        """Validate required environment variables"""
        print("1. Validating environment variables...")
        
        required_vars = [
            "AZURE_AI_SEARCH_ENDPOINT",
            "AZURE_AI_SEARCH_AUTH_METHOD"
        ]
        
        missing_vars = []
        
        for var in required_vars:
            value = os.getenv(var)
            if not value:
                missing_vars.append(var)
            else:
                print(f"   ✓ {var}: {value}")
        
        # Check auth-specific variables
        auth_method = os.getenv("AZURE_AI_SEARCH_AUTH_METHOD", "TOKEN")
        if auth_method == "KEY":
            api_key = os.getenv("AZURE_AI_SEARCH_API_KEY")
            if not api_key:
                missing_vars.append("AZURE_AI_SEARCH_API_KEY")
            else:
                print(f"   ✓ AZURE_AI_SEARCH_API_KEY: {'*' * len(api_key)}")
        
        if missing_vars:
            self.errors.append(f"Missing environment variables: {', '.join(missing_vars)}")
            print(f"   ✗ Missing variables: {', '.join(missing_vars)}")
        else:
            self.validation_results["environment_variables"] = True
            print("   ✓ All required environment variables are set")
        
        print()
    
    async def validate_authentication(self):
        """Validate authentication configuration"""
        print("2. Validating authentication...")
        
        try:
            auth_method = azure_ai_search_config.auth_method
            print(f"   Authentication method: {auth_method}")
            
            if auth_method == "TOKEN":
                # Test token provider
                token = get_openai_token()
                if token:
                    print(f"   ✓ Token provider working (token length: {len(token)})")
                    self.validation_results["authentication"] = True
                else:
                    self.errors.append("Token provider returned empty token")
                    print("   ✗ Token provider returned empty token")
            
            elif auth_method == "KEY":
                if azure_ai_search_config.api_key:
                    print("   ✓ API key configured")
                    self.validation_results["authentication"] = True
                else:
                    self.errors.append("API key not configured")
                    print("   ✗ API key not configured")
            
            else:
                self.errors.append(f"Unknown authentication method: {auth_method}")
                print(f"   ✗ Unknown authentication method: {auth_method}")
        
        except Exception as e:
            self.errors.append(f"Authentication validation failed: {str(e)}")
            print(f"   ✗ Authentication validation failed: {str(e)}")
        
        print()
    
    async def validate_connectivity(self):
        """Validate connectivity to Azure AI Search"""
        print("3. Validating connectivity...")
        
        try:
            # Test index client connectivity
            index_client = get_async_index_client()
            indexes = await index_client.list_indexes()
            
            index_list = [index async for index in indexes]
            print(f"   ✓ Successfully connected to Azure AI Search")
            print(f"   ✓ Found {len(index_list)} existing indexes")
            
            for index in index_list:
                print(f"     - {index.name}")
            
            self.validation_results["connectivity"] = True
        
        except Exception as e:
            self.errors.append(f"Connectivity test failed: {str(e)}")
            print(f"   ✗ Connectivity test failed: {str(e)}")
        
        print()
    
    async def validate_index_schemas(self):
        """Validate index schema definitions"""
        print("4. Validating index schemas...")
        
        try:
            schemas = get_all_index_schemas()
            print(f"   ✓ Found {len(schemas)} index schema definitions:")
            
            for index_name, schema in schemas.items():
                print(f"     - {index_name}: {len(schema.fields)} fields")
                
                # Check for required fields
                field_names = [field.name for field in schema.fields]
                required_fields = ["id", "file_name", "page_content", "embedding"]
                
                missing_fields = [field for field in required_fields if field not in field_names]
                if missing_fields:
                    self.errors.append(f"Index {index_name} missing required fields: {missing_fields}")
                    print(f"       ✗ Missing fields: {missing_fields}")
                else:
                    print(f"       ✓ All required fields present")
            
            self.validation_results["index_schemas"] = True
        
        except Exception as e:
            self.errors.append(f"Index schema validation failed: {str(e)}")
            print(f"   ✗ Index schema validation failed: {str(e)}")
        
        print()
    
    async def validate_search_functionality(self):
        """Validate basic search functionality"""
        print("5. Validating search functionality...")
        
        try:
            # Try to get a search client for the main index
            search_client = get_async_search_client("surest-training-material")
            
            # Perform a simple search (this will fail if index doesn't exist, which is OK)
            try:
                results = await search_client.search("*", top=1)
                result_list = [result async for result in results]
                print(f"   ✓ Search functionality working")
                print(f"   ✓ Test search returned {len(result_list)} results")
                self.validation_results["search_functionality"] = True
            
            except Exception as search_error:
                if "index not found" in str(search_error).lower():
                    print("   ⚠ Index not found - this is expected if you haven't migrated data yet")
                    print("   ✓ Search client creation successful")
                    self.validation_results["search_functionality"] = True
                else:
                    raise search_error
        
        except Exception as e:
            self.errors.append(f"Search functionality test failed: {str(e)}")
            print(f"   ✗ Search functionality test failed: {str(e)}")
        
        print()
    
    def print_validation_summary(self):
        """Print validation summary"""
        print("=" * 60)
        print("VALIDATION SUMMARY")
        print("=" * 60)
        
        for check, passed in self.validation_results.items():
            status = "✓ PASS" if passed else "✗ FAIL"
            print(f"{check.replace('_', ' ').title()}: {status}")
        
        print()
        
        if self.errors:
            print("ERRORS FOUND:")
            for i, error in enumerate(self.errors, 1):
                print(f"{i}. {error}")
            print()
        
        overall_status = all(self.validation_results.values())
        if overall_status:
            print("🎉 All validations passed! Azure AI Search is ready to use.")
        else:
            print("❌ Some validations failed. Please fix the errors above.")
        
        print("=" * 60)
    
    def get_next_steps(self) -> list:
        """Get recommended next steps based on validation results"""
        next_steps = []
        
        if not self.validation_results["environment_variables"]:
            next_steps.append("Set up required environment variables")
        
        if not self.validation_results["authentication"]:
            next_steps.append("Fix authentication configuration")
        
        if not self.validation_results["connectivity"]:
            next_steps.append("Check network connectivity and credentials")
        
        if all(self.validation_results.values()):
            next_steps.extend([
                "Run: python scripts/azure_ai_search_migration.py --indexes-only",
                "Run: python scripts/azure_ai_search_migration.py",
                "Run: python scripts/rag_performance_test.py --iterations 3"
            ])
        
        return next_steps

async def main():
    """Main validation function"""
    validator = AzureAISearchValidator()
    results = await validator.validate_all()
    
    # Print next steps
    next_steps = validator.get_next_steps()
    if next_steps:
        print("\nRECOMMENDED NEXT STEPS:")
        for i, step in enumerate(next_steps, 1):
            print(f"{i}. {step}")
    
    # Exit with appropriate code
    sys.exit(0 if results["overall_status"] else 1)

if __name__ == "__main__":
    asyncio.run(main())
