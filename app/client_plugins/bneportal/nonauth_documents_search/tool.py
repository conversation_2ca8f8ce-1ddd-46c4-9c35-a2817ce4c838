from langchain_core.tools import tool
from config.openAI import json_model, embeddings
from config.postgres import get_pg_connection, return_pg_connection
import json
from typing import Annotated, Optional
from langgraph.prebuilt.tool_node import InjectedState
from utils.tool_utils import log_tool_error
import traceback
from client_plugins.bneportal.bneportal_utils.utils import get_table_name_based_on_user_type, get_tile_options_for_user, check_for_multiple_eff_dates, get_sbc, get_plan_id_data, validate_plan_id, find_single_effective_date, get_contract_options_for_user, get_employer_handbook, find_contract_opt_type, get_member_handbook, get_forms
from client_plugins.bneportal.bneportal_consts import NONAUTH_DOC_SEARCH_THRESHOLD, STATE_OPTION_MAP, GROUP_SIZE_OPTION_MAP, LANGUAGE_OPTION_MAP

bne_training_material_fallback_message = "Sorry we cannot find the answer to your question. Please contact UHC Representative."

@tool
async def bne_training_document_search(state: Annotated[dict, InjectedState], bne_question: str): # add arguments if required
    "This tool provide capability of search and doing Q&A with non authenticated document, guides, and report related all the queries present in B&E portal."
    try:
        user_type = state.get("additional_arg", {}).get("bne_user_type")
        functionality = 'Training Materials'
        table_name = get_table_name_based_on_user_type(user_type, functionality)
        
        pg_connection = await get_pg_connection()
        # Establish cursor  
        cursor = pg_connection.cursor()  
        
        # Define similarity search query  
        similarity_search_query = f"""  
            SELECT   
                file_name,   
                file_url,   
                page_content,   
                created_at,   
                embedding <-> %s::vector AS score  
            FROM   
                {table_name}  
            ORDER BY   
                score  
            LIMIT 2  
        """  
        
        # Embed the question  
        question_embedding = await embeddings.aembed_query(bne_question)  
        
        # Execute the query with the embedding  
        await cursor.execute(similarity_search_query, (question_embedding,))  
        vector_results = await cursor.fetchall()  

        # Debug
        # for result in vector_results:
        #     print(result)
        #     print()
        # print()
        
        # Check if any results are returned  
        if not vector_results or len(vector_results) == 0:  
            await log_tool_error(state, "No documents found in DB for similarity search.", "document_search")
            return bne_training_material_fallback_message
        
        # Initialize variables
        vector_result_content = ""
        vector_results_file_url = ""
        most_similar_page = None
        threshold_score = NONAUTH_DOC_SEARCH_THRESHOLD
        
        # Filter results that meet the threshold criteria
        relevant_results = []
        for res in vector_results:
            file_name, file_url, page_content, created_at, score = res
            if score <= threshold_score:
                relevant_results.append(res)
        
        # If no results meet the threshold criteria, return fallback message
        if not relevant_results:
            return bne_training_material_fallback_message
        
        # Process only the relevant results that meet the threshold
        for res in relevant_results:
            file_name, file_url, page_content, created_at, score = res
            vector_result_content += page_content + "\n\n"
            
            # Initialize or update the most similar page based on the lowest score
            if not most_similar_page:  
                most_similar_page = {  
                    "score": score,  
                    "file_url": file_url  
                }  
            elif score < most_similar_page["score"]:
                most_similar_page = {
                    "score": score,
                    "file_url": file_url
                }
        # print("SCORE:", most_similar_page["score"])
        
        # Assign the file URL of the most similar page
        vector_results_file_url = most_similar_page["file_url"]
        
        prompt = f"""
        Follow the given instructions and answer the question provided:
        Instructions:
        0. If the context has semantic meaning related to the question then only answer the question else return the message {bne_training_material_fallback_message}.
        1. If the user is asking to download a document, or for a document link, then answer the query and also give hyperlink generated using the file name and PDF link relevant to the question.
           - Take the first line from the document as a reference for the file name.
           - Ensure the hyperlink is properly formatted in Markdown syntax: `[File Name](Encoded URL)`.
           - Encode the URL to replace spaces with `%20` or any other special characters.
        2. Do not assume anything, do not hallucinate, and do not make up any information.
        3. Based on the context, respond with 3 follow-up questions.
        4. Make sure follow-up questions have semantic meaning related to the context and are short and precise.
        5. Generate follow-up questions a user might ask based on specific details in the context, and phrase it in  first-person assertive form, meaning they should reflect how a user would naturally ask a question in a conversational, direct, and task-oriented manner. Avoid formal or assistant-like "you" phrasing such as "Would you like to see..." or "Can I help you with...". Instead, use concise, action-driven questions that mirror user intent.
        Example: "I want steps to register for uhceservices", "Can I use same OHID over multiple sites?"
        6. Respond in a JSON format with the following structure:
        {{
            "answer": "Your answer (should always be a string)",
            "follow_up_questions": ["Question 1", "Question 2", "Question 3"]
        }}

        Context: {vector_result_content}

        Question: {bne_question}

        PDF Link: {vector_results_file_url}

        Answer:
        """
        
        response = json_model.invoke(prompt).content
        json_response = json.loads(response)
        answer = json_response.get("answer", "") + "[view-source-hyper-link]" + vector_results_file_url
        follow_up_questions = json_response.get("follow_up_questions", [])
        if bne_training_material_fallback_message.lower() in answer.lower():
            return {
                "answer": bne_training_material_fallback_message,
                "follow_up_questions": []
            }
        return {
            "answer": answer,
            "follow_up_questions": follow_up_questions
        }

    except Exception as e:
        await pg_connection.rollback()
        await log_tool_error(state, traceback.print_exc(), "document_search")
        raise Exception (traceback.format_exc())
    finally:
        await pg_connection.commit()
        await cursor.close()
        await return_pg_connection(pg_connection)


@tool
async def bne_training_video_search(state: Annotated[dict, InjectedState], bne_question: str):
    """
    This tool provides access to training videos related to 'B&E'. It should be used whenever a user wants to watch or view training videos or any other video content related to 'B&E'.
    """
    try:
        user_type = state.get("additional_arg", {}).get("bne_user_type")
        functionality = 'Training Videos'
        table_name = get_table_name_based_on_user_type(user_type, functionality)
        # Connect with the database 
        pg_connection = await get_pg_connection()
        cursor = pg_connection.cursor()

        # Generate embedding for the question
        question_embedding = await embeddings.aembed_query(bne_question)
        
        # Perform similarity search using the embedding
        similarity_search_query = f"""
        SELECT file_url, embedding <-> %s::vector AS score 
        FROM {table_name}
        ORDER BY score
        LIMIT 1
        """
        await cursor.execute(similarity_search_query, (question_embedding,))
        result = await cursor.fetchone()
        
        if not result or len(result) == 0:
            return bne_training_material_fallback_message
        
        bne_training_video_url = result[0]
        
        if (not bne_training_video_url):
            return bne_training_material_fallback_message
        
        return {
            "client": "bne_client",
            "response_type": "video",
            "url": bne_training_video_url
        }
    
    except Exception as e:
        await pg_connection.rollback()
        await log_tool_error(state, traceback.print_exc(), "bne_training_video_search")
        raise Exception (traceback.format_exc())
    finally:
        await pg_connection.commit()
        await cursor.close()
        await return_pg_connection(pg_connection)


@tool
async def bne_training_tile_click(state: Annotated[dict, InjectedState], bne_question: str):
    """
    This tool provides very specific data related to exact user query strings 'Broker Training', 'Employer Training', and 'Internal Training'.
    Strictly: This tool should be used everytime a user asks a question that matches one of these strings: 'Broker Training', 'Employer Training', and 'Internal Training'.
    """
    try:
        user_type = state.get("additional_arg", {}).get("bne_user_type")
        
        data = await get_tile_options_for_user(bne_question, user_type)

        if not data or len(data) == 0:
            return bne_training_material_fallback_message
        
        return {
            "client": "bne_client",
            "response_type": "training_tile",
            "data": data
        }
    
    except Exception as e:
        await log_tool_error(state, traceback.print_exc(), "bne_training_tile_click")
        raise Exception (traceback.format_exc())


@tool
def bne_find_resources_tile_click(state: Annotated[dict, InjectedState], bne_question: str):
    """
    This tool provides very specific data related to exact user query strings 'Find Resources'.
    Strictly: This tool should be used everytime a user asks a question that matches one of these strings: 'Find Resources'
    """
    try:
        
        return {
            "client": "bne_client",
            "answer": "I can help with that! What resource are you looking for today?",
            "custom_suggestions": [
                {
                "value": "Summary of Benefit and Coverage",
                "type": "resources"
                },
                {
                "value": "Member Handbook",
                "type": "resources"
                },
                {
                "value": "Employer Handbook",
                "type": "resources"
                },
                {
                "value": "Marketing Materials",
                "type": "resources"
                },
                {
                "value": "Forms",
                "type": "resources"
                },
                {
                "value": "Plan Grids and Network Documents",
                "type": "resources"
                },
                {
                "value": "Benefit Summary",
                "type": "resources"
                },
            ]
        }
    
    except Exception as e:
        log_tool_error(state, traceback.print_exc(), "bne_find_resources_tile_click")
        raise Exception (traceback.format_exc())
    

@tool
async def bne_get_employer_handbook(state: Annotated[dict, InjectedState], group_id: str = None,
                         plan_id: str = None, effective_date: str = None) -> dict:
    """
    Use this tool ONLY if the user asks for Employer Handbook. Do NOT use this tool for pulling any other document or video. Do NOT use this tool for Member Handbook or SBC (Summary Benefits Coverage).
    1. This tool will retrieve Employer Handbook Document based on the given group_id.
    2. When responding to user, keep it concise, short, and not too verbose. But response must be polite and in a conversational tone.
    3. Only if the user has explicitly provided/mentioned a Group ID in the input, convert it to string and assign that value to the group_id variable. If not, no need to do anything.
    4. Once the user has provided the Group ID, run this tool to ask the user for the Plan ID. The user must provide ID. If Plan ID is provided, convert to string and assign it to the plan_id parameter.
    5. If the user has explicitly provided a Plan ID in previous message(s) in the chat history, use the most recent Plan ID provided.
    6. If the user enters an ID and does not specify what kind of ID it is, then you must ask the user to specify if it is a Plan ID. Dont just assume what type of ID it is.
    7. Only if multiple ids are provided, then ask the user to specify which is the is a Plan ID if not already specified.
    8. Once the user has provided the Group ID and Plan ID, run this tool to check if the plan has one or more than one coverage effective dates. If there are multiple effective dates, it will ask the user to specify the effective date.
    9. Bold the fields needed from user in each prompt.
    """

    plan_id_data = dict()

    if not group_id or group_id == "":
        no_group_message = "Please provide a valid Group ID to view (Employer Handbook)."
        return {
            "answer": no_group_message,
            "follow_up_questions": []
        }
    else:
        plan_id_data = await get_plan_id_data(group_id)

    if not plan_id or plan_id == "":
        no_plan_message = "Please provide a valid Plan ID to view Employer Handbook(s)."
        
        custom_suggestions = []
        for contractOptType, plans in plan_id_data.items():
            for planId, planInfo in plans.items():
                custom_suggestions.append({
                    "value": f"{planId} | {planInfo.get('planName', '')}",
                    "type": contractOptType,
                    "notes": "Employer Handbook"
                })

        return {
            "client": "bne_client",
            "answer": no_plan_message,
            "custom_suggestions": custom_suggestions
        }
    elif not validate_plan_id(plan_id_data, plan_id):
        invalid_plan_id_message = "The Plan ID provided is invalid. Please provide a valid Plan ID to view Employer Handbook."
        return {
            "answer": invalid_plan_id_message,
            "follow_up_questions": []
        }
    elif (not effective_date or effective_date == "") and check_for_multiple_eff_dates(plan_id_data, plan_id):
        multiple_eff_dates_message = "The Plan ID provided has multiple effective dates. Please specify the **effective date** for the Plan ID."
        
        custom_suggestions = []
        for contractOptType, plans in plan_id_data.items():
            for planId, planInfo in plans.items():
                if planId == plan_id:
                    for effDateObj in planInfo.get("effDates", []):
                        custom_suggestions.append({
                            "value": effDateObj.get("effDate"),
                            "type": contractOptType,
                            "notes": "Effective Date"
                        })
                    break
        
        return {
            "client": "bne_client",
            "answer": multiple_eff_dates_message,
            "custom_suggestions": custom_suggestions
        }
    else:
        try:
            eff_date = effective_date if effective_date else find_single_effective_date(plan_id_data, plan_id)
            contract_Opt_Type = find_contract_opt_type(plan_id_data, plan_id)
            response = await get_employer_handbook(group_id, plan_id, eff_date, contract_Opt_Type)
            status = response.get('httpStatus', '')
            if status == 'OK':
                return {
                    "custom_suggestions": [
                        {
                            "file_date": eff_date,
                            "report_type": response.get('filename', ''),
                            "type": "expanded",
                            "document_type": "Employer Handbook",
                            "file":{
                                "payload": response.get('payload', ''),
                                "file_name": response.get('filename', ''),
                                "file_extension": response.get('fileExtension', '')
                            },
                            "group_id": group_id,
                            "plan_id": plan_id,
                            "effective_date": eff_date
                        }
                    ],
                    "client": "bne_client",
                    "response_type": "Employer Handbook",
                    "answer": "Found the Employer Handbook Documents for the provided Group ID and Plan ID!",
                }
            else:
                if response.get('code') == 15000:
                    return {
                        "answer": "Employer Handbook not available yet.",
                        "follow_up_questions": []
                    }
                resp_error_message = "I am unable to retrieve this Employer Handbook document at this time. Please try again later."
                return {
                    "answer": resp_error_message,
                    "follow_up_questions": []
                }
        except Exception as e:
            await log_tool_error(state, traceback.print_exc(), "bne_get_employer_handbook")
            return {
                "answer": "I am unable to retrieve this Employer Handbook document at this time. Please try again later.",
                "follow_up_questions": []
            }
    

@tool
async def bne_get_member_handbook(state: Annotated[dict, InjectedState], group_id: str = None,
                         plan_id: str = None, effective_date: str = None) -> dict:
    """
    Use this tool ONLY if the user asks for Member Handbook. Do NOT use this tool for pulling any other document or video. Do NOT use this tool for Employer Handbook or SBC (Summary Benefits Coverage).
    1. This tool will retrieve Member Handbook Document based on the given group_id.
    2. When responding to user, keep it concise, short, and not too verbose. But response must be polite and in a conversational tone.
    3. Only if the user has explicitly provided/mentioned a Group ID in the input, convert it to string and assign that value to the group_id variable. If not, no need to do anything.
    4. Once the user has provided the Group ID, run this tool to ask the user for the Plan ID. The user must provide ID. If Plan ID is provided, convert to string and assign it to the plan_id parameter.
    5. If the user has explicitly provided a Plan ID in previous message(s) in the chat history, use the most recent Plan ID provided.
    6. If the user enters an ID and does not specify what kind of ID it is, then you must ask the user to specify if it is a Plan ID. Dont just assume what type of ID it is.
    7. Only if multiple ids are provided, then ask the user to specify which is the is a Plan ID if not already specified.
    8. Once the user has provided the Group ID and Plan ID, run this tool to check if the plan has one or more than one coverage effective dates. If there are multiple effective dates, it will ask the user to specify the effective date.
    9. Bold the fields needed from user in each prompt.
    """

    plan_id_data = dict()

    if not group_id or group_id == "":
        no_group_message = "Please provide a valid Group ID to view (Member Handbook)."
        return {
            "answer": no_group_message,
            "follow_up_questions": []
        }
    else:
        plan_id_data = await get_plan_id_data(group_id)

    if not plan_id or plan_id == "":
        no_plan_message = "Please provide a valid Plan ID to view Member Handbook(s)."
        
        custom_suggestions = []
        for contractOptType, plans in plan_id_data.items():
            for planId, planInfo in plans.items():
                custom_suggestions.append({
                    "value": f"{planId} | {planInfo.get('planName', '')}",
                    "type": contractOptType,
                    "notes": "Member Handbook"
                })

        return {
            "client": "bne_client",
            "answer": no_plan_message,
            "custom_suggestions": custom_suggestions
        }
    elif not validate_plan_id(plan_id_data, plan_id):
        invalid_plan_id_message = "The Plan ID provided is invalid. Please provide a valid Plan ID to view Member Handbook."
        return {
            "answer": invalid_plan_id_message,
            "follow_up_questions": []
        }
    elif (not effective_date or effective_date == "") and check_for_multiple_eff_dates(plan_id_data, plan_id):
        multiple_eff_dates_message = "The Plan ID provided has multiple effective dates. Please specify the **effective date** for the Plan ID."
        
        custom_suggestions = []
        for contractOptType, plans in plan_id_data.items():
            for planId, planInfo in plans.items():
                if planId == plan_id:
                    for effDateObj in planInfo.get("effDates", []):
                        custom_suggestions.append({
                            "value": effDateObj.get("effDate"),
                            "type": contractOptType,
                            "notes": "Effective Date"
                        })
                    break
        
        return {
            "client": "bne_client",
            "answer": multiple_eff_dates_message,
            "custom_suggestions": custom_suggestions
        }
    else:
        try:
            eff_date = effective_date if effective_date else find_single_effective_date(plan_id_data, plan_id)
            contract_Opt_Type = find_contract_opt_type(plan_id_data, plan_id)
            response = await get_member_handbook(group_id, plan_id, eff_date, contract_Opt_Type)
            status = response.get('httpStatus', '')
            if status == 'OK':
                return {
                    "custom_suggestions": [
                        {
                            "file_date": eff_date,
                            "report_type": response.get('filename', ''),
                            "type": "expanded",
                            "document_type": "Member Handbook",
                            "file":{
                                "payload": response.get('payload', ''),
                                "file_name": response.get('filename', ''),
                                "file_extension": response.get('fileExtension', '')
                            },
                            "group_id": group_id,
                            "plan_id": plan_id,
                            "effective_date": eff_date
                        }
                    ],
                    "client": "bne_client",
                    "response_type": "Member Handbook",
                    "answer": "Found the Member Handbook Documents for the provided Group ID and Plan ID!",
                }
            else:
                if response.get('code') == 15000:
                    return {
                        "answer": "Member Handbook not available yet.",
                        "follow_up_questions": []
                    }
                resp_error_message = "I am unable to retrieve this Member Handbook document at this time. Please try again later."
                return {
                    "answer": resp_error_message,
                    "follow_up_questions": []
                }
        except Exception as e:
            await log_tool_error(state, traceback.print_exc(), "bne_get_member_handbook")
            return {
                "answer": "I am unable to retrieve this Member Handbook document at this time. Please try again later.",
                "follow_up_questions": []
            }
    

@tool
async def bne_get_sbc(state: Annotated[dict, InjectedState], group_id: str = None,
                         plan_id: Optional[str] = None, effective_date: Optional[str] = None) -> dict:
    """
    Use this tool ONLY if the user asks for SBC (Summary Benefits Coverage). Do NOT use this tool for pulling any other document or video. Do NOT use this tool for Employer Handbook or Member Handbook.
    1. This tool will retrieve SBC Document based on the given group_id.
    2. When responding to user, keep it concise, short, and not too verbose. But response must be polite and in a conversational tone.
    3. Only if the user has explicitly provided/mentioned a Group ID in the input, convert it to string and assign that value to the group_id variable. If not, no need to do anything.
    4. Once the user has provided the Group ID, run this tool to ask the user for the Plan ID. If Plan ID is provided, convert to string and assign it to the plan_id parameter.
    5. If the user has explicitly provided a Plan ID in previous message(s) in the chat history, use the most recent Plan ID provided.
    6. If the user enters an ID and does not specify what kind of ID it is, then you must ask the user to specify if it is a Plan ID. Dont just assume what type of ID it is.
    7. Only if multiple ids are provided, then ask the user to specify which is the is a Plan ID if not already specified.
    8. Once the user has provided the Group ID and Plan ID, run this tool to check if the plan has one or more than one coverage effective dates. If there are multiple effective dates, it will ask the user to specify the effective date.
    9. Bold the fields needed from user in each prompt.

    Args:
        group_id: (Required) A 7 or 8 digit numeric string representing the member group ID.
        plan_id: (Optional) An alphanumeric string representing the plan ID.
        effective_date: (Optional) A date string in the format 'YYYY-MM-DD' representing the effective date. If some effective date is provided but not in this format, convert the date to this format when you assign it to the effective_date variable.
    """

    plan_id_data = dict()

    if not group_id or group_id == "":
        no_group_message = "Please provide a valid Group ID to view SBC(s)."
        return {
            "answer": no_group_message,
            "follow_up_questions": []
        }
    else:
        plan_id_data = await get_plan_id_data(group_id)

    if not plan_id or plan_id == "":
        no_plan_message = "Please provide a valid Plan ID to view SBC(s)."
        
        custom_suggestions = []
        for contractOptType, plans in plan_id_data.items():
            for planId, planInfo in plans.items():
                custom_suggestions.append({
                    "value": f"{planId} | {planInfo.get('planName', '')}",
                    "type": contractOptType,
                    "notes": "Summary of Benefit and Coverage"
                })

        return {
            "client": "bne_client",
            "answer": no_plan_message,
            "custom_suggestions": custom_suggestions
        }
    elif not validate_plan_id(plan_id_data, plan_id):
        invalid_plan_id_message = "The Plan ID provided is invalid. Please provide a valid Plan ID to view SBC(s)."
        return {
            "answer": invalid_plan_id_message,
            "follow_up_questions": []
        }
    elif (not effective_date or effective_date == "") and check_for_multiple_eff_dates(plan_id_data, plan_id):
        multiple_eff_dates_message = "The Plan ID provided has multiple effective dates. Please specify the **effective date** for the Plan ID."
        
        custom_suggestions = []
        for contractOptType, plans in plan_id_data.items():
            for planId, planInfo in plans.items():
                if planId == plan_id:
                    for effDateObj in planInfo.get("effDates", []):
                        custom_suggestions.append({
                            "value": effDateObj.get("effDate"),
                            "type": contractOptType,
                            "notes": "Effective Date"
                        })
                    break
        
        return {
            "client": "bne_client",
            "answer": multiple_eff_dates_message,
            "custom_suggestions": custom_suggestions
        }
    else:
        try:
            eff_date = effective_date if effective_date else find_single_effective_date(plan_id_data, plan_id)
            contract_Opt_Type = find_contract_opt_type(plan_id_data, plan_id)
            if not (contract_Opt_Type == "MD"):
                return {
                    "answer": "SBC is not available for this Plan ID. Please check with your employer or UHC representative for more information.",
                    "follow_up_questions": []
                }
            response = await get_sbc(group_id, plan_id, eff_date)
            status = response.get('httpStatus', '')
            if status == 'OK':
                return {
                    "custom_suggestions": [
                        {
                            "file_date": eff_date,
                            "report_type": response.get('filename', ''),
                            "type": "expanded",
                            "document_type": "SBC",
                            "file":{
                                "payload": response.get('payload', ''),
                                "file_name": response.get('filename', ''),
                                "file_extension": response.get('fileExtension', '')
                            },
                            "group_id": group_id,
                            "plan_id": plan_id,
                            "effective_date": eff_date
                        }
                    ],
                    "client": "bne_client",
                    "response_type": "sbc"
                }
            else:
                if response.get('code') == 15000:
                    return {
                        "answer": "Summary Benefits Coverage Document not available yet.",
                        "follow_up_questions": []
                    }
                resp_error_message = "I am unable to retrieve this SBC document at this time. Please try again later."
                return {
                    "answer": resp_error_message,
                    "follow_up_questions": []
                }
        except Exception as e:
            await log_tool_error(state, traceback.print_exc(), "bne_get_sbc")
            return {
                "answer": "I am unable to retrieve this SBC document at this time. Please try again later.",
                "follow_up_questions": []
            }
        
@tool
async def bne_get_forms(state: Annotated[dict, InjectedState], US_state: str = None,
                         group_size: str = None, language: Optional[str] = 'English') -> dict:
    """
    Use this tool ONLY if the user asks for Forms. Do NOT use this tool for pulling any other document or video. Do NOT use this tool for Employer Handbook or Member Handbook or SBC (Summary Benefits Coverage).
    0. When the tool is called, greet the user with the following messages.
        ---
        Great! Send me the following:
        - **State** for forms you’re looking for
        - Your **Group Size** (# of members looking to enroll)
        - (Optional) If you’re looking for forms in a **language** other than English, please also specify that now.
        ---

    1. This tool will retrieve Forms Documents based on the given state.
    2. When responding to user, keep it concise, short, and not too verbose. But response must be polite and in a conversational tone.
    3. Only if the user has explicitly provided/mentioned a State and Group Size in the input, convert it to string and assign that value to the variables US_state and group_size respectively. If not, no need to do anything.
    4. Optionally, the user can also provide the language if it needs to view the forms in any non English language. If the user does not provide a language, default to English.
    5. Only if multiple states or group sizes are provided, then ask the user to specify which is the is a state or group_size he would like to search for first. Then assign that value to the respective variable.
        
    Args:
        US_state: (Required) Valid states: [Alabama, Alaska, Arizona, Arkansas, California, Colorado, Connecticut, Delaware, Florida, Georgia, Hawaii, Idaho, Illinois, Indiana, Iowa, Kansas, Kentucky, Louisiana, Maine, Maryland, Massachusetts, Michigan, Minnesota, Mississippi, Missouri, Montana, Nebraska, New Jersey, New York, Nevada, New Hampshire, New Mexico, North Carolina, North Dakota, Ohio, Oklahoma, Oregon, Pennsylvania, Rhode Island, South Carolina, South Dakota, Tennessee, Texas, Utah, Vermont, Virginia, Washington, West Virginia, Wisconsin, Wyoming, Virgin Islands]
        
        group_size: (Required) Valid group sizes: ["50 or fewer", "51-100", "100+", "All group sizes"]
        
        language: (Optional) Valid languages: ["English", "Spanish", "Vietnamese", "Chinese", "Japanese", "Korean", "Tagalog"] Default is English.
    """

    try:
        prompt = f"""
            Following are the parameters received to fetch the forms:
            US_state: {US_state}
            group_size: {group_size}
            language: {language}

            Map these parameters to format structure accepted by the forms API. Always map parameters to the value and not the key:
            States are accepted as one of the following values in the map :-
            state: {STATE_OPTION_MAP}

            Group Size is accepted as one of the following values in the map :-
            groupSize: {GROUP_SIZE_OPTION_MAP}

            Language is accepted as one of the following values in the map :-
            language: {LANGUAGE_OPTION_MAP}

            Respond in a JSON format with the following structure:
            {{
                "state": "Your answer (should always be a string)",
                "groupSize": "Your answer (should always be a string)",
                "language": "Your answer (should always be a string)"
            }}

            Note: Never force a mapping if you dont feel confident that the parameters are present in the map. Supply an empty string for the parameter if you are not confident about it.
        """

        response = json_model.invoke(prompt).content
        json_response = json.loads(response)
        us_state = json_response.get("state", "")
        group_size = json_response.get("groupSize", "")
        language = json_response.get("language", "")

        if not us_state in STATE_OPTION_MAP.values():
            return {
                "answer": "Please provide a valid State to view forms. Here are the valid states: " + ", ".join(STATE_OPTION_MAP.keys()),
                "follow_up_questions": []
            }
        elif not group_size in GROUP_SIZE_OPTION_MAP.values():
            return {
                "answer": "The Group Size provided is invalid. Please provide a valid Group Size to view forms. Here are the valid group sizes: " + ", ".join(GROUP_SIZE_OPTION_MAP.keys()),
                "follow_up_questions": []
            }
        elif not language in LANGUAGE_OPTION_MAP.values():
            return {
                "answer": "The Language provided is invalid. Please provide a valid Language to view forms. Here are the valid languages: " + ", ".join(LANGUAGE_OPTION_MAP.keys()),
                "follow_up_questions": []
            }
        else:
            response = await get_forms(us_state, group_size, language)
            findDocumentsResponseType = response.get('findDocumentsResponseType', '')
            if (
                findDocumentsResponseType
                and getattr(findDocumentsResponseType, "documentList", None)
                and getattr(findDocumentsResponseType.documentList, "document", None)
                and len(findDocumentsResponseType.documentList.document) > 0
            ):
                return {
                    "custom_suggestions": [
                        {
                            "report_type": "",
                            "type": "expanded",
                            "document_type": "Forms",
                            "documents": findDocumentsResponseType.documentList.document
                        }
                    ],
                    "client": "bne_client",
                    "response_type": "forms",
                    "answer": "Found the Forms for the provided state!",
                }
            else:
                if response.get('code') == 15000:
                    return {
                        "answer": "Forms Document not available yet.",
                        "follow_up_questions": []
                    }
                resp_error_message = "I am unable to retrieve this forms document at this time. Please try again later."
                return {
                    "answer": resp_error_message,
                    "follow_up_questions": []
                }
    except Exception as e:
        await log_tool_error(state, traceback.print_exc(), "bne_get_forms")
        return {
            "answer": "I am unable to retrieve this forms document at this time. Please try again later.",
            "follow_up_questions": []
        }
