from client_plugins.bneportal.compensation.utils import check_pcis_id_authorization, validate_cust_crid, get_followup_questions, get_customer_validation_results,get_logger,post_with_cert, filterResultsByCRID
import requests
from typing import Annotated
from langgraph.prebuilt.tool_node import InjectedState
import json
import os
from config.openAI import json_model
from langchain_core.tools import tool
from utils.tool_utils import log_tool_error
import traceback

TRY_ANOTHER_PROMPTS = "View Compensation Prompts"
logger = get_logger(__name__)

@tool
async def serviceFeeAgreement(state: Annotated[dict, InjectedState], customer_id: str, pcis_input: str = None) -> dict:
    """
    Use this tool ONLY if the user asks for Service Fee Agreement (SFA) information.
    1. Only Customer ID is required for retrieval of the SFA details. If the user has provided a Customer ID in previous message(s) in the chat history, use the most recent Customer ID given and assign it to the customerNumber parameter.
    2. If the user has not provided a Customer ID in a previous message(s) in the chat history, ask the user for the Customer ID.
    3. If a Customer ID is provided by the user in the input, convert it to string and assign it to the customer_id parameter.
    4. Do not prompt user to confirm if the ID provided is a Customer ID, especially if there is only an ID in the input and nothing else. Assume the provided ID is a Customer ID for this tool. 
    5. Only if multiple ids are provided, then ask the user to specify which is the Customer ID if not already specified.
    6. If the user enters an ID that is alphanumeric or longer than 6 digits, assume it is a Customer ID but confirm with the user.
    """ 

    user_type, authorized, pcis_id = check_pcis_id_authorization(state, pcis_input)
    logger.info(f"pcis_id: {pcis_id}, authorized: {authorized}, user_type: {user_type}")    

    if not authorized and pcis_id == "no_pcis":
        no_pcis_message = "You have not provided a PCIS ID. Please provide a valid PCIS ID to view Manual Holds Information."
        return {
            "answer": no_pcis_message,
            "follow_up_questions": [TRY_ANOTHER_PROMPTS]
        }
    elif not authorized:
        no_pcis_auth_message = "You do not have authorization to view Manual Holds Information for provided PCIS ID. "
        return {
            "answer": no_pcis_auth_message,
            "follow_up_questions": [TRY_ANOTHER_PROMPTS]
        }
    else:
        validation_results = await get_customer_validation_results(state, customer_id, pcis_id)
        logger.info(f"****AFTER MAKING FUNCTION: {validation_results}")

        if "answer" in validation_results:
            return validation_results
        elif validation_results.get("valid_association"):
            logger.info(f"Inside ServiceFeeAgreement tool with customer_id: {customer_id}")
            api_url = os.getenv("SERVICE_FEE_AGREEMENT_ENDPOINT")
            headers = {  
                "Content-Type": "application/json"  
            }
            payload = {
                "alliancePartners": ["UHG", "Medica", "Harvard Pilgrim", "Mid-Atlantic Health", "Pacificare"],
                "customerId": str(customer_id),
                "type": "Customer" 
            }

            try:  
                response = await post_with_cert(api_url, payload) 
                if response.status_code == 200:  
                    sfa_data_response = response.json()  
                    sfa_data = sfa_data_response.get('payload', {}).get('data', {})
                    customer_data = sfa_data_response.get("payload", {}).get("data", {}).get("customerDetailsResponse", [])

                    if sfa_data :
                        questions = [
                            "View Customer Details for Customer Number: xxxxx", 
                            "View Compensation Details"
                        ]

                        if customer_data and len(customer_data) > 0:
                            questions = get_followup_questions(questions, customer_data, "service_fee_agreement")

                        userInfo = {
                            "type": user_type,
                            "pcis": pcis_id,
                        }
                        filtered_sfa_data = filterResultsByCRID(sfa_data, validation_results)

                        return {
                            "answer": {
                                "client": "bne_client",
                                "functionality": "compensation", 
                                "response_type": "serviceFeeAgreement",
                                "userInfo": userInfo, 
                                "custValidationResult" : validation_results,
                                "serviceFeeAgreementData": filtered_sfa_data,
                            },
                            "follow_up_questions": questions,
                        }
                    else:
                        no_data_message = "No Service Fee Agreement details found for given Customer ID. Please check the Customer ID provided."
                        return {
                            "answer": no_data_message,
                            "follow_up_questions": [TRY_ANOTHER_PROMPTS]
                        }
                else:  
                    resp_error_message = "I was unable to retrieve service fee agreement details. Please try again or at a later time."
                    return {
                        "answer": resp_error_message,
                        "follow_up_questions": [TRY_ANOTHER_PROMPTS]
                    }  
            except Exception as e:
                await log_tool_error(state, traceback.format_exc(), "serviceFeeAgreement")
                exception_message = "I am unable to retrieve service fee agreement details at this time. Please try at a later time."
                return {
                    "answer": exception_message,
                    "follow_up_questions": [TRY_ANOTHER_PROMPTS]
                } 
        else:
            status_message = "Can't find valid association of PCIS ID and the Customer ID provided. Please try with different Customer ID"
            return {
                "answer": status_message,
                "follow_up_questions": ["Try with another Customer ID: ",TRY_ANOTHER_PROMPTS]
            }

@tool
async def filterServiceFeeAgreement(state: Annotated[dict, InjectedState], customerNumber: str = None, crids: list = None, mapOfContractState: dict = None, issState: str = None, coverage: str = None, chargeLine: str = None) -> dict:    
    """ 
    This tool retrieves the Service Fee Agreement data previously loaded by the serviceFeeAgreement tool and applies filtering
    1. Never call this tool before the serviceFeeAgreement tool has been called at least once even if user asks to call this tool.
    2. The Customer Number (customerNumber) parameter is REQUIRED and must match the one used in the previous serviceFeeAgreement call.
    """

    try:
        # Extract serviceFeeAgreementData from state
        sfa_data = None
        if state and 'messages' in state:
            # Loop through messages to find serviceFeeAgreement response
            for message in state['messages']:
                if hasattr(message, 'content') and isinstance(message.content, str):
                    try:
                        content_dict = json.loads(message.content)
                        if content_dict.get('answer', {}).get('response_type') == 'serviceFeeAgreement' and 'serviceFeeAgreementData' in content_dict.get('answer', {}):
                            sfa_data = content_dict.get('answer', {}).get('serviceFeeAgreementData')
                            logger.info(f"Found SFA data in state: {sfa_data}")
                    except (json.JSONDecodeError, AttributeError):
                        # For ToolMessages that might contain the data directly
                        continue
                elif hasattr(message, 'content') and isinstance(message.content, dict):
                    if message.content.get('response_type') == 'serviceFeeAgreement' and 'serviceFeeAgreementData' in message.content:
                        sfa_data = message.content.get('serviceFeeAgreementData')
                        logger.info(f"Found SFA data in message content dict: {sfa_data}")
                        break
        
        if not sfa_data:
            return {
                "answer": "No Service Fee Agreement data found. Please retrieve Service Fee Agreement data first.",
                "follow_up_questions": [TRY_ANOTHER_PROMPTS]
            }
        
        # Extract values from sfa_data if they aren't already provided
        if sfa_data:
            # Extract customerDetailsResponse data
            customer_details = sfa_data.get('customerDetailsResponse', [{}])[0] if sfa_data.get('customerDetailsResponse') else {}
            sfa_summary = sfa_data.get('sfaSummary', [{}])[0] if sfa_data.get('sfaSummary') else {}
            
            # Use provided values or extract from data if not provided
            if not customerNumber and customer_details.get('srcSysCustNbr'):
                customerNumber = customer_details.get('srcSysCustNbr')
            
            if not crids and customer_details.get('crids'):
                crids = customer_details.get('crids')
            
            # Extract mapOfContractStates directly from customer_details if available
            if not mapOfContractState and customer_details.get('mapOfContractStates'):
                mapOfContractState = customer_details.get('mapOfContractStates')
                logger.info(f"Found mapOfContractStates in customer details: {mapOfContractState}")
            # Fall back to contractIds if mapOfContractStates is not available
            elif not mapOfContractState and customer_details.get('contractIds'):
                contract_ids = customer_details.get('contractIds', [])
                mapOfContractState = {contract_id: [] for contract_id in contract_ids}
                logger.info(f"Created mapOfContractState from contractIds: {mapOfContractState}")
            
            if not issState and sfa_summary.get('issState'):
                issState = sfa_summary.get('issState')
            
            if not coverage and sfa_summary.get('coverage'):
                coverage = sfa_summary.get('coverage')
            
            if not chargeLine and sfa_summary.get('chargeLine'):
                chargeLine = sfa_summary.get('chargeLine')
        
        # Create a payload JSON object with all filter parameters 
        filter_params = {  
            "customerNumber": customerNumber,
            "crids": crids if crids else [],
            "mapOfContractState": mapOfContractState if mapOfContractState else {},
            "issState": issState if issState else "",
            "coverage": coverage if coverage else "",
            "chargeLine": chargeLine if chargeLine else "",
        }
        
        logger.info(f"filterServiceFeeAgreement with customerNumber: {customerNumber}, filter params: {json.dumps(filter_params)}")

        return {
            "answer": {
                "client": "bne_client",
                "functionality": "compensation",
                "response_type": "filterServiceFeeAgreement",
                "serviceFeeAgreementData": sfa_data,
                "filterServiceFeeAgreementData": filter_params
            },
            "follow_up_questions": []
        }
        
    except Exception as e:
        await log_tool_error(state, traceback.format_exc(), "filterServiceFeeAgreement")
        return {
            "answer": "I am unable to filter Service Fee Agreement data at this time. Please try again later.",
            "follow_up_questions": [TRY_ANOTHER_PROMPTS]
        }
        
@tool
async def sfa_summary(compRelId: str, contractId: str, issueState: str, coverageType: str, chargeLine: str, custNbr: str, **kwargs) -> dict:
    """
    This tool with the provided parameters to retrieve service fee agreement summary details.
    1. This tool should be called ONLY after the user has selected values from the filterServiceFeeAgreement.
    2. Required parameters are: compRelId, contractId, issueState, coverageType, chargeLine, and custNbr.
    """
    api_url = os.getenv("SFA_SUMMARY_ENDPOINT")
    
    payload = {
        "compRelId": compRelId,
        "contractId": contractId,
        "issueState": issueState,
        "coverageType": coverageType,
        "chargeLine": chargeLine,
        "custNbr": custNbr,
        "sysCode": "AC",
        "displayList": ["All"],
        "filterSelected": True
    }
    
    for key, value in kwargs.items():
        payload[key] = value
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        response = await post_with_cert(api_url, payload)
        if response.status_code == 200:
            # sfa_summary_data = response.json()
            # data = sfa_summary_data.get('payload', {}).get('data', {})
            data = response.json().get('payload', {}).get('data', {})
            
            if data:
                prompt = f"""
                1. Based on the context, respond with 2 follow-up questions related to SFA summary details.
                2. The follow-up questions should be about different aspects of service fee agreements that users might be interested in.
                3. Keep the questions short and concise, focusing on what a user might want to know next about the SFA details.
                4. Respond in a JSON format with the following structure:
                {{
                    "follow_up_questions": ["Question 1", "Question 2"]
                }}
                """
                
                try:
                    response_prompt = json_model.invoke(prompt).content
                    parsed_json = json.loads(response_prompt)
                    follow_up_questions = parsed_json.get("follow_up_questions", [])
                except Exception as e:
                    logger.info(f"Error fetching follow-up questions from the json_model: {e}")
                    follow_up_questions = [TRY_ANOTHER_PROMPTS]
                
                return {
                    "answer": {
                        "client": "bne_client",
                        "functionality": "compensation",
                        "response_type": "sfaSummary",
                        "sfaSummaryData": data
                    },
                    "follow_up_questions": follow_up_questions.append(TRY_ANOTHER_PROMPTS),   
                }
            else:
                no_data_message = "No SFA summary details found for the provided criteria. Please check your filter selections and try again."
                return {
                    "answer": no_data_message,
                    "follow_up_questions": [TRY_ANOTHER_PROMPTS]
                }
        else:
            resp_error_message = f"Unable to retrieve SFA summary details. Please try again later."
            return {
                "answer": resp_error_message,
                "follow_up_questions": [TRY_ANOTHER_PROMPTS]
            }
    except Exception as e:
        await log_tool_error(state, traceback.format_exc(), "sfa_summary")
        return {
                "answer": "I am unable to retrieve SFA summary details at this time. Please try at a later time.",
                "follow_up_questions": [TRY_ANOTHER_PROMPTS]
        }