from config.db import sync_db_onboarding

# Load client IDs and assistant routers synchronously during module initialization
collection = sync_db_onboarding["Configs"]

client_id_document = collection.find_one({"type": "Client_Assistant_Tool_Mapper"})
if client_id_document and "mapper" in client_id_document:
    valid_client_ids = [client_id for client_id, data in client_id_document["mapper"].items() 
                       if data.get("enabled", False)]
else:
    valid_client_ids = []

valid_assistants = {}
if client_id_document and "mapper" in client_id_document:
    for client_id, client_data in client_id_document["mapper"].items():
        if client_data.get("enabled", False):
            valid_assistants[client_id] = [
                assistant_name
                for assistant_name, assistant_data in client_data.get("assistants", {}).items()
                if assistant_data.get("enabled", False)
            ]

tool_mappings = {}
if client_id_document and "mapper" in client_id_document:
    for client_id, client_data in client_id_document["mapper"].items():
        if client_data.get("enabled", False):
            tool_mappings[client_id] = {}
            
            for assistant_name, assistant_data in client_data.get("assistants", {}).items():
                if assistant_data.get("enabled", False):
                    tool_mappings[client_id][assistant_name] = []
                    
                    if "tools" in assistant_data:
                        tool_mappings[client_id][assistant_name] = [
                            tool_name
                            for tool_name, tool_enabled in assistant_data["tools"].items()
                            if tool_enabled
                        ]
                    else:
                        # If no tools specified, assume all tools are allowed
                        tool_mappings[client_id][assistant_name] = ["all"]

async def validate_client_id(client_id: str):
    """
    Check if the provided client_id is valid.
    
    Args:
        client_id: The client ID to validate
        
    Returns:
        bool: True if the client_id is valid, False otherwise
    """
    return client_id in valid_client_ids

async def validate_assistant(client_id: str, assistant: str):
    """
    Check if the provided assistant is valid for the given client_id.
    
    Args:
        client_id: The validated client ID attempting to access the assistant
        assistant: The name of the assistant or its router tool to validate
        
    Returns:
        bool: True if the assistant is valid for the client_id, False otherwise
    """
    if client_id in valid_assistants:
        return assistant in valid_assistants[client_id]
    return False

async def validate_tool(client_id: str, assistant: str, tool: str):
    """
    Check if the provided tool is valid and enabled for the given client_id and assistant.
    
    Args:
        client_id: The validated client ID 
        assistant: The validated assistant name
        tool: The name of the tool to validate
        
    Returns:
        bool: True if the tool is valid and enabled for the client_id and assistant, False otherwise
    """
    if tool in tool_mappings.get(client_id, {}).get(assistant, []):
        return True
    return False
