import sys
import os
import asyncio
sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..', '..', '..')))
from dotenv import load_dotenv
load_dotenv()
from utils.tool_utils import _print_event
import uuid
# from client_plugins.bneportal.nonauth_documents_search.main import nonauth_documents_search_builder
from client_plugins.bneportal.nonauth_documents_search.document_search_subgraph import init_nonauth_documents_search_graph
from config.postgres import init_auto_commit_pool,init_connection_pool ,close_connection_pools
from graph.state import get_postgres_checkpointer
from utils.subgraph_registry import SubgraphRegistry

async def start_nonauth_documents_search_graph():
    if SubgraphRegistry.get("nonauth_documents_search") is None:
        await init_connection_pool()
        await init_auto_commit_pool()
        await get_postgres_checkpointer()
        await init_nonauth_documents_search_graph()
    nonauth_documents_search_graph = SubgraphRegistry.get("nonauth_documents_search")
    return nonauth_documents_search_graph

_printed = set()

async def run_agent(queryString, session_id, graph, user_name=None, uuid=None, additional_arg=None):
    additional_arg = additional_arg or {}
    thread_id = session_id
    config = {
        "configurable": {
            "thread_id": thread_id,
        }
    }
    user_info = {"client_id": "bne_client", "uuid": uuid, "session_id": session_id, "user_name": user_name}

    events = graph.astream(
        {
            "messages": ("user", queryString),
            "user_info": user_info,
            "is_multiagent": False,
            "is_auth_performed": False,
            "additional_arg": additional_arg,
            "run_mode": "test"
        },
        config,
        stream_mode="values"
    )
    async for event in events:
        response = _print_event(event, _printed)
    return response

async def main():
    graph = await start_nonauth_documents_search_graph()
    thread_id = str(uuid.uuid4())
    valid_user_types = ['Broker', 'Employer', 'Internal']
    input_text = ""
    user_type = ""
    try:
        while user_type not in valid_user_types:
            user_type = input("Enter a BNE User Type `Broker`, `Employer`, or `Internal`: ")
            if user_type not in valid_user_types:
                print("Invalid user type... Try Again!")

        while True:
            input_text = input("Enter your question or press 'q' to exit: ")
            if input_text.lower() in ["quit", "exit", "q"]:
                print("Goodbye! Have a nice day!")
                print("Exiting...")
                break
            response = await run_agent(input_text, thread_id, graph, user_type)
    finally:
        await close_connection_pools()

if __name__ == "__main__":
    asyncio.run(main())