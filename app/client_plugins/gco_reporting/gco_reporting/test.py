import sys
import os
import asyncio
sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..', '..', '..')))
from dotenv import load_dotenv
load_dotenv()
from utils.tool_utils import _print_event
import uuid
from client_plugins.gco_reporting.gco_reporting.gco_reporting_subgraph import init_gco_reporting_graph
from config.postgres import init_connection_pool,init_auto_commit_pool,close_connection_pools
from graph.state import get_postgres_checkpointer
from utils.subgraph_registry import SubgraphRegistry

async def start_gco_reporting_graph():
    if SubgraphRegistry.get("gco_reporting") is None:
        await init_connection_pool()
        await init_auto_commit_pool()
        await get_postgres_checkpointer()
        await init_gco_reporting_graph()
    gco_reporting_graph = SubgraphRegistry.get("gco_reporting")
    return gco_reporting_graph

_printed = set()

async def run_agent(queryString, session_id, graph, user_name=None, uuid=None, additional_arg=None):
    additional_arg = additional_arg or {}
    thread_id = session_id
    config = {
        "configurable": {
            "thread_id": thread_id,
        }
    }
    user_info = {"client_id": "Internal", "uuid": uuid, "session_id": session_id, "user_name": user_name}
    events = graph.astream(
        {
            "messages": ("user", queryString),
            "user_info": user_info,
            "is_multiagent": False,
            "is_auth_performed": False,
            "additional_arg": additional_arg,  
            "run_mode": "test"
        },
        config,
        stream_mode="values"
    )
    async for event in events:
        response = _print_event(event, _printed)
    return response

async def main():
    graph = await start_gco_reporting_graph()
    thread_id = str(uuid.uuid4())
    # additional_arg = {
    #     "client_id": "Internal",
    #     "uuid": "test-uuid-1234",
    #     "agent_name": "portal_form_enrollment",
    #     "application_name": "bneportal",
    # }
    try:
        while True:
            input_text = input("Enter your question or press 'q' to exit: ")
            if input_text.lower() in ["quit", "exit", "q"]:
                print("Goodbye! Have a nice day!")
                print("Exiting...")
                break
            response = await run_agent(input_text, thread_id, graph)
    finally:
        await close_connection_pools()

if __name__ == "__main__":
    asyncio.run(main())
