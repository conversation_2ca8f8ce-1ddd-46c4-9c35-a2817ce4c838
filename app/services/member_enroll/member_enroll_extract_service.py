import io
import os
import traceback
from datetime import datetime

import boto3
from botocore.exceptions import NoCredentialsError
from config.member_enroll_logging_config import member_enroll_logger
import re
from services import log_error_to_db_service
from services.member_enroll.api_service import (
    bne_callback_api,
    email_automation_api,
    process_bne_response,
    xtractor_api,
    sbc_api,
)
from services.member_enroll.mongo_service import (
    update_and_retrieve_counter,
    upsert_enrollment_document,
    upsert_xtractor_document,
    update_and_retrieve_xtractorCounter,
)
from utils.helpers.constants import OEC_DEFAULT_CLIENT_ID

def clean_file_name(file_name):
    pattern = r'[^a-zA-Z0-9_\s-]'

    # Replace any character that does not match the pattern with an empty string
    cleaned_file_name = re.sub(pattern, '', file_name)

    # Replace any sequence of space, hyphen, or underscore with the first character in that sequence
    cleaned_file_name = re.sub(r'([ _-])[ _-]+', r'\1', cleaned_file_name)

    # Remove trailing space, hyphen, or underscore
    cleaned_file_name = re.sub(r'[ _-]+$', '', cleaned_file_name)
    
    return cleaned_file_name

async def upload_to_s3(file_content, job_id, object_name=None):
    """Upload a file to an S3 bucket.
    :param file_content: Content of the file to upload.
    :param job_id: job ID associated with the file_content
    :param object_name: S3 object name.
    :return: Tuple (bool, str). True and an empty string if the file was uploaded successfully, else False and an error message.
    """
    s3_client = boto3.client(  
        's3',  
        aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),  
        aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),  
        region_name="us-east-1",
        endpoint_url=os.getenv('AWS_ENDPOINT')
    )

    bucket_name = os.getenv('AWS_BUCKET_NAME')

    # Construct the object key
    path = os.getenv('AWS_FOLDER')
    object_key = f'{path}/{object_name}'
    file_bytes = io.BytesIO(file_content)

    try:
        # object_key is the full path of the file in the bucket 
        s3_client.upload_fileobj(file_bytes, bucket_name, object_key)
        member_enroll_logger.info("Successfully uploaded %s to S3", object_key, extra={'job_id': job_id})
        return True, ""
    except NoCredentialsError:
        member_enroll_logger.info("Bad Credentials error uploading to S3", extra={'job_id': job_id})
        return False, "S3 credentials are invalid. Check your access and secret key."
    except Exception as e:  
        member_enroll_logger.info("Unexpected error uploading to S3, error: %s", str(e), extra={'job_id': job_id})
        return False, f"An unexpected error occurred while uploading to S3: {str(e)}"

async def handle_xtractor_error(job_id, message, status, source):
    messages = [{"event": status, "source": source, "message": message}]
    mongo_record = {"messages": messages, "jobStatus": "Complete", "processStatus": "Error"}
    updated_doc = await upsert_xtractor_document(job_id, mongo_record)
    client_callback_url = updated_doc["bneCallback"]
    callback_params = {
        "status": "Transaction Error",
        "jobId": job_id,
        "query": updated_doc["requestType"],
        "messages": messages,
        "clientID": updated_doc["clientId"],
        "version": updated_doc["apiVersion"],
        "uuid": updated_doc["uuid"],
    }
    callback_response = await bne_callback_api(callback_params, client_callback_url)
    await process_bne_response(callback_response, job_id, "Error", False)

async def handle_xtractor_extraction(job_id, file_content, json_data):
    xtractor_response = await xtractor_api(file_content, job_id)
    mongo_record = {"xtractorResponse": xtractor_response}

    if xtractor_response.get("status") == "Success":
        mongo_record["jobStatus"] = "Complete"
        mongo_record["processStatus"] = "Success"
        updated_doc = await upsert_xtractor_document(job_id, mongo_record)
        client_callback_url = updated_doc["bneCallback"]
        callback_params = {
            "status": "Success",
            "jobId": job_id,
            "query": updated_doc["requestType"],
            "payload": xtractor_response["data"],
            "clientID": updated_doc["clientId"],
            "version": updated_doc["apiVersion"],
            "uuid": updated_doc["uuid"],
        }
        callback_response = await bne_callback_api(callback_params, client_callback_url)
        await process_bne_response(callback_response, job_id, "Success", False)
    else:
        await handle_xtractor_error(job_id, xtractor_response.get("message"), "Transaction Error", "Lingo")

    member_enroll_logger.info("Finished processing xtractor %s response", xtractor_response.get("status"), extra={'job_id': job_id})

async def handle_sbc_extraction(job_id, file_content, json_data):
    xtractor_sbc_response = await sbc_api(file_content, job_id)
    mongo_record = {"xtractorSbcResponse": xtractor_sbc_response}

    if xtractor_sbc_response.get("status") == "Success":
        mongo_record["jobStatus"] = "Complete"
        mongo_record["processStatus"] = "Success"
        updated_doc = await upsert_xtractor_document(job_id, mongo_record)
        client_callback_url = updated_doc["bneCallback"]
        callback_params = {
            "status": "Success",
            "jobId": job_id,
            "query": updated_doc["requestType"],
            "payload": xtractor_sbc_response["data"],
            "clientID": updated_doc["clientId"],
            "version": updated_doc["apiVersion"],
            "uuid": updated_doc["uuid"],
        }
        callback_response = await bne_callback_api(callback_params, client_callback_url)
        await process_bne_response(callback_response, job_id, "Success", False)
    else:
        await handle_xtractor_error(job_id, xtractor_sbc_response.get("message"), "Transaction Error", "Lingo")

    member_enroll_logger.info("Finished processing SBC xtractor %s response", xtractor_sbc_response.get("status"), extra={'job_id': job_id})

async def process_document_extraction(job_id: int, file_content: bytes, json_data):
    """Process document extraction by uploading to S3, updating MongoDB, and calling email automation API.
    Call the bne callback endpoint with relevant error information if one occurs
    :param job_id: Job ID for the document.
    :param file_content: Content of the file to upload.
    :param json_data: JSON data containing additional information.
    :return: None
    """
    member_enroll_logger.info("Starting document extraction process", extra={'job_id': job_id})
    query_type = ''.join(json_data["query"].split()).lower()
    client_id = ''.join(json_data["clientId"].split())

    try:
        # Check if we should hit SBC xtractor
        if query_type == "sbc" and client_id.lower() == "cd":
            member_enroll_logger.info("Request type is SBC and clientId is CD, calling xtractor SBC API", extra={'job_id': job_id})
            await handle_sbc_extraction(job_id, file_content, json_data)
            return

        # Check if we should hit xtractor
        if query_type != "enrollment":
            member_enroll_logger.info("Request type is not Enrollment, calling xtractor API", extra={'job_id': job_id})
            await handle_xtractor_extraction(job_id, file_content, json_data)
            return

        # Enrollment Step 1: Upload the document to S3
        original_file_name = json_data["fileName"]
        s3_bucket = os.getenv('AWS_BUCKET_NAME')
        path = os.getenv('AWS_FOLDER')

        # Extract the file name before the .pdf extension
        file_name_without_extension = original_file_name.rsplit('.pdf', 1)[0]
        cleaned_file_name = clean_file_name(file_name_without_extension)

        # Append the .pdf extension
        cleaned_file_name_with_extension = f"{cleaned_file_name}.pdf"

        object_name_prefix = ''
        if json_data['clientId'] == 'BNE_MOBILE':
            object_name_prefix = 'bnem_'
        elif json_data['clientId'] == 'BNE_PORTAL':
            object_name_prefix = 'bnep_'

        s3_object_name = f"{object_name_prefix}{job_id}_{cleaned_file_name_with_extension}"

        member_enroll_logger.info("Uploading file to S3: %s", s3_object_name, extra={'job_id': job_id})
        upload_result, error_message = await upload_to_s3(file_content, job_id, s3_object_name)

        # Enrollment Step 2: Update MongoDB with S3 information
        mongo_record = {}
        if upload_result:
            member_enroll_logger.info("File uploaded to S3 successfully: %s", s3_object_name, extra={'job_id': job_id})
            s3_file_location = f"{s3_bucket}/{path}/{s3_object_name}"
            mongo_record["S3FileLocation"] = s3_file_location

            await upsert_enrollment_document(job_id, mongo_record)
            member_enroll_logger.info("Updated MongoDB with S3 file location", extra={'job_id': job_id})
        else:
            member_enroll_logger.error("Error uploading file to S3: %s", error_message, extra={'job_id': job_id})        
            messages = [
                {
                    "event": "Error",
                    "source": "Lingo",
                    "message": error_message
                }
            ]

            mongo_record["processStatus"] = "Error"
            mongo_record["messages"] = messages
            mongo_record["endTime"] = datetime.utcnow().isoformat()

            updated_doc = await upsert_enrollment_document(job_id, mongo_record)
            member_enroll_logger.error("Updated MongoDB with S3 error status", extra={'job_id': job_id})        

            bne_callback_url = updated_doc["bneCallback"]

            bne_callback_params = {
                "status": "Error",
                "jobId": job_id,
                "messages": messages,
                "query": "Enrollment",
                "clientID": updated_doc["clientId"],
                "version": updated_doc["apiVersion"],
                "uuid": updated_doc["uuid"],         
            }

            # Call the BNE callback because there was an error
            callback_response = await bne_callback_api(bne_callback_params, bne_callback_url)

            await process_bne_response(callback_response, job_id, "Error", True)
            member_enroll_logger.info("Finished processing S3 error response", extra={'job_id': job_id})

            # Stop the process because the s3 upload had an issue
            return

        # Enrollment Step 3: Call the email automation API and get the initial response
        base_url = os.getenv("LINGO_OEC_BASE_URL")
        lingo_callback_url = f"{base_url}docExtractionCallback"
        email_params = {
            "jobId": str(job_id),
            "s3": f"{s3_bucket}/{path}/{s3_object_name}",
            "uuid": json_data["uuid"],
            "clientId": json_data["clientId"],
            "requestType": "Enrollment",
            "callbackAPI": lingo_callback_url,
            "version": json_data["version"],
        }
        member_enroll_logger.info("Calling email automation API with params: %s", email_params, extra={'job_id': job_id})    
        initial_email_response = await email_automation_api(email_params)
        member_enroll_logger.info("Initial email automation API response: %s", initial_email_response, extra={'job_id': job_id})  

        updated_mongo_record = {"emailAutomationResponse": initial_email_response}
        await upsert_enrollment_document(job_id, updated_mongo_record)

        if "error" in initial_email_response:
            member_enroll_logger.error("Error in email automation API response: %s", initial_email_response["error"], extra={'job_id': job_id})
            error_messages = [{
                "event": "Transaction Error",
                "source": "SAL",
                "message": initial_email_response["error"]
            }]

            mongo_record = {"messages": error_messages}
            updated_doc = await upsert_enrollment_document(job_id, mongo_record)

            bne_callback_url = updated_doc["bneCallback"]
            bne_callback_params = {
                "status": "Error",
                "jobId": email_params["jobId"],
                "messages": error_messages,
                "query": "Enrollment",
                "clientID": updated_doc["clientId"],
                "version": json_data["version"],
                "uuid": updated_doc["uuid"],
            }   

            # Call the BNE callback because there was a SAL error
            callback_response = await bne_callback_api(bne_callback_params, bne_callback_url)

            await process_bne_response(callback_response, job_id, "Error", True)
            member_enroll_logger.info("Finished processing email automation error response", extra={'job_id': job_id})
    except Exception as e:
        member_enroll_logger.error("Unexpected error processing document extraction: %s", str(e), extra={'job_id': job_id})
        traceback.print_exc()
        await log_error_to_db_service.log_error_to_db(traceback.format_exc(), OEC_DEFAULT_CLIENT_ID, None, None, "Code Break")  
      
    member_enroll_logger.info("Completed document extraction process", extra={'job_id': job_id})

async def handle_document_extraction(file_content, json_data, background_tasks):
    """Handle document extraction by creating a job, validating the file type, updating MongoDB, and processing the document.
    :param file_content: The decoded file content to be processed.
    :param json_data: JSON data containing additional request information.
    :param background_tasks: Background tasks to be executed after the response is sent.
    :return: A dictionary with the job status and messages.
    """    
    query_type = ''.join(json_data["query"].split()).lower()
    job_id = ''
    status_code = 200
    if query_type == "enrollment":
        job_id = await update_and_retrieve_counter()
    else:
        job_id = await update_and_retrieve_xtractorCounter()
    
    
    member_enroll_logger.info("Job ID created", extra={'job_id': job_id})
    if not job_id:
        member_enroll_logger.error("Error creating jobId in MongoDB")
        return 500, {
            "status": "Transaction Error", 
            "messages": [
                {
                    "event": "Error",
                    "source": "Lingo",
                    "message": "Error creating jobId in MongoDB"
                }
            ]
        }

    mongo_record = {
        "jobId": job_id,
        "jobStatus": "Processing",
        "processStatus": "Processing",
        "clientId": json_data["clientId"],
        "uuid": json_data["uuid"],
        "requestType": json_data["query"],
        "apiVersion": json_data["version"],
        "bneCallback": json_data["callbackAPI"],
        "startTime": datetime.utcnow().isoformat()
    }

    # Check if file type passed is a pdf or png
    is_pdf = file_content[:4] == b'%PDF'
    is_png = file_content[:8] == b'\x89PNG\r\n\x1a\n'

    json_data["is_pdf"] = is_pdf

    bne_response = {
        "jobId": job_id,
    }

    # If the file isn't a pdf or png and it's not an Enrollment, respond with an error (xtractor only supports pdf, png)
    if not is_pdf and not is_png and query_type != "enrollment":
        member_enroll_logger.error("Unsupported file type for non-enrollment form", extra={'job_id': job_id})

        messages = [
            {
                "event": "Error",
                "source": "Lingo",
                "message": "Unsupported file type. Only PDF and PNG files are supported for non-enrollment forms."
            }
        ]
        mongo_record["jobStatus"] = "Completed"
        mongo_record["processStatus"] = "Error"
        mongo_record["messages"] = messages
        mongo_record["endTime"] = datetime.utcnow().isoformat()

        bne_response["status"] = "Transaction Error"
        bne_response["messages"] = messages

        await upsert_xtractor_document(job_id, mongo_record)

        member_enroll_logger.info("Response to BNE for unsupported file type: %s", bne_response, extra={'job_id': job_id})

        return 400, bne_response

    # Valid combinations
    if (is_pdf and query_type == "enrollment") or (is_pdf or is_png and query_type != "enrollment"):
        member_enroll_logger.info("File type is valid: %s", "pdf" if is_pdf else "png", extra={'job_id': job_id})

        bne_response["status"] = "Processing"
        mongo_record["fileType"] = "pdf" if is_pdf else "png"
        mongo_record["fileName"] = json_data["fileName"]
        if query_type == "enrollment":
            await upsert_enrollment_document(job_id, mongo_record)
        else:
            await upsert_xtractor_document(job_id, mongo_record)
    else:
        member_enroll_logger.error(
            "Unsupported file type with combination query: %s, pdf: %s, png: %s",
            query_type,
            is_pdf,
            is_png,
            extra={"job_id": job_id},
        )
        # Create Response with Error as given in response payload and then return that response back
        messages = [
            {
                "event": "Error",
                "source": "Lingo",
                "message": f"Unsupported file type for query {json_data['query']} combination."
            }
        ]
        mongo_record["jobStatus"] = "Completed"
        mongo_record["processStatus"] = "Error"
        mongo_record["messages"] = messages
        mongo_record["endTime"] = datetime.utcnow().isoformat()

        bne_response["status"] = "Transaction Error"
        bne_response["messages"] = messages

        if query_type == "enrollment":
            await upsert_enrollment_document(job_id, mongo_record)
        else:
            await upsert_xtractor_document(job_id, mongo_record)

        member_enroll_logger.info("Response to BNE for unsupported file type: %s", bne_response, extra={'job_id': job_id})

        return 400, bne_response

    # Tasks to run in the background after response is sent back
    background_tasks.add_task(process_document_extraction, job_id, file_content, json_data)

    member_enroll_logger.info("Initial response to BNE: %s", bne_response, extra={'job_id': job_id})

    return status_code, bne_response
