import os

BNE_MEDICAL_PHARMACY_PLANS_URL = "/api/bne/member/secure/v1.0/medical-pharmacy-plans"
BNE_CONTRACT_OPTIONS_URL = "/api/bne/member/secure/v3.0/contractoptions"
BNE_DOCUMENT_URL = "/api/bne/reports/secure/v4.0/document"
BNE_GET_TOKEN_URL = "https://bneportal-dev-polaris-member.prod.internal-gcp.optum.com/api/bne/member/getTokenForInternalApi"
BNE_GET_FORMS_URL = "https://www.uhceservices.com/api/bne/reports/member/v4.0/document/search"

def get_bne_base_url():
    """
    Returns the base URL for the BNE portal.
    """
    lingo_env = os.environ.get("ENV", "").lower()
    if lingo_env in ["dev", "demo", "stage"]:
        return "https://uhceservices-stg.optum.com"
    elif lingo_env == "prod":
        return "https://www.uhceservices.com"