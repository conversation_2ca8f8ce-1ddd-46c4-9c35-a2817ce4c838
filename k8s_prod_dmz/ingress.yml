apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: lingo
  labels:
    app: lingo
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    cert-manager.io/cluster-issuer: "optum-app"
    cert-manager.io/common-name: "lingoai-api.optum.com"
spec:
  ingressClassName: nginx
  rules:
  - host: lingoai-api.optum.com
    http:
      paths:
      - pathType: Prefix
        path: "/"
        backend:
          service:
            name: lingo-api
            port:
              number: 8080
  - host: lingoai-api-datacenter.optum.com
    http:
      paths:
      - pathType: Prefix
        path: "/"
        backend:
          service:
            name: lingo-api
            port:
              number: 8080
  tls:
    - hosts: 
        - lingoai-api.optum.com
        - lingoai-api-datacenter.optum.com
      secretName: lingo-api-ingress-secret