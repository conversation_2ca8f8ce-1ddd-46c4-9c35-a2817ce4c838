from langgraph.graph import StateGraph, START
from graph.state import State, get_postgres_checkpointer
from graph.nodes import Assistant
from langgraph.pregel import RetryPolicy
from graph.chains import fetch_primary_assistant_runnable
from langgraph.prebuilt import ToolNode
from utils.helpers.constants import TOOL_CALL_ERROR_MESSAGE
from graph.nodes import restrict_parallel_agents_run, invalid_assistant
from subgraph.router import route_to_workflow, route_primary_assistant
from utils.subgraph_utils import create_entry_message
from utils.subgraph_builder import BaseSubgraphBuilder
from utils.subgraph_registry import SubgraphRegistry

supervisor_compiled_graph = None

async def init_supervisor_graph():
    """
    Initialize the supervisor graph using the BaseSubgraphBuilder pattern
    and register it with SubgraphRegistry.
    """
    global supervisor_compiled_graph
    if supervisor_compiled_graph is None:
        # Get the compiled subgraphs from the registry
        cirrus_compiled_graph = SubgraphRegistry.get("cirrus")
        surest_compiled_graph = SubgraphRegistry.get("surest")

        # Create a new builder with fluent interface
        builder = BaseSubgraphBuilder(State)
        
        # Get tools and assistant
        primary_runnable, primary_tools = fetch_primary_assistant_runnable('Internal')
        primary_assistant_tool_node = ToolNode(primary_tools, handle_tool_errors=TOOL_CALL_ERROR_MESSAGE)
        
        # Add nodes
        builder.add_node("primary", Assistant(primary_runnable), retry=RetryPolicy(max_attempts=2))
        builder.add_node("enter_cirrus_assistant", create_entry_message('cirrus assistant', 'cirrus_subgraph'))
        builder.add_node("cirrus_subgraph", cirrus_compiled_graph)
        builder.add_node("enter_surest_assistant", create_entry_message('surest assistant', 'surest_subgraph'))
        builder.add_node("surest_subgraph", surest_compiled_graph)
        builder.add_node("invalid_tool", primary_assistant_tool_node)
        builder.add_node("restrict_parallel_agents_run", restrict_parallel_agents_run)
        builder.add_node("invalid_assistant", invalid_assistant)
        
        
        # Add edges
        builder.add_edge("enter_cirrus_assistant", "cirrus_subgraph")
        builder.add_edge("enter_surest_assistant", "surest_subgraph")
        builder.add_edge("invalid_tool", "primary")
        builder.add_edge("restrict_parallel_agents_run", "primary")
        # Set conditional entry point
        builder.add_conditional_edges(START, route_to_workflow)
        # Add conditional edges for routing
        builder.add_conditional_edges("primary", route_primary_assistant)
        
        # Compile with async checkpointer
        supervisor_compiled_graph = builder.compile(checkpointer=await get_postgres_checkpointer())
        
        # Register with SubgraphRegistry for future access
        SubgraphRegistry.register("supervisor", supervisor_compiled_graph)