import psutil
import time
import os
import threading
from contextlib import contextmanager

class SystemMetrics:
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(SystemMetrics, cls).__new__(cls)
                cls._instance.process = psutil.Process(os.getpid())
                cls._instance.events = {}
            return cls._instance
    
    @contextmanager
    def track_event(self, event_id):
        """Context manager to track metrics for a specific event."""
        start_time = time.time()
        
        # Get initial CPU percentage to establish baseline
        initial_cpu = self.process.cpu_percent(interval=None)
        
        start_cpu_times = self.process.cpu_times()
        start_memory = self.process.memory_info().rss // (1024 * 1024)  # MB
        
        # Update events dictionary immediately with initial data
        self.events[event_id] = {
            "started_at": start_time,
            "status": "in_progress"
        }
        
        try:
            # Yield the event_id so the calling code can access it
            yield {"event_id": event_id}
        finally:
            end_time = time.time()
            end_cpu_times = self.process.cpu_times()
            end_memory = self.process.memory_info().rss // (1024 * 1024)  # MB
            
            # Calculate deltas
            cpu_user = end_cpu_times.user - start_cpu_times.user
            cpu_system = end_cpu_times.system - start_cpu_times.system
            latency = end_time - start_time
            memory_change = end_memory - start_memory
            
            # Get a more accurate CPU percentage with a small interval
            cpu_usage = self.process.cpu_percent(interval=None)  # Non-blocking, measures since last call
            memory_usage = self.process.memory_info().rss // (1024 * 1024)  # Process memory in MB
            
            self.events[event_id] = {
                "latency": f"{latency:.4f}s",
                # "cpu_user": f"{cpu_user:.4f}s", #Metrics that are not needed currently
                # "cpu_system": f"{cpu_system:.4f}s", #Metrics that are not needed currently
                # "memory_change": f"{memory_change}MB", #Metrics that are not needed currently
                "cpu_usage": f"{cpu_usage:.1f}%",
                "memory_usage": f"{memory_usage}MB",
            }
    @staticmethod
    def get_metrics():
        """Return process-specific metrics as a dictionary."""
        process = psutil.Process(os.getpid())
        cpu_usage = process.cpu_percent(interval=None)
        memory_usage = process.memory_info().rss // (1024 * 1024)  # in MB
        return {
            "cpu_usage": f"{cpu_usage}%",
            "memory_usage": f"{memory_usage}MB"
        }
    
    def get_event_metrics(self, event_id):
        """Get metrics for a specific event."""
        return self.events.get(event_id, {})

    def clear_event(self, event_id):
        """Remove metrics for a specific event."""
        if event_id in self.events:
            del self.events[event_id]

    def _cleanup_old_events(self, max_age_seconds=3600):
        """Internal method to remove old events."""
        current_time = time.time()
        threshold = current_time - max_age_seconds

        self.events = {k: v for k, v in self.events.items()
                       if v.get("started_at", current_time) >= threshold}

    def clear_all_events(self):
        """Clear all events from memory."""
        self.events.clear()