from client_plugins.bneportal.compensation.utils import check_pcis_id_authorization, validate_cust_crid, get_followup_questions, get_customer_validation_results, get_logger, post_with_cert, filterResultsByCRID
import requests
from typing import Annotated
from langgraph.prebuilt.tool_node import InjectedState
import json
import os
from config.openAI import json_model
from langchain_core.tools import tool
from utils.tool_utils import log_tool_error
from datetime import datetime 
import traceback

TRY_ANOTHER_PROMPTS = "View Compensation Prompts"
ALLIANCE_PARTNERS = ["UHG","Medica","Harvard Pilgrim","Mid-Atlantic Health","Pacificare"]
logger = get_logger(__name__)

@tool
async def customer_details(state: Annotated[dict, InjectedState], customer_id: str, pcis_input: str = None, sourceSystem: str = None) -> dict:
    """
    This tool will give the Customer details/information based on the Customer ID and Source System provided by the user.
    1. The Customer ID is needed and required. The Customer ID is a 6 or 7-digit numerical or alphanumeric value.
    2. If the user has provided a customer ID in previous message(s) in the chat history, use the most recent customer ID given and assign it to the customer_id parameter. But the user must have explicitly given the customer ID in the previous message(s) in the chat history, dont just assume customer ID.
    3. If the user has not provided a customer ID in a previous message(s) in the chat history and the model is not able to get it, then prompt/ask the broker for the customer ID and assign it to the customer_id parameter.
    4. Never prompt the user to provide a PCIS ID. Only if the user has explicitly provided/mentioned a PCIS ID/pcis id in the input without being asked, assign that value to the pcis_input variable. Otherwise, don't do anything.
    5. Do not prompt user to confirm if the ID provided is a Customer ID, especially if there is only an ID in the input and nothing else. Assume the provided ID is a Customer ID for this tool. 
    6. Only if multiple ids are provided, then ask the user to specify which is the is a Customer ID if not already specified.
    """

    user_type, authorized, pcis_id = check_pcis_id_authorization(state, pcis_input)

    logger.info(f"pcis_id: {pcis_id}, authorized: {authorized}, user_type: {user_type}")

    if not authorized and pcis_id == "no_pcis":
        no_pcis_message = "You have not provided a PCIS ID. Please provide a valid PCIS ID to view Customer Details."
        return {
            "answer": no_pcis_message,
            "follow_up_questions": [TRY_ANOTHER_PROMPTS]
        }
    elif not authorized:
        no_pcis_auth_message = "You do not have authorization to view Customer Details for provided PCIS ID. "
        return {
            "answer": no_pcis_auth_message,
            "follow_up_questions": [TRY_ANOTHER_PROMPTS]
        }
    else:
        validation_results = await get_customer_validation_results(state, customer_id, pcis_id)
        logger.info(f"***AFTER MAKING FUNCTION ******** {validation_results}")

        if "answer" in validation_results:
            return validation_results
        elif validation_results.get("valid_association"):

            api_url = os.getenv("CUSTOMER_DETAILS_ENDPOINT")
            headers = {
                "Content-Type": "application/json"
            }
            payload = {"alliancePartners": ALLIANCE_PARTNERS,
                       "customerId":customer_id,"sourceSystem":sourceSystem,"isMultipleCustomers":True,"type":"Customer"} 
            

            logger.info(f"Payload for customer details API: {payload}")
            try:
                response = await post_with_cert(api_url, payload)
                if response.status_code == 200:
                    response_json = response.json()
                    if response_json.get('errorMsg'):
                        error_message = f"Unable to retrieve customer information at this time. Please try again later."
                        return {
                            "answer": error_message,
                            "follow_up_questions": ["Try with another Customer ID: ",TRY_ANOTHER_PROMPTS]
                        }
                        
                    customer_data = response_json.get("payload", {}).get("data", {}).get("customerDetailsResponse", [])
                    logger.info(f"customer_data API Response: {customer_data}")
                    if customer_data and len(customer_data) > 1:
                        return {
                            "answer": {
                                "client": "bne_client",
                                "functionality": "compensation",
                                "response_type": "customerDetailsWithMultipleCustomers",
                                "customerDetailsData": customer_data,
                                
                            },
                            "follow_up_questions": ["Try with another Customer ID: ",TRY_ANOTHER_PROMPTS]
                        }
                                        
                    if customer_data and len(customer_data) > 0:
                        questions = [
                            "View Customer Details with Customer Number: xxxxx", 
                            "View Compensation Details"
                        ]
                        questions = get_followup_questions(questions, customer_data, "customer_details")

                        user_info = {
                            "type": user_type,
                            "pcis": pcis_id,
                        }
                        
                        return {
                            "answer": {
                                "client": "bne_client",
                                "functionality": "compensation",
                                "response_type": "customerDetails",
                                "customerDetailsData": customer_data,
                                "userInfo": user_info,
                                "custValidationResult" : validation_results,
                            },
                            "follow_up_questions": questions  
                        }
                    else:
                        logger.info(f"No customer data found or empty array returned.")
                        return {
                            "answer": f"No customer information found for customer ID {customer_id}. This customer may not exist in our records.",
                            "follow_up_questions": ["Try with another customer ID: ",TRY_ANOTHER_PROMPTS]
                        }
                else:
                    status_message = f"Unable to retrieve customer information at this time. Please try again later."
                    return {
                        "answer": status_message,
                        "follow_up_questions": ["Try with another customer ID: ",TRY_ANOTHER_PROMPTS]
                    }
            except Exception as e:
                await log_tool_error(state, traceback.format_exc(), "customer_details")
                return {
                    "answer": "I am unable to retrieve customer information at this time. Please try at a later time.",
                    "follow_up_questions": ["Try with another customer ID: ",TRY_ANOTHER_PROMPTS]
                }
        else:
            status_message = "Can't find valid association between PCIS ID and the Customer ID provided. Please try with different Customer ID"
            return {
                "answer": status_message,
                "follow_up_questions": ["Try with another customer ID: ",TRY_ANOTHER_PROMPTS]
            }
    
@tool
async def customer_override(state: Annotated[dict, InjectedState], customer_id:str, pcis_input: str = None) -> dict:
    """
    This tool will give the customer override information based on the customer ID provided by the broker.
    1. The Customer ID is needed and required. The Customer ID is a 6 or 7-digit numerical or alphanumeric value.
    2. If the user has provided a customer ID in previous message(s) in the chat history, use the most recent customer ID given and assign it to the customer_id parameter. But the user must have explicitly given the customer ID in the previous message(s) in the chat history, dont just assume customer ID.
    3. If the user has not provided a customer ID in a previous message(s) in the chat history and the model is not able to get it, then prompt/ask the broker for the customer ID and assign it to the customer_id parameter.
    4. Never prompt the user to provide a PCIS ID. Only if the user has explicitly provided/mentioned a PCIS ID/pcis id in the input without being asked, assign that value to the pcis_input variable. Otherwise, don't do anything.
    5. Do not prompt user to confirm if the ID provided is a Customer ID, especially if there is only an ID in the input and nothing else. Assume the provided ID is a Customer ID for this tool. 
    6. Only if multiple ids are provided, then ask the user to specify which is the is a Customer ID if not already specified.
    """

    user_type, authorized, pcis_id = check_pcis_id_authorization(state, pcis_input)
    logger.info(f"pcis_id: {pcis_id}, authorized: {authorized}, user_type: {user_type}")

    if not authorized and pcis_id == "no_pcis":
        no_pcis_message = "You have not provided a PCIS ID. Please provide a valid PCIS ID to view Customer Override Information."
        return {
            "answer": no_pcis_message,
            "follow_up_questions": [TRY_ANOTHER_PROMPTS]
        }
    elif not authorized:
        no_pcis_auth_message = "You do not have authorization to view Customer Override Information for provided PCIS ID. "
        return {
            "answer": no_pcis_auth_message,
            "follow_up_questions": [TRY_ANOTHER_PROMPTS]
        }
    else:
        validation_results = await get_customer_validation_results(state, customer_id, pcis_id)
        logger.info(f"***AFTER MAKING FUNCTION ******** {validation_results}")

        if "answer" in validation_results:
            return validation_results
        elif validation_results.get("valid_association"):
            logger.info(f"*****Inside customerOverride tool function with customer_id: {customer_id}*********")
            api_url = os.getenv("CUSTOMER_OVERRIDE_ENDPOINT")
            headers = {
                "Content-Type": "application/json"
            }
            payload = {
                "alliancePartners": ALLIANCE_PARTNERS,
                "customerId": customer_id,
                "type": "customer",
            }
            try:
                response = await post_with_cert(api_url, payload)
                if response.status_code == 200:
                    response_json = response.json()
                    # Check for error message in the response
                    if response_json.get('errorMsg'):
                        error_message = f"No customer override information found for customer ID {customer_id}. Please verify the customer ID and try again."
                        return {
                            "answer": error_message,
                            "follow_up_questions": ["Try with another customer ID: ",TRY_ANOTHER_PROMPTS]
                        }
                    
                    customer_data = response_json.get("payload", {}).get("data", {}).get("customerDetailsResponse", [])
                    override_details = response_json.get('payload', {}).get('data',{}).get('overrideDetails', [])
                    customer_override_data = override_details if override_details else []
                    
                    logger.info(f"customer_override_data API Response: {customer_override_data}")
                    filtered_override_data = filterResultsByCRID(customer_override_data, validation_results)
                    questions = [
                        "View Customer Details for Customer Number: xxxxx", 
                        "View Compensation Details"
                    ]

                    if customer_data and len(customer_data) > 0:
                        questions = get_followup_questions(questions, customer_data, "customer_override")

                    user_info = {
                        "type": user_type,
                        "pcis": pcis_id,
                    }
                    
                    if customer_override_data and len(customer_override_data) > 0:
                        logger.info(f"Found {len(customer_override_data)} customer overrides. Returning data.")
                        return {
                            "answer": {
                                "client": "bne_client",
                                "functionality": "compensation",
                                "response_type": "customerOverride",
                                "customerOverrideData": filtered_override_data,
                                "userInfo": user_info,
                                "custValidationResult" : validation_results,
                            },
                            "follow_up_questions": questions 
                        }
                    else:
                        return {
                            "answer": f"No override information found for customer ID {customer_id}. This customer may not have any overrides.",
                            "follow_up_questions": ["Try with another customer ID: ",TRY_ANOTHER_PROMPTS]
                        }
                else:
                    status_message = f"Unable to retrieve customer override information. Status code: {response.status_code}. Please try again later."
                    return {
                        "answer": status_message,
                        "follow_up_questions": ["Try with another customer ID: ",TRY_ANOTHER_PROMPTS]
                    }
            except Exception as e:
                await log_tool_error(state, traceback.format_exc(), "customer_override")
                return {
                    "answer": "I am unable to retrieve customer override information at this time. Please try at a later time.",
                    "follow_up_questions": ["Try with another customer ID: ",TRY_ANOTHER_PROMPTS]
                }
        else:
            status_message = "Can't find valid association of PCIS ID and the Customer ID provided. Please try with different Customer ID"
            return {
                "answer": status_message,
                "follow_up_questions": ["Try with another customer ID: ",TRY_ANOTHER_PROMPTS]
            }
                  
@tool
async def manual_holds(state: Annotated[dict, InjectedState], customer_id: str, pcis_input: str = None) -> dict:
    """
    This tool will give the manual holds information based on the customer ID provided by the broker.
    1. The Customer ID is needed and required. The Customer ID is a 6 or 7-digit numerical or alphanumeric value.
    2. If the user has provided a customer ID in previous message(s) in the chat history, use the most recent customer ID given and assign it to the customer_id parameter. But the user must have explicitly given the customer ID in the previous message(s) in the chat history, dont just assume customer ID.
    3. If the user has not provided a customer ID in a previous message(s) in the chat history and the model is not able to get it, then prompt/ask the broker for the customer ID and assign it to the customer_id parameter.
    4. Never prompt the user to provide a PCIS ID. Only if the user has explicitly provided/mentioned a PCIS ID/pcis id in the input without being asked, assign that value to the pcis_input variable. Otherwise, don't do anything.
    5. Do not prompt user to confirm if the ID provided is a Customer ID, especially if there is only an ID in the input and nothing else. Assume the provided ID is a Customer ID for this tool. 
    6. Only if multiple ids are provided, then ask the user to specify which is the is a Customer ID if not already specified.
    """

    user_type, authorized, pcis_id = check_pcis_id_authorization(state, pcis_input)
    logger.info(f"pcis_id: {pcis_id}, authorized: {authorized}, user_type: {user_type}")

    if not authorized and pcis_id == "no_pcis":
        no_pcis_message = "You have not provided a PCIS ID. Please provide a valid PCIS ID to view Manual Holds Information."
        return {
            "answer": no_pcis_message,
            "follow_up_questions": [TRY_ANOTHER_PROMPTS]
        }
    elif not authorized:
        no_pcis_auth_message = "You do not have authorization to view Manual Holds Information for provided PCIS ID. "
        return {
            "answer": no_pcis_auth_message,
            "follow_up_questions": [TRY_ANOTHER_PROMPTS]
        }
    else:
        validation_results = await get_customer_validation_results(state, customer_id, pcis_id)
        logger.info(f"***AFTER MAKING FUNCTION ******** {validation_results}")

        if "answer" in validation_results:
            return validation_results
        elif validation_results.get("valid_association"):  
            logger.info(f"*****Inside Manual Holds tool*****")
            api_url = os.getenv("MANUAL_HOLDS_ENDPOINT")
            headers = {
                "Content-Type": "application/json"
            }
            payload = {
                "alliancePartners": ALLIANCE_PARTNERS,
                "customerId": customer_id,
                "moduleName" : "",
                "type": "customer",
            }

            try:
                response = await post_with_cert(api_url, payload)
                if response.status_code == 200:
                    response_json = response.json()
                    # Check for error message in the response
                    if response_json.get('errorMsg'):
                        error_message = f"No manual holds found for customer ID {customer_id}. Please verify the customer ID and try again."
                        return {
                            "answer": error_message,
                            "follow_up_questions": ["Try with another customer ID: ",TRY_ANOTHER_PROMPTS]
                        }
                    
                    customer_data = response_json.get("payload", {}).get("data", {}).get("customerDetailsResponse", [])
                    manual_holds_data = response_json.get('payload', {}).get('data', {}).get('customerManualHoldsSummary', [])
                    logger.info(f"manual_holds_data API Response: {manual_holds_data}")

                    questions = [
                        "View Customer Details for Customer Number: xxxxx",
                        "View Compensation Details"
                    ]

                    if customer_data and len(customer_data) > 0:
                        questions = get_followup_questions(questions, customer_data, "manual_holds")

                    user_info = {
                        "type": user_type,
                        "pcis": pcis_id,
                    }
                    
                    # Explicitly check if data exists (not empty array or None)
                    if manual_holds_data and len(manual_holds_data) > 0:
                        return {
                            "answer": {
                                "client": "bne_client",
                                "functionality": "compensation",
                                "response_type": "manualHolds",
                                "manualHoldsData": manual_holds_data,
                                "userInfo": user_info,
                                "custValidationResult" : validation_results,
                            },
                            "follow_up_questions": questions
                        }
                    else:
                        logger.info(f"No manual holds data found or empty array returned.")
                        return {
                            "answer": f"No manual holds found for Customer ID {customer_id}. This customer may not have any manual holds.",
                            "follow_up_questions": ["Try with another customer ID: ",TRY_ANOTHER_PROMPTS]
                        }
                else:
                    status_message = f"Unable to retrieve manual holds information. Status code: {response.status_code}. Please try again later."
                    return {
                        "answer": status_message,
                        "follow_up_questions": ["Try with another customer ID: ",TRY_ANOTHER_PROMPTS]
                    }
            except Exception as e:
                await log_tool_error(state, traceback.format_exc(), "manual_holds")
                return {
                    "answer": "I am unable to retrieve manual holds information at this time. Please try at a later time.",
                    "follow_up_questions": ["Try with another customer ID: ",TRY_ANOTHER_PROMPTS]
                }
        else:
            status_message = "Can't find valid association of PCIS ID and the Customer ID provided. Please try with different Customer ID"
            return {
                "answer": status_message,
                "follow_up_questions": ["Try with another customer ID: ",TRY_ANOTHER_PROMPTS]
            }