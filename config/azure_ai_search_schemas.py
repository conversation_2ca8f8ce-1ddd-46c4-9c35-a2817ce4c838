from azure.search.documents.indexes.models import (
    SearchIndex,
    SearchField,
    SearchFieldDataType,
    SimpleField,
    SearchableField,
    VectorSearch,
    VectorSearchProfile,
    VectorSearchAlgorithmConfiguration,
    HnswAlgorithmConfiguration,
    VectorSearchAlgorithmKind,
    VectorSearchAlgorithmMetric,
    SemanticConfiguration,
    SemanticSearch,
    SemanticPrioritizedFields,
    SemanticField
)
from config.azure_ai_search import (
    SUREST_TRAINING_MATERIAL_INDEX,
    SUREST_TRAINING_VIDEO_INDEX,
    BNE_TRAINING_DOCUMENT_INDEX
)

def create_vector_search_config():
    """Create vector search configuration for all indexes"""
    return VectorSearch(
        profiles=[
            VectorSearchProfile(
                name="default-vector-profile",
                algorithm_configuration_name="default-hnsw-config"
            )
        ],
        algorithms=[
            HnswAlgorithmConfiguration(
                name="default-hnsw-config",
                kind=VectorSearchAlgorithmKind.HNSW,
                parameters={
                    "m": 4,
                    "efConstruction": 400,
                    "efSearch": 500,
                    "metric": VectorSearchAlgorithmMetric.COSINE
                }
            )
        ]
    )

def create_semantic_search_config(index_name: str):
    """Create semantic search configuration"""
    return SemanticSearch(
        configurations=[
            SemanticConfiguration(
                name="default-semantic-config",
                prioritized_fields=SemanticPrioritizedFields(
                    content_fields=[
                        SemanticField(field_name="page_content")
                    ],
                    keywords_fields=[
                        SemanticField(field_name="file_name")
                    ]
                )
            )
        ]
    )

def create_surest_training_material_index():
    """Create index schema for Surest training materials"""
    fields = [
        SimpleField(
            name="id",
            type=SearchFieldDataType.String,
            key=True,
            filterable=True
        ),
        SearchableField(
            name="file_name",
            type=SearchFieldDataType.String,
            searchable=True,
            filterable=True,
            sortable=True
        ),
        SimpleField(
            name="file_url",
            type=SearchFieldDataType.String,
            filterable=True
        ),
        SearchableField(
            name="page_content",
            type=SearchFieldDataType.String,
            searchable=True,
            analyzer_name="en.microsoft"
        ),
        SimpleField(
            name="created_at",
            type=SearchFieldDataType.DateTimeOffset,
            filterable=True,
            sortable=True
        ),
        SearchField(
            name="embedding",
            type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
            searchable=True,
            vector_search_dimensions=1536,  # text-embedding-3-small dimension
            vector_search_profile_name="default-vector-profile"
        ),
        SimpleField(
            name="score",
            type=SearchFieldDataType.Double,
            filterable=True,
            sortable=True
        )
    ]
    
    return SearchIndex(
        name=SUREST_TRAINING_MATERIAL_INDEX,
        fields=fields,
        vector_search=create_vector_search_config(),
        semantic_search=create_semantic_search_config(SUREST_TRAINING_MATERIAL_INDEX)
    )

def create_surest_training_video_index():
    """Create index schema for Surest training videos"""
    fields = [
        SimpleField(
            name="id",
            type=SearchFieldDataType.String,
            key=True,
            filterable=True
        ),
        SearchableField(
            name="file_name",
            type=SearchFieldDataType.String,
            searchable=True,
            filterable=True,
            sortable=True
        ),
        SimpleField(
            name="file_url",
            type=SearchFieldDataType.String,
            filterable=True
        ),
        SearchableField(
            name="page_content",
            type=SearchFieldDataType.String,
            searchable=True,
            analyzer_name="en.microsoft"
        ),
        SimpleField(
            name="created_at",
            type=SearchFieldDataType.DateTimeOffset,
            filterable=True,
            sortable=True
        ),
        SearchField(
            name="embedding",
            type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
            searchable=True,
            vector_search_dimensions=1536,  # text-embedding-3-small dimension
            vector_search_profile_name="default-vector-profile"
        ),
        SimpleField(
            name="score",
            type=SearchFieldDataType.Double,
            filterable=True,
            sortable=True
        )
    ]
    
    return SearchIndex(
        name=SUREST_TRAINING_VIDEO_INDEX,
        fields=fields,
        vector_search=create_vector_search_config(),
        semantic_search=create_semantic_search_config(SUREST_TRAINING_VIDEO_INDEX)
    )

def create_bne_training_document_index():
    """Create index schema for BNE training documents"""
    fields = [
        SimpleField(
            name="id",
            type=SearchFieldDataType.String,
            key=True,
            filterable=True
        ),
        SearchableField(
            name="file_name",
            type=SearchFieldDataType.String,
            searchable=True,
            filterable=True,
            sortable=True
        ),
        SimpleField(
            name="file_url",
            type=SearchFieldDataType.String,
            filterable=True
        ),
        SearchableField(
            name="page_content",
            type=SearchFieldDataType.String,
            searchable=True,
            analyzer_name="en.microsoft"
        ),
        SimpleField(
            name="created_at",
            type=SearchFieldDataType.DateTimeOffset,
            filterable=True,
            sortable=True
        ),
        SearchField(
            name="embedding",
            type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
            searchable=True,
            vector_search_dimensions=1536,  # text-embedding-3-small dimension
            vector_search_profile_name="default-vector-profile"
        ),
        SimpleField(
            name="score",
            type=SearchFieldDataType.Double,
            filterable=True,
            sortable=True
        )
    ]
    
    return SearchIndex(
        name=BNE_TRAINING_DOCUMENT_INDEX,
        fields=fields,
        vector_search=create_vector_search_config(),
        semantic_search=create_semantic_search_config(BNE_TRAINING_DOCUMENT_INDEX)
    )

# Index registry for easy access
INDEX_SCHEMAS = {
    SUREST_TRAINING_MATERIAL_INDEX: create_surest_training_material_index,
    SUREST_TRAINING_VIDEO_INDEX: create_surest_training_video_index,
    BNE_TRAINING_DOCUMENT_INDEX: create_bne_training_document_index
}

def get_index_schema(index_name: str):
    """Get index schema by name"""
    if index_name in INDEX_SCHEMAS:
        return INDEX_SCHEMAS[index_name]()
    else:
        raise ValueError(f"Unknown index name: {index_name}")

def get_all_index_schemas():
    """Get all index schemas"""
    return {name: schema_func() for name, schema_func in INDEX_SCHEMAS.items()}
