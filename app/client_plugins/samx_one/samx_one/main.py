from client_plugins.samx_one.samx_one.tool import create_structure_from_schema, samx_one_validate_application_documents, validate_binder_check, samx_one_available_capabilities, samx_one_quote_query, samx_one_update_quote, samx_one_handle_navigation ,samx_one_quote_glance, samx_one_generate_proposal, samx_one_start_new_quote, samx_one_create_quote_using_uploaded_documents, samx_one_quote_through_questionnaire, samx_one_view_quote, samx_one_suggest_specialty_plans
from graph.state import State
from graph.nodes import Assistant
from config.openAI import model
from langgraph.graph import END, StateGraph, START
from langgraph.prebuilt import tools_condition

from langchain_core.runnables import RunnableLambda
from langgraph.prebuilt import ToolNode
from tools.common_tools import CompleteOrEscalate
from utils.helpers.constants import TOOL_CALL_ERROR_MESSAGE
from client_plugins.samx_one.samx_one.prompts import samx_one_system_prompt
import os
# Import the other required modules


samx_one_tools = []
if os.getenv('ENV').lower()=='dev':
    samx_one_tools = [create_structure_from_schema, validate_binder_check, samx_one_validate_application_documents, samx_one_available_capabilities, samx_one_quote_query, samx_one_update_quote, samx_one_handle_navigation, samx_one_quote_glance, samx_one_generate_proposal, samx_one_start_new_quote, samx_one_create_quote_using_uploaded_documents, samx_one_quote_through_questionnaire, samx_one_view_quote, samx_one_suggest_specialty_plans]
else:
    samx_one_tools = [create_structure_from_schema, validate_binder_check, samx_one_validate_application_documents]

samx_one_tools = samx_one_tools + [CompleteOrEscalate]
samx_one_tool_node = ToolNode(samx_one_tools, handle_tool_errors = TOOL_CALL_ERROR_MESSAGE)
samx_one_runnable = samx_one_system_prompt | model.bind_tools(samx_one_tools, parallel_tool_calls= False) 