from config.db import get_client_database_connection
from utils.helpers.constants import *
from utils.general_utils import getTime

async def store_feedback(
    client_id, user_name, uuid, user_email, session_id, index, submittedData, chatbotResponse, feedback,
    feedback_category=None, feedback_details=None, documents=None, request_id=None
):
    client_db = await get_client_database_connection(client_id)
    feedback_collection = client_db['UserChatFeedback']
    current_timestamp = getTime()
    # Determine whether a conversation entry with the given id exists
    if await feedback_collection.count_documents({"session_id": session_id}, limit=1):
        # Empty feedback
        # Remove the message from the conversation entry
        if feedback == "":
            await feedback_collection.update_one(
                {"session_id": session_id},
                {"$pull": {"data": {"index": index, "submittedData": submittedData, "chatbotResponse": chatbotResponse}}}
            )
            
            document = await feedback_collection.find_one({"session_id": session_id})
            if document and len(document.get("data", [])) == 0:
                await feedback_collection.delete_one({"session_id": session_id})
            return
        
        # Non-empty feedback
        # Check if an entry with the given index, submittedData, and chatbotResponse exists
        existing_entry = await feedback_collection.find_one({
            "session_id": session_id,
            "data": {
                "$elemMatch": {
                    "index": index,
                    "submittedData": submittedData,
                    "chatbotResponse": chatbotResponse
                }
            }
        })
        
        if existing_entry:
            update_data = {
                "data.$.feedback": feedback,
                "update_timestamp": current_timestamp,
                "data.$.feedback_category": feedback_category,
                "data.$.feedback_details": feedback_details,
                "data.$.documents": documents,
                "data.$.request_id": request_id
            }
            # Fixed query - use the $elemMatch operator to properly identify the array element
            await feedback_collection.update_one(
                {
                    "session_id": session_id,
                    "data": {
                        "$elemMatch": {
                            "index": index,
                            "submittedData": submittedData,
                            "chatbotResponse": chatbotResponse
                        }
                    }
                },
                {"$set": update_data}
            )
        else:
            update_query = {"session_id": session_id}
            feedback_data = {
                "index": index, 
                "submittedData": submittedData, 
                "chatbotResponse": chatbotResponse, 
                "feedback": feedback,
                "feedback_category": feedback_category,
                "feedback_details": feedback_details,
                "documents": documents,
                "request_id": request_id
            }
            update_data = {"$push": {"data": feedback_data}}
            await feedback_collection.update_one(update_query, update_data)
    else:
        feedback_list = {
            "user_name": user_name,
            "uuid": uuid,
            "user_email": user_email,
            "session_id": session_id,
            "create_timestamp": current_timestamp,
            "update_timestamp": current_timestamp,
            "data": [],
        }
        feedback_data = {
            "index": index, 
            "submittedData": submittedData, 
            "chatbotResponse": chatbotResponse, 
            "feedback": feedback,
            "feedback_category": feedback_category,
            "feedback_details": feedback_details,
            "documents": documents,
            "request_id": request_id
        }
        feedback_list["data"].append(feedback_data)
        await feedback_collection.insert_one(feedback_list)