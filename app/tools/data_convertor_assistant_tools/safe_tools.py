from langchain_core.tools import tool
from utils.schemas.classes import NYS45Data
import traceback
from langgraph.prebuilt.tool_node import InjectedState
from typing import Annotated
import json 
import base64
import io
from config.openAI import model, json_model
from utils.general_utils import get_gpt_message_object, trim_json_response
from utils.tool_utils import log_tool_error

@tool
async def extract_pdf_data(
    state: Annotated[dict, InjectedState],
    nys45: bool,
   ):
    """Extracts the data found within an uploaded pdf file in JSON format.
    Determine whether "NYS-45" is found in the document before calling this tool.
    """
    try:
        pdf = state.get("pdf_data")

        if not pdf:
            return "Please upload a pdf file in order to extract data."

        pdf_data = base64.b64decode(pdf)
        pdf_file = io.BytesIO(pdf_data)

        prompt = ("Create a JSON object with the extracted data from the PDF and do not exceed the depth of 2 JSON maximum.")
        resolution = 300 if nys45 else 200
        messages = await get_gpt_message_object(pdf_file, prompt, resolution)

        messages.insert(0, {"role": "system", "content": [{"type": "text", "text": """You are a helpful assistant who provides responses in JSON format. 
                                                           Ensure your responses follow the JSON structure precisely. Place data in a JSON with maximum depth of 2. 
                                                           Do not produce responses with a depth greater than 2. If you cannot fit certain data into a JSON object with a maximum depth of 2, leave that data out. 
                                                           Provide key names in plain English without underscores. Give the highest-level objects inside the JSON object names related to the sections of the document."""}]})

        if nys45:
            llm_str = model.with_structured_output(NYS45Data)
            json_response = await llm_str.ainvoke(messages)

        else:
            response = await json_model.ainvoke(messages)
            json_response = trim_json_response(json.loads(response.content))
        
        final_resp = {
            "client": "extractPdfData",
            "response": json.dumps(json_response)
        }

        final_resp = json.dumps(final_resp)
        return final_resp

    except Exception:
        await log_tool_error(state, traceback.format_exc(), "extract_pdf_data")
        raise Exception (traceback.format_exc())
