# Surest Client Plugin

## Overview
This plugin provides Surest-specific tools and assistant capabilities for the Lingo. It supports plan comparison, training material Q&A, and training video Q&A using embeddings, PostgreSQL, and LangChain/Graph-based orchestration.

## Architecture

### Components
- **Tools**: Defined in `surest/tool.py`, these are async functions for plan comparison, training material Q&A, and video Q&A.
- **Assistant**: Orchestrated via `surest/main.py` and `surest/surest_subgraph.py`, using LangGraph and custom routing.
- **Database**: PostgreSQL is used for storing and retrieving training materials and video metadata, with vector search for semantic similarity.
- **Embeddings**: OpenAI embeddings are used for semantic search.
- **Text Processing**: PDF files are split and embedded for Q&A.
- **Routing**: Custom router (`surest/router.py`) directs requests to the correct tool or fallback.

### Sequence Diagram (Textual)

```
User
 |
 | 1. Sends a question
 v
Surest Assistant (main.py)
 |
 | 2. Routes to appropriate tool (router.py)
 v
Surest Tool (tool.py)
 |
 | 3. Generates embedding for question
 | 4. Queries PostgreSQL for similar content (utils.py)
 | 5. If found, formats answer and follow-ups
 v
Surest Assistant
 |
 | 6. Returns answer to user
 v
User
```

### File Structure
- `surest/tool.py`: Tool definitions (plan comparison, Q&A)
- `surest/main.py`: Assistant and tool node setup
- `surest/surest_subgraph.py`: Graph orchestration and registration
- `surest/router.py`: Routing logic for tool selection
- `surest_utils/utils.py`: Utility functions for DB and embedding operations
- `surest_consts.py`: Constants and configuration

## Setup

1. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
2. Configure environment variables for database and OpenAI.
3. Run the test script for interactive Q&A:
   ```
   python surest/test.py
   ```

## Notes
- All answers are based strictly on the embedded training material.
- Fallback messages are returned if no relevant context is found.
- Embeddings and vector search are used for semantic matching.
