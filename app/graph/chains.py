from tools.cirrus_assistant_tools.safe_tools import fetch_group_contract_details, fetch_member_details, trigger_pre_installation, check_member_status
from client_plugins.plan_recommendation.plan_recommendation_assistant.tool import recommend_plans,recommend_shopping_plans
from tools.data_convertor_assistant_tools.safe_tools import extract_pdf_data
from tools.cirrus_assistant_tools.sensitive_tools import trigger_final_installation, review_data_for_final_installation
from tools.primary_assistant_tools.router_tools import ToSamxOneAssistant, ToPlanRecommendationAssistant, ToCirrusAssistant, ToDataConverterAssistant, ToSurestAssistant, ToDocumentSearchAssistant, ToPortalFormEnrollmentAssistant, ToCompensationAssistant, ToServiceTrackerAssistant
from client_plugins.bnemobile.bnemobile_router_tools import ToCommCentreAssistant, ToSbcRetrievalAssistant
from client_plugins.gco_reporting.gco_reporting.gco_reporting_router_tools import ToGCOReportingAssistant
from tools.primary_assistant_tools.safe_tools import activeCapabilities
from config.openAI import model
from langchain_core.prompts import Cha<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from graph.prompts_and_capabilities import primary_assistant_system_prompt_text, special_routing_instructions, primary_assistant_prompt, plan_recommendation_assistant_prompt, cirrus_assistant_prompt, data_converter_assistant_prompt
from tools.common_tools import CompleteOrEscalate
from services.get_prompts_service import get_additional_master_prompt
from utils.helpers.constants import OEC_DEFAULT_CLIENT_ID, OEC_BNEPORTAL_CLIENT_ID, OEC_BNEMOBILE_CLIENT_ID, OEC_SAMXONE_CLIENT_ID

def get_primary_assistant_prompt(client_id, primary_assistant_prompt):
    if client_id == OEC_BNEPORTAL_CLIENT_ID:
        additional_master_prompt = get_additional_master_prompt(client_id)
        if not additional_master_prompt:
            raise ValueError("No additional master prompt found for the BNE client ID.")
        additional_master_prompt_text = additional_master_prompt.get("additionalMasterPrompt", "")
        primary_assistant_system_prompt = ("system", f"{primary_assistant_system_prompt_text}\n\n{special_routing_instructions}\n\n{additional_master_prompt_text}")
        primary_assistant_prompt = ChatPromptTemplate.from_messages([primary_assistant_system_prompt,("placeholder", "{messages}")]).partial()
        return primary_assistant_prompt
    else:
        return primary_assistant_prompt

def fetch_primary_assistant_runnable(client_id):
    # The below checks are only for clients that currently have multi-agent capabilities (i.e. the primary_assistant_tools list should never be a single tool).
    if client_id == OEC_DEFAULT_CLIENT_ID:
        primary_assistant_tools = [ToCirrusAssistant, ToSurestAssistant]
    elif client_id == OEC_BNEPORTAL_CLIENT_ID:
        primary_assistant_tools = [ToDocumentSearchAssistant, ToPortalFormEnrollmentAssistant, ToGCOReportingAssistant, ToCompensationAssistant, ToServiceTrackerAssistant]
    elif client_id == OEC_BNEMOBILE_CLIENT_ID:
        primary_assistant_tools = [ToCommCentreAssistant, ToSbcRetrievalAssistant]
    elif client_id == OEC_SAMXONE_CLIENT_ID:
        primary_assistant_tools = [ToSamxOneAssistant, ToSurestAssistant]

    primary_assistant_runnable = get_primary_assistant_prompt(client_id, primary_assistant_prompt) | model.bind_tools(primary_assistant_tools)

    return primary_assistant_runnable, primary_assistant_tools

#NOTE: THE BELOW CODE IS NOT BEING USED. IT IS PART OF THE LEGACY GRAPH

primary_assistant_tools = [ToSamxOneAssistant, ToCirrusAssistant, ToPlanRecommendationAssistant, ToDataConverterAssistant, ToSurestAssistant] 
primary_assistant_runnable = primary_assistant_prompt | model.bind_tools(primary_assistant_tools)

plan_recommendation_tools = [recommend_plans, recommend_shopping_plans]
plan_recommendation_runnable = plan_recommendation_assistant_prompt | model.bind_tools(plan_recommendation_tools + [CompleteOrEscalate])

cirrus_tools = [fetch_group_contract_details, fetch_member_details] #+ [trigger_pre_installation, review_data_for_final_installation, trigger_final_installation, check_member_status]
cirrus_assistant_runnable = cirrus_assistant_prompt | model.bind_tools(cirrus_tools + [CompleteOrEscalate])

data_converter_tools = [extract_pdf_data]
data_converter_assistant_runnable = data_converter_assistant_prompt | model.bind_tools(data_converter_tools + [CompleteOrEscalate])
