from pydantic import BaseModel, ConfigDict


class PendReply(BaseModel):
    pend_reason_id: str
    reply_message: str
    replied_by: str

class Notes(BaseModel):
    username: str
    noteText: str

class CreatePendNote(BaseModel): 
  quoteId: str
  notes: Notes
  
class NewBusinessGroupsFilter(BaseModel):
    caseId: str = ""
    companyName: str = ""
    effectiveDate: str = ""
    groupStatus: str = ""
    planType: str = ""
    states: str = ""