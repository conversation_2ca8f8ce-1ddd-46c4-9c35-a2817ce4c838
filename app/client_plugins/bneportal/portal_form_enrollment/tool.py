from langchain_core.tools import tool
import traceback
from typing import Annotated
from langgraph.prebuilt.tool_node import InjectedState

bne_enrollment_navigation_fallback_message = "Please navigate to the Enroll Member page to proceed with an enrollment."

@tool
async def initiate_enrollment(state: Annotated[dict, InjectedState]): # add arguments if required
    """
    This tool provides very specific data related to exact user query strings 'Initiate Enrollment Process'.
    Strictly: This tool should be used every time and only at times a user asks a question that matches this string exactly: 'Initiate Enrollment Process'.

    """               

    try:
        functionality = state.get("additional_arg", {}).get("bne_user_functionality")
        if functionality == "enrollment":
            return {
                "client": "bne_client",
                "response_type": "initiate_enroll",
                "custom_chatbot_json_message": "Would you like to upload an Enrollment Form to automatically fill out this enrollment?"
            }
        return {
            "answer": bne_enrollment_navigation_fallback_message,
            "follow_up_questions": []
        }
    except Exception as e:
        raise Exception (traceback.format_exc())

@tool
async def process_enrollment(state: Annotated[dict, InjectedState]): # add arguments if required
    """
       Handles all queries related to auto-fill enrollment. All queries related to auto-fill enrollment should be routed to this tool.
       This tool should be called even in scenarios where the user responds with Yes or any kind of agreement response that shows their intent to go ahead with Enrollment 
       This tool is only called for the second user query 
       As a response to the first user query, No action needed from this tool that work is handled by other tool.
       Then the second query from the user can either be yes or no (can be any word that shows agreement or disagreement).
       If the user responds with Yes please call this tool otherwise a respond with a message 'Sure, Please Continue your Enrollment Manually'.

    """

    try:
        functionality = state.get("additional_arg", {}).get("bne_user_functionality")
        if functionality == "enrollment":
            return {
                "client": "bne_client",
                "response_type": "form_upload",
                "custom_chatbot_json_message": "Great! Upload the Enrollment Form below to begin."
            }
        return {
            "answer": bne_enrollment_navigation_fallback_message,
            "follow_up_questions": []
        }
    except Exception as e:
        raise Exception (traceback.format_exc())
    