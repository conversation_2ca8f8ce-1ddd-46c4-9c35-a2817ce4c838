import time
import httpx
import os

def get_openai_token():
    openai_token = os.getenv("OPENAI_TOKEN")
    if (openai_token == None):
        return generate_new_openai_token()
    openai_token_issue_time = os.getenv("OPENAI_TOKEN_ISSUE_TIME")
    if (openai_token_issue_time == None) or (int(time.time()) >= int(openai_token_issue_time) + 3590):
        return generate_new_openai_token()
    return openai_token

def generate_new_openai_token():
    auth = os.environ["OPENAI_AUTH_URL"]
    client_id = os.environ["OPENAI_CLIENT_ID"]
    client_secret = os.environ["OPENAI_CLIENT_SECRET"]
    scope = os.environ["OPENAI_SCOPE"]
    grant_type = "client_credentials"
    issue_time = int(time.time())

    with httpx.Client() as client:  
        body = {
            "grant_type": grant_type,
            "scope": scope,
            "client_id": client_id,
            "client_secret": client_secret,
        }
        resp = client.post(auth, data=body, timeout=60)
    token = resp.json()["access_token"]
    os.environ["OPENAI_TOKEN"] = token
    os.environ["OPENAI_TOKEN_ISSUE_TIME"] = str(issue_time)
    return token