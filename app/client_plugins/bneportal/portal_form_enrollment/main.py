
from client_plugins.bneportal.portal_form_enrollment.tool import process_enrollment, initiate_enrollment

from graph.state import State
from graph.nodes import Assistant
from config.openAI import model
from langgraph.graph import END, StateGraph, START
from langgraph.prebuilt import tools_condition

# from utils.tool_utils import handle_tool_error
from langchain_core.runnables import Runna<PERSON><PERSON><PERSON><PERSON><PERSON>
from langgraph.prebuilt import ToolNode
from tools.common_tools import CompleteOrEscalate
from client_plugins.bneportal.portal_form_enrollment.prompts import portal_form_enrollment_system_prompt
from utils.helpers.constants import TOOL_CALL_ERROR_MESSAGE
# Import the other required modules
# def create_tool_node_with_fallback(tools: ToolNode) -> dict:
#     return tools.with_fallbacks(
#         [RunnableLambda(handle_tool_error)], exception_key="error"
#     )

portal_form_enrollment_tools = [process_enrollment,initiate_enrollment,CompleteOrEscalate]  
portal_form_enrollment_tool_node = ToolNode(portal_form_enrollment_tools, handle_tool_errors = TOOL_CALL_ERROR_MESSAGE)
portal_form_enrollment_runnable = portal_form_enrollment_system_prompt | model.bind_tools(portal_form_enrollment_tools)
            