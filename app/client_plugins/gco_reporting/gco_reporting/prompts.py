 
from langchain_core.prompts import ChatPromptTemplate
from services.get_prompts_service import master_prompt_document, get_prompt_by_assistant_name
# Add your system prompt here
# dfprom[pfjsoidjfo]

gco_reporting_assistant_prompt_document = get_prompt_by_assistant_name("gco_reporting")

if not gco_reporting_assistant_prompt_document:
    raise ValueError("gco_reporting Assistant Prompt Not Found")

master_prompt = master_prompt_document.get("masterPrompt")
assistant_prompt = gco_reporting_assistant_prompt_document.get("assistantPrompt")

system_prompt = ("system", master_prompt + assistant_prompt)
gco_reporting_system_prompt = ChatPromptTemplate.from_messages([system_prompt, ("placeholder", "{messages}")]).partial() 
