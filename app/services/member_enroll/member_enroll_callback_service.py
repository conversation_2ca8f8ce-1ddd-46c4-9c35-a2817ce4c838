import traceback
from config.member_enroll_logging_config import member_enroll_logger
from MagnusLimraConverter import convert_docsight_to_limra
from services import log_error_to_db_service
from services.member_enroll.api_service import bne_callback_api, process_bne_response
from services.member_enroll.mongo_service import (
    find_enrollment_document,
    upsert_enrollment_document,
)
from utils.helpers.constants import OEC_DEFAULT_CLIENT_ID

async def process_callback(request_data, errored_request):
    """Process the callback request data from SAL by updating MongoDB and calling the BNE callback API.
    :param request_data: A dictionary containing the callback request data.
    :param errored_request: Boolean containing whether the request contained an error.
    :return: None
    """    
    status = request_data.get('status')
    messages = request_data.get('messages')
    job_id = request_data.get('jobId')

    member_enroll_logger.info("Processing callback request", extra={'job_id': job_id})
    try:
        if errored_request:
            member_enroll_logger.info("Email Automation request is malformed", extra={'job_id': job_id})
            mongo_record = await find_enrollment_document(job_id)

            bne_callback_url = mongo_record["bneCallback"]
            bne_callback_params = {
                "status": "Error",
                "jobId": mongo_record["jobId"],
                "messages": mongo_record["messages"],
                "query": mongo_record["requestType"],
                "clientID": mongo_record["clientId"],
                "version": mongo_record["apiVersion"],
                "uuid": mongo_record["uuid"],
            }

            callback_response = await bne_callback_api(bne_callback_params, bne_callback_url)

            await process_bne_response(callback_response, job_id, "Error", True)
            member_enroll_logger.info("Finished processing malformed request from Email Automation", extra={'job_id': job_id})
            return

        # If Email Automation sends Success in the request payload
        if status == "Success":
            member_enroll_logger.info("Email Automation status is Success", extra={'job_id': job_id})
            # Convert into Limra Payload
            limra_payload = convert_docsight_to_limra(request_data.get("payload"))

            mongo_document = {
                "limraPayload": limra_payload,
            }

            # Update Mongo document with the Limra payload
            updated_doc = await upsert_enrollment_document(job_id, mongo_document)
            member_enroll_logger.info("Successfully updated MongoDB with Limra payload", extra={'job_id': job_id})

            bne_callback_url = updated_doc["bneCallback"]
            bne_callback_params = {
                "status": "Success",
                "jobId": job_id,
                "query": updated_doc["requestType"],
                "payload": updated_doc["limraPayload"],
                "query": updated_doc["requestType"],
                "clientID": updated_doc["clientId"],
                "version": updated_doc["apiVersion"],
                "uuid": updated_doc["uuid"],
            }

            callback_response = await bne_callback_api(bne_callback_params, bne_callback_url)

            await process_bne_response(callback_response, job_id, "Success", True)
            member_enroll_logger.info("Finished processing success status from Email Automation", extra={'job_id': job_id})

        # If Email Automation sends Error in the request payload
        elif "error" in status.lower():
            member_enroll_logger.info("Email Automation status is Error", extra={'job_id': job_id})
            mongo_document = {
                "messages": messages,
                "processStatus": "Error",
            }
            updated_doc = await upsert_enrollment_document(job_id, mongo_document)
            member_enroll_logger.info("Successfully updated MongoDB", extra={'job_id': job_id})      

            bne_callback_url = updated_doc["bneCallback"]
            bne_callback_params = {
                "status": "Error",
                "jobId": updated_doc["jobId"],
                "messages": updated_doc["messages"],
                "query": updated_doc["requestType"],
                "clientID": updated_doc["clientId"],
                "version": updated_doc["apiVersion"],            
                "uuid": updated_doc["uuid"],
            }

            callback_response = await bne_callback_api(bne_callback_params, bne_callback_url)

            await process_bne_response(callback_response, job_id, "Error", True)
            member_enroll_logger.info("Finished processing error status from Email Automation", extra={'job_id': job_id})
    except Exception as e:
        member_enroll_logger.error("Error processing callback request, error: %s", str(e), extra={'job_id': job_id})
        traceback.print_exc()
        await log_error_to_db_service.log_error_to_db(traceback.format_exc(), OEC_DEFAULT_CLIENT_ID, None, None, "Code Break")

    return

async def handle_callback(request_data, background_tasks):
    """Handle the callback request by updating MongoDB with request data and processing it in the background.
    :param request_data: A dictionary containing the callback request data.
    :param background_tasks: Background tasks to be executed after the response is sent.
    :return: A dictionary with the status and message of the operation.
    """    
    job_id = request_data.get("jobId")
    status = request_data.get("status")
    messages = request_data.get("messages", [])
    payload = request_data.get("payload")
    returned_version = request_data.get("version")

    errored_request = False
    status_code = 200

    mongo_document = {
        "lingoCallbackRequestParams": request_data
    }

    return_obj = {
        "jobId": job_id
    }

    # Check if Mongo document exists
    document = await find_enrollment_document(job_id)

    if not document:
        member_enroll_logger.error("Job ID doesn't exist.", extra={'job_id': job_id})
        return_obj["status"] = "Failure"
        return_obj["message"] = f"Job ID {job_id} does not exist."
        return 400, return_obj
    
    requested_api_version = document.get("apiVersion", "1.0")

    # Check to make sure payload is present if status is success
    if status == "Success" and (not payload or payload == "N/A"):
        member_enroll_logger.error("Payload missing for successful status.", extra={'job_id': job_id})
        errored_request = True
        status_code = 400
        error_message = "Error occurred while processing the request. You must provide a payload if the status is success."
        mongo_document["processStatus"] = "Error"
        mongo_document["messages"] = [{
            "event": "Error",
            "source": "Lingo",
            "message": error_message
        }]

        return_obj["status"] = "Failure"
        return_obj["message"] = error_message

    # Check to make sure messages is present if status is error
    elif "error" in status.lower() and not messages:
        member_enroll_logger.error("Messages missing for error status.", extra={'job_id': job_id})
        errored_request = True
        status_code = 400
        error_message = "Error occurred while processing the request. You must provide a messages list if the status is error."
        mongo_document["processStatus"] = "Error"
        mongo_document["messages"] = [{
            "event": "Error",
            "source": "Lingo",
            "message": error_message
        }]
        return_obj["status"] = "Failure"
        return_obj["message"] = error_message
    # Check to make sure requested payload equals received payload
    elif status == "Success" and requested_api_version != returned_version:
        error_message = f"API version mismatch. Requested version: {requested_api_version}, received version: {returned_version}"
        member_enroll_logger.error(error_message, extra={'job_id': job_id})
        errored_request = True
        status_code = 400
        mongo_document["processStatus"] = "Error"
        mongo_document["messages"] = [{
            "event": "Error",
            "source": "Lingo",
            "message": error_message
        }]
        return_obj["status"] = "Failure"
        return_obj["message"] = error_message
    else:
        member_enroll_logger.info("Callback payload is in the correct format.", extra={'job_id': job_id})
        return_obj["status"] = "Success"
        return_obj["message"] = "Received data successfully."

    try:
        await upsert_enrollment_document(job_id, mongo_document)
        member_enroll_logger.info("Successfully updated MongoDB with EA request parameters", extra={'job_id': job_id})
    except Exception as e:
        member_enroll_logger.error("Error updating MongoDB, error: %s", str(e), extra={'job_id': job_id})
        status_code = 500
        return_obj["status"] = "Failure"
        return_obj["message"] = f"Unexpected error updating Mongo: {str(e)}"

    # Call background tasks
    background_tasks.add_task(process_callback, request_data, errored_request)

    member_enroll_logger.info("Successfully handled callback", extra={'job_id': job_id})
    return status_code, return_obj
