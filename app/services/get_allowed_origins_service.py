from fastapi.responses import JSONResponse
from config.db import db_onboarding
from fastapi import HTTPException

async def allowedOrigins():
    collection = db_onboarding["Configs"] 
    allowed_origins = await collection.find_one({"type": "allowedOrigins"})

    if not allowed_origins:  
        raise HTTPException(status_code=404, detail="Allowed origins configuration not found")  

    allowed_origins = allowed_origins.get("allowedOrigins", [])     
    return JSONResponse(content={"status": "success", "allowedOrigins": allowed_origins}, status_code=200) 
