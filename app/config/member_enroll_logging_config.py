import logging
import time

class JobIdFormatter(logging.Formatter):
    def format(self, record):
        if not hasattr(record, 'job_id'):
            record.job_id = 'N/A'
        return super().format(record)

# Create member enroll logger
member_enroll_logger = logging.getLogger('member_enroll_logger')
member_enroll_logger.setLevel(logging.INFO)

# Create a handler and set the formatter
formatter = JobIdFormatter('%(asctime)s - %(levelname)s - jobId: %(job_id)s - %(message)s', '%Y-%m-%d %H:%M:%S')
stream_handler = logging.StreamHandler()
stream_handler.setFormatter(formatter)

# Add the handler to the custom logger
member_enroll_logger.addHandler(stream_handler)

# Set UTC time for the timestamp
logging.Formatter.converter = time.gmtime