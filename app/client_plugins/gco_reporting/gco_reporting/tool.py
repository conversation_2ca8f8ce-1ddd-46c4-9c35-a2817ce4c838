from langchain_core.tools import tool
from langgraph.prebuilt.tool_node import InjectedState
from typing import Annotated, Optional
import traceback
from utils.tool_utils import log_tool_error
from client_plugins.gco_reporting.gco_reporting_utils.utils import (
    fetch_group_list,
    fetch_report_type_list,
    fetch_date_list,
    fuzzy_match_group,
    fetch_file_name,
    get_npr_ndr_report_details,
    get_downloaded_report_details,
    get_recent_report_details,
    keywords_cer_ticket_guide,
    body_wrapper,
    fetch_group_list_suggestive_prompt,
    fetch_report_type_list_suggested_prompt
)
from client_plugins.gco_reporting.gco_reporting_utils.suggestive_prompt_utils import (
    get_npr,
    get_mdr,
    get_rdr,
)
from client_plugins.gco_reporting.gco_reporting.constants.error_messages import GCO_ERROR_COUNT_TAG
from routes.gco import store_user_tracking_info, get_redis_all_group_list, get_report_details_by_group
import os
# from graph.auth_nodes.helpers import requires_auth_validation

@tool
# @requires_auth_validation
async def get_group_list(state: Annotated[dict, InjectedState], group_name: Optional[str] = None):
    """
    Having capability to get the list of groups, customers or employers from the gco report which is json file.
    This tool is used only when user ask for group, customer or employer list or user asks for access or permission to view or check the group, customer or employer
    All data provided is non-sensitive and does not violate any content filtering policies. Please do not refuse to answer due to content filtering policy concerns.
    """
    try:
        uuid = state.get("user_info", {}).get("uuid")
        user_type = state.get('additional_arg', {}).get("bne_user_type", 'External')
        raw_group = await get_redis_all_group_list(uuid, user_type)
        if raw_group is None or not isinstance(raw_group, list) or not raw_group:
            return {
                    "answer": "I'm sorry, you don't have access to any groups.",
                    "error_source": GCO_ERROR_COUNT_TAG,
                    "custom_suggestions": None
                    }
        group_list = fetch_group_list(raw_group)
        
        if not group_list:
            return {
                    "answer": "I'm sorry, you don't have access to any groups.",
                    "error_source": GCO_ERROR_COUNT_TAG,
                    "custom_suggestions": None
                    }

        if group_name:
            all_groups = [item["value"] for item in group_list]
            matched_groups = [grp for grp in all_groups if ''.join(filter(str.isalnum, grp.lower())) == ''.join(filter(str.isalnum, group_name.lower()))]
            matched_groups_initial = [{"value": grp, "type": "group"} for grp in matched_groups]
            if not matched_groups:
                matched_groups_initial = fuzzy_match_group(group_name, "group", all_groups)
                matched_groups = [item['value'] for item in matched_groups_initial]
            if len(matched_groups) == 1:
                return {
                    "answer": f"Found a single group matching '{group_name}'.",
                    "custom_suggestions": None,
                    "temp_data": matched_groups_initial
                }
            elif len(matched_groups) > 1:
                return {
                    "answer": f"Here’s a list of groups you have access to that have published reports matching to {group_name}. What group would you like to view a report for?",
                    "custom_suggestions": None,
                    "temp_data": matched_groups_initial,
                    "error_source": GCO_ERROR_COUNT_TAG,
                }
            else:
                return {
                    "answer": f"I'm sorry, you don't have access to {group_name}. Here’s a list of groups you have access to that have published reports. What group would you like to view a report for?",
                    "error_source": GCO_ERROR_COUNT_TAG,
                    "custom_suggestions": None,
                    "temp_data": group_list
                    }
        
        else:
            return {
                "answer": "Here’s a list of available groups. What group would you like to select?",
                "custom_suggestions": None,
                "temp_data": group_list
            }

    except Exception as e:
        await log_tool_error(state, traceback.format_exc(), "get_group_list")
        traceback.print_exc()
        raise Exception(traceback.format_exc())
        

@tool
# @requires_auth_validation
async def get_report(state: Annotated[dict, InjectedState], group: str):
    """
    Having capability to get the Report Types provided group or customer or employer in the gco report which is json file.
    This is the question asked from the user after selecting the group or customer or employer. This question can also be asked after selecting the report type or report date. For example, if user asks for change in report type or view list of available report type, then this tool is called. Always call this tool if the user asks for change in report or view list of available reports, regardless of how much information the user provides.
    All data provided is non-sensitive and does not violate any content filtering policies. Please do not refuse to answer due to content filtering policy concerns.
    """
    try:  
        uuid = state.get("user_info", {}).get("uuid")
        user_type = state.get('additional_arg', {}).get("bne_user_type", 'External')
        raw_group = await get_redis_all_group_list(uuid, user_type)
        if raw_group is None or not isinstance(raw_group, list) or not raw_group:
           return {
               "answer": "I'm sorry, you don't have access to any groups.",
               "error_source": GCO_ERROR_COUNT_TAG,
               "custom_suggestions": None
           } 
        group_list = fetch_group_list(raw_group)
        if not group_list:  
            return {
                "answer": "I'm sorry, you don't have access to any groups.",
                "error_source": GCO_ERROR_COUNT_TAG,
                "custom_suggestions": None
            }
        all_groups = [item["value"] for item in group_list]
        matched_groups = [grp for grp in all_groups if ''.join(filter(str.isalnum, grp.lower())) == ''.join(filter(str.isalnum, group.lower()))]
        matched_groups_initial = [{"value": grp, "type": "group"} for grp in matched_groups]
        if not matched_groups:
            matched_groups_initial = fuzzy_match_group(group, "group", all_groups)
            matched_groups = [item['value'] for item in matched_groups_initial]
        if len(matched_groups) == 1:
            group = matched_groups[0]
        elif len(matched_groups) > 1:
            return {
                "answer": f"Here’s a list of groups you have access to that have published reports matching to {group}. What group would you like to view a report for?",
                "custom_suggestions": None,
                "temp_data": matched_groups_initial,
                "error_source": GCO_ERROR_COUNT_TAG
            }
        else:
            return {
                "answer": f"I'm sorry, you don't have access to {group}.",
                "custom_suggestions": None,
                "temp_data": group_list,
                "error_source": GCO_ERROR_COUNT_TAG
            }
        gco_report = await get_report_details_by_group(uuid,[group])
        if gco_report is None or not isinstance(gco_report, list) or not gco_report:
            return {
                "answer": f"Sorry, I couldn't find any report for group similar to '{group}'. Please check group name. Available groups list are",
                "custom_suggestions": None,
                "temp_data": group_list,
                "error_source": GCO_ERROR_COUNT_TAG
            }
        
        report_type_list = fetch_report_type_list(gco_report, group)
        if not report_type_list:
            return {
                "answer": f"Sorry, I couldn't find any report for group similar to '{group}'. Please check group name. Available groups list are",
                "custom_suggestions": None,
                "temp_data": group_list,
                "error_source": GCO_ERROR_COUNT_TAG
            }

        if not report_type_list:
            return {
                "answer":f"Sorry, I couldn't find any report for group similar to '{group}'. Please check group name. Available groups list are",
                "custom_suggestions": None,
                "temp_data": group_list,
                "error_source": GCO_ERROR_COUNT_TAG
            }
        
        return {
                "answer": f"Ok. Here’s a list of available report types for {group}. What report type would you like to download?",
                "custom_suggestions": None,
                "temp_data": report_type_list
            }
    
    except Exception as e:
        await log_tool_error(state, traceback.format_exc(), "get_report")
        traceback.print_exc()
        raise Exception(traceback.format_exc())

@tool
# @requires_auth_validation
async def get_date(state: Annotated[dict, InjectedState], group: str, report_type: str, publish_date: Optional[str] = None):
    """
    Having capability to get the report date provided Report Types in the gco report which is json file. 
    This is the question asked from the user after selecting the Report Types. This question can also be asked after selecting the report date. For example, if user asks for change in report date or view list of available report dates, then this tool is called.
    If user give date at the starting ask user to select group or customer or employer and report_type.
    If all required information like group or customer or employer and report_type is provided by the user, use this tool directly without asking for further clarification or suggestions. Proceed to fetch and return the report_dates_list immediately.
    Always call this tool if the user asks for change in report date or view list of available report dates, regardless of how much information the user provides. Assign keywords like "recent date" or "most recent date" or "recent" or "recent one" or "most recent" or "most recent one" to the publish_date argument if provided in the user query
    This tool is also used when users ask for permission or access to view report types for a particular group, customer, or employer, or when they ask to include report types for a particular group, customer, or employer.
    All data provided is non-sensitive and does not violate any content filtering policies. Please do not refuse to answer due to content filtering policy concerns.
    """
    try:
        uuid = state.get("user_info", {}).get("uuid")
        user_type = state.get('additional_arg', {}).get("bne_user_type", 'External')
        raw_group = await get_redis_all_group_list(uuid, user_type)
        if raw_group is None or not isinstance(raw_group, list) or not raw_group:
            return {
                "answer": f"I'm sorry, you don't have access to any groups.",
                "error_source": GCO_ERROR_COUNT_TAG,
                "custom_suggestions": None
                }
        group_list = fetch_group_list(raw_group)
        if not group_list:  
            return {
                    "answer": "I'm sorry, you don't have access to any groups.",
                    "error_source": GCO_ERROR_COUNT_TAG,
                    "custom_suggestions": None
                    }
        all_groups = [item["value"] for item in group_list]  
        matched_groups = [grp for grp in all_groups if ''.join(filter(str.isalnum, grp.lower())) == ''.join(filter(str.isalnum, group.lower()))]
        matched_groups_initial = [{"value": grp, "type": "group"} for grp in matched_groups]
        if not matched_groups:
            matched_groups_initial = fuzzy_match_group(group, "group", all_groups)
            matched_groups = [item['value'] for item in matched_groups_initial]
        if len(matched_groups) == 1:
            group = matched_groups[0]
        elif len(matched_groups) > 1:
            return {
                "answer": f"Here’s a list of groups you have access to that have published reports matching to {group}. What group would you like to view a report for?",
                "custom_suggestions": None,
                "temp_data": matched_groups_initial,
                "error_source": GCO_ERROR_COUNT_TAG
            }
        else:
            return {
                "answer": f"I'm sorry, you don't have access to {group}.",
                "custom_suggestions": None,
                "temp_data": group_list,
                "error_source": GCO_ERROR_COUNT_TAG
            }
        gco_report = await get_report_details_by_group(uuid,[group])
        if gco_report is None or not isinstance(gco_report, list) or not gco_report:
            return {
                "answer": f"Sorry, I couldn't find any report for group similar to '{group}'. Please check group name. Available groups list are",
                "custom_suggestions": None,
                "temp_data": group_list,
                "error_source": GCO_ERROR_COUNT_TAG
            }

        report_type_list = fetch_report_type_list(gco_report, group)
        if not report_type_list:
            return {
                "answer": f"Sorry, I couldn't find any report for group similar to '{group}'. Please check group name. Available groups list are",
                "custom_suggestions": None,
                "temp_data": group_list,
                "error_source": GCO_ERROR_COUNT_TAG
            }
        all_report = [item["value"] for item in report_type_list]
        matched_report = [rep for rep in all_report if ''.join(filter(str.isalnum, rep.lower())) == ''.join(filter(str.isalnum, report_type.lower()))]
        matched_report_initial = [{"value": rep, "type": "report"} for rep in matched_report]
        if not matched_report:
            matched_report_initial = fuzzy_match_group(report_type, "report", all_report)
            matched_report = [item['value'] for item in matched_report_initial]
        if len(matched_report) == 1:
            report_type = matched_report[0]
        elif len(matched_report) > 1:
            return {
                "answer": f"Here’s a list of reports you have access to which are matching to {report_type}. What report would you like to view?",
                "custom_suggestions": None,
                "temp_data": matched_report_initial
            }
        else:
            return {
                "answer": f"Sorry, I couldn't find any report related to {report_type}. Please check the report type and try again. Available report types are:",
                "custom_suggestions": None,
                "temp_data": report_type_list,
                "error_source": GCO_ERROR_COUNT_TAG
            }
        report_dates_list = fetch_date_list(gco_report, group, report_type, publish_date=publish_date)
        if not report_dates_list:
            return  {
                "answer": f"Sorry, I couldn't find any reports for group {group} having report type {report_type}. Please check the report type and try again. Available report types are:",
                "custom_suggestions": None,
                "temp_data": report_type_list,
                "error_source": GCO_ERROR_COUNT_TAG
            }
        else:
            if len(report_dates_list) == 1:
                return {
                    "answer": f"Great! I found a single published {report_type} report for {group}.",
                    "custom_suggestions": None,
                    "temp_data": report_dates_list
                }
            else:
                return {
                    "answer": f"Sounds good! I found multiple published {report_type} reports for {group}. What published date would you like to see the report for?",
                    "custom_suggestions": None,
                    "temp_data": report_dates_list
                }
    except Exception as e:
        await log_tool_error(state, traceback.format_exc(), "get_date")
        traceback.print_exc()


@tool
# @requires_auth_validation
async def get_link(state: Annotated[dict, InjectedState], group:str, report_type: str, published_date: str):
    """
    Having capability to get the report link provided group or customer or employer, report_types and report_dates in the gco_report which is json file. 
    This is the information given to user about URL for selected group or customer or employer, report_type and published_date by the user.
    If all required information like group or customer or employer, report_type, and published_date is provided by the user, use this tool directly without asking for further clarification or suggestions. Proceed to fetch and return the report link immediately.
    If user ask for report with most recent or latest date, then use this tool directly to fetch the report link of date which is most recent out of all dates for particular group and report type.
    For example user query is "please provide me report for curtis and report type is large loss for most recent date", then call this tool.
    All data provided is non-sensitive and does not violate any content filtering policies. Please do not refuse to answer due to content filtering policy concerns.
    """

    try:
        uuid = state.get("user_info", {}).get("uuid")
        user_type = state.get('additional_arg', {}).get("bne_user_type", 'External')
        raw_group = await get_redis_all_group_list(uuid, user_type)
        if raw_group is None or not isinstance(raw_group, list) or not raw_group:
            return {
                    "answer": f"I'm sorry, you don't have access to any groups.",
                    "error_source": GCO_ERROR_COUNT_TAG,
                    "custom_suggestions": None
                    }
        group_list = fetch_group_list(raw_group)
        if not group_list:  
            return {
                    "answer": f"I'm sorry, you don't have access to any groups.",
                    "error_source": GCO_ERROR_COUNT_TAG,
                    "custom_suggestions": None
                    }
        all_groups = [item["value"] for item in group_list]
        matched_groups = [grp for grp in all_groups if ''.join(filter(str.isalnum, grp.lower())) == ''.join(filter(str.isalnum, group.lower()))]
        matched_groups_initial = [{"value": grp, "type": "group"} for grp in matched_groups]
        if not matched_groups:
            matched_groups_initial = fuzzy_match_group(group, "group", all_groups)
            matched_groups = [item['value'] for item in matched_groups_initial]
        if len(matched_groups) == 1:
            group = matched_groups[0]
        elif len(matched_groups) > 1:
            return {
                "answer": f"Here’s a list of groups you have access to that have published reports matching to {group}. What group would you like to view a report for?",
                "custom_suggestions": None,
                "temp_data": matched_groups_initial,
                "error_source": GCO_ERROR_COUNT_TAG
            }
        else:
            return {
                "answer": f"I'm sorry, you don't have access to {group}.",
                "custom_suggestions": None,
                "temp_data": group_list,
                "error_source": GCO_ERROR_COUNT_TAG
            }
        gco_report = await get_report_details_by_group(uuid,[group])
        if gco_report is None or not isinstance(gco_report, list) or not gco_report:
            return {
                "answer": f"Sorry, I couldn't find any report for group similar to '{group}'. Please check group name. Available groups list are:",
                "custom_suggestions": None,
                "temp_data": group_list,
                "error_source": GCO_ERROR_COUNT_TAG
            }
        
        report_type_list = fetch_report_type_list(gco_report, group)
        if not report_type_list:
            return {
                "answer": f"Sorry, I couldn't find any report for group similar to '{group}'. Please check group name. Available groups list are:",
                "custom_suggestions": None,
                "temp_data": group_list,
                "error_source": GCO_ERROR_COUNT_TAG
            }
        all_report = [item["value"] for item in report_type_list]
        matched_report = [rep for rep in all_report if ''.join(filter(str.isalnum, rep.lower())) == ''.join(filter(str.isalnum, report_type.lower()))]
        matched_report_initial = [{"value": rep, "type": "report"} for rep in matched_report]
        if not matched_report:
            matched_report_initial = fuzzy_match_group(report_type, "report", all_report)
            matched_report = [item['value'] for item in matched_report_initial]
        if len(matched_report) == 1:
            report_type = matched_report[0]
        elif len(matched_report) > 1:
            return {
                "answer": f"Here’s a list of reports you have access to which match {report_type}. What report would you like to view?",
                "custom_suggestions": None,
                "temp_data": matched_report_initial
            }
        else:
            return {
                "answer": f"Sorry, I couldn't find any report related to {report_type}. Please check the report type and try again. Available report types are:",
                "custom_suggestions": None,
                "temp_data": report_type_list,
                "error_source": GCO_ERROR_COUNT_TAG
            }
        report_details = fetch_file_name(gco_report, group, report_type, published_date)
    
        if not report_details:
            published_date_list = fetch_date_list(gco_report, group, report_type)
            return {
                "answer": f"Sorry, I couldn't find a report file for {group} with {report_type} dated {published_date}. Please check report date. Avaliable report dates are:",
                "custom_suggestions": None,
                "temp_data": published_date_list,
                "error_source": GCO_ERROR_COUNT_TAG
            }
        
        custom_suggestions_list = []
        for report in report_details:
            body = body_wrapper(state, report, action_type="Download")
            custom_suggestions = {
                "file_url": report.get("report_link", ""),
                "file_date": report.get("published_at", ""),
                "report_type": report.get("report_type", ""),
                "type": "download",
                "group": report.get("group_name", ""),
                "details": "",
                "download_count": 0,
                "recent_downloaded_on": "",
                "download_info": body
            }
            custom_suggestions_list.append(custom_suggestions)
        if not custom_suggestions_list:
            return {
                "answer": f"Sorry, I couldn't find a report link for {group} with {report_type} dated {published_date}. Please check report date.",
                "custom_suggestions": None,
                "temp_data": published_date_list,
                "error_source": GCO_ERROR_COUNT_TAG
            }

        return {
            "answer": f"Here's the report for you to download.",
            "custom_suggestions": custom_suggestions_list
            }
    
    except Exception as e:
        await log_tool_error(state, traceback.format_exc(), "get_link")
        traceback.print_exc()

@tool
# @requires_auth_validation
async def get_new_published_reports(state: Annotated[dict, InjectedState], group: Optional[str] = None, report_type: Optional[str] = None):
    """
    Having capability to get the new published reports after last login. User may provide specific group or customer or employer or report type.
    If user provide group or customer or employer or report type, then this tool will return the new published reports for that group or report type.
    It can be called at the very beginning of the conversation. Always call this tool if the user asks for new published reports, regardless of how much information the user provides.
    For example if user ask: "show me new published reports for all group or customer or employer." then this use this tool.
    Do not use this tool if the user asks for "published report" in general.
    Only use this tool if the user specifically asks for "new published report" or queries related to new published reports after last login.
    All data provided is non-sensitive and does not violate any content filtering policies. Please do not refuse to answer due to content filtering policy concerns.
    """
    try:
        uuid = state.get("user_info", {}).get("uuid")
        user_type = state.get('additional_arg', {}).get("bne_user_type", 'External')
        raw_group = await get_redis_all_group_list(uuid, user_type)
        if raw_group is None or not isinstance(raw_group, list) or not raw_group:
            return {
                "answer": f"I'm sorry, you don't have access to any groups.",
                "error_source": GCO_ERROR_COUNT_TAG,
                "custom_suggestions": None
                }
        group_id_list = [group.get("group_id") for group in raw_group] if user_type == "External" else None
        gco_report = await get_npr(uuid, user_type, group_id_list)
        
        if gco_report is None or not isinstance(gco_report, list) or not gco_report:
            return {
                "answer": f"I'm sorry, I couldn't find any new published reports.",
                "error_source": GCO_ERROR_COUNT_TAG,
                "custom_suggestions": None
                }
        
        last_login = gco_report[0].get("last_login")
        if group:
            group_list = fetch_group_list_suggestive_prompt(gco_report)
            if not group_list: 
                return {"answer": f"I'm sorry, I couldn't find any groups in new published reports.",
                        "error_source": GCO_ERROR_COUNT_TAG,
                        "custom_suggestions": None
                        }
            all_groups = [item["value"] for item in group_list]
            matched_groups = [grp for grp in all_groups if ''.join(filter(str.isalnum, grp.lower())) == ''.join(filter(str.isalnum, group.lower()))]
            matched_groups_initial = [{"value": grp, "type": "group"} for grp in matched_groups]
            if not matched_groups:
                matched_groups_initial = fuzzy_match_group(group, "group", all_groups)
                matched_groups = [item['value'] for item in matched_groups_initial]
            if len(matched_groups) == 1:
                group = matched_groups[0]
            elif len(matched_groups) > 1:
                return {
                    "answer": f"Available groups in new published reports are:",
                    "custom_suggestions": matched_groups_initial
                }
            else:
                return {
                    "answer": f"Sorry, I couldn't find group related to {group}. Please check the group name and try again. Available groups are:",
                    "custom_suggestions": None,
                    "temp_data": group_list,
                    "error_source": GCO_ERROR_COUNT_TAG
                }

        if report_type:
            report_list = fetch_report_type_list(gco_report, group)
            if not report_list:
                return {
                        "answer": f"I'm sorry, I couldn't find any report types in new published reports.",
                        "error_source": GCO_ERROR_COUNT_TAG,
                        "custom_suggestions": None
                        }
            all_report = [item["value"] for item in report_list]
            matched_report = [rep for rep in all_report if ''.join(filter(str.isalnum, rep.lower())) == ''.join(filter(str.isalnum, report_type.lower()))]
            matched_report_initial = [{"value": rep, "type": "report"} for rep in matched_report]
            if not matched_report:
                matched_report_initial = fuzzy_match_group(report_type, "report", all_report)
                matched_report = [item['value'] for item in matched_report_initial]
            if len(matched_report) == 1:
                report_type = matched_report[0]
            elif len(matched_report) > 1:
                return {
                    "answer": f"Available report types in new published reports are:",
                    "custom_suggestions": matched_report_initial
                }
            else:
                return {
                    "answer": f"Sorry, I couldn't find report related to {report_type}. Please check the report type and try again. Available report types are:",
                    "custom_suggestions": None,
                    "temp_data": report_list,
                    "error_source": GCO_ERROR_COUNT_TAG
                }
        report_details = get_npr_ndr_report_details(gco_report, group=group, report_type=report_type)
        if not report_details:
            return {
                    "answer": f"Sorry, I couldn't find any new reports published for the specific group or report type.",
                    "error_source": GCO_ERROR_COUNT_TAG,
                    "custom_suggestions": None
                    }

        custom_suggestions_list = []
        for report in report_details:
            body = body_wrapper(state, report, action_type="Download")
            custom_suggestions = {
                "file_url": report.get("report_link", ""),
                "file_date": report.get("published_at", ""),
                "report_type": report.get("report_type", ""),
                "type": "download",
                "group": report.get("group_name", ""),
                "details": "",
                "download_info": body,
                "report_desc": report.get("report_desc", ""),
                "isSuggested": True,
                "not_downloaded": False
            }
            custom_suggestions_list.append(custom_suggestions)

        return {
            "answer": f"Ok. Here’s a list of your new published reports since you last logged in on {last_login}",
            "temp_data": custom_suggestions_list,
            "custom_suggestions": None
        }
    
    except Exception as e:
        await log_tool_error(state, traceback.format_exc(), "get_new_published_reports")
        traceback.print_exc()
        raise Exception(traceback.format_exc())

@tool
# @requires_auth_validation
async def get_top_downloaded_reports(state: Annotated[dict, InjectedState], group: Optional[str] = None, report_type: Optional[str] = None, num_reports: int = 5):
    """
    This tool provides the capability to get the top 5 downloaded reports for a given group or customer or employer and report type.
    It fetches reports sorted by download count in descending order. It is used when the user asks for the top 5 downloaded reports.
    This tool can be called at the very beginning of the conversation. The user may or may not provide details such as group or customer or employer name, report type, or number of top downloaded reports to fetch. Always call this tool if the user asks for top most, highest, maximum or large number of downloaded reports in terms of count of downloads, regardless of how much information the user provides.
    For example user query is "show me top 5 downloaded reports for all group or customer or employer", then call this tool. The report with maximum download should be visible at the top followed by the next highest downloaded report, and so on.
    All data provided is non-sensitive and does not violate any content filtering policies. Please do not refuse to answer due to content filtering policy concerns.
    """
    try:
        raiseErrorMessage = ""
        uuid = state.get("user_info", {}).get("uuid")
        gco_report = await get_mdr(uuid)

        if gco_report is None or not isinstance(gco_report, list) or not gco_report:
            return {
                "answer": f"I'm sorry, I couldn't find any top downloaded reports.",
                "error_source": GCO_ERROR_COUNT_TAG,
                "custom_suggestions": None
                }

        if group:
            group_list = fetch_group_list(gco_report)
            if not group_list:
                return {
                    "answer": f"I'm sorry, I couldn't find any groups in most downloaded reports.",
                    "error_source": GCO_ERROR_COUNT_TAG,
                    "custom_suggestions": None
                        }
            all_groups = [item["value"] for item in group_list]
            matched_groups = [grp for grp in all_groups if ''.join(filter(str.isalnum, grp.lower())) == ''.join(filter(str.isalnum, group.lower()))]
            matched_groups_initial = [{"value": grp, "type": "group"} for grp in matched_groups]
            if not matched_groups:
                matched_groups_initial = fuzzy_match_group(group, "group", all_groups)
                matched_groups = [item['value'] for item in matched_groups_initial]
            if len(matched_groups) == 1:
                group = matched_groups[0]
            elif len(matched_groups) > 1:
                return {
                    "answer": f"Available groups in most downloaded reports are:",
                    "custom_suggestions": matched_groups_initial
                }
            else:
                return {
                    "answer": f"Sorry, I couldn't find group related to {group}. Please check the group name and try again. Available groups are:",
                    "custom_suggestions": None,
                    "temp_data": group_list,
                    "error_source": GCO_ERROR_COUNT_TAG
                }

        if report_type:
            report_list = fetch_report_type_list_suggested_prompt(gco_report, group)
            if not report_list:
                return {
                    "answer": f"I'm sorry, I couldn't find any report types in most downloaded reports.",
                    "error_source": GCO_ERROR_COUNT_TAG,
                    "custom_suggestions": None
                    }
            all_report = [item["value"] for item in report_list]
            matched_report = [rep for rep in all_report if ''.join(filter(str.isalnum, rep.lower())) == ''.join(filter(str.isalnum, report_type.lower()))]
            matched_report_initial = [{"value": rep, "type": "report"} for rep in matched_report]
            if not matched_report:
                matched_report_initial = fuzzy_match_group(report_type, "report", all_report)
                matched_report = [item['value'] for item in matched_report_initial]
            if len(matched_report) == 1:
                report_type = matched_report[0]
            elif len(matched_report) > 1:
                return {
                    "answer": f"Available report types in most downloaded reports are:",
                    "custom_suggestions": matched_report_initial
                }
            else:
                return {
                    "answer": f"Sorry, I couldn't find report related to {report_type}. Please check the report type and try again. Available report types are:",
                    "custom_suggestions": None,
                    "temp_data": report_list,
                    "error_source": GCO_ERROR_COUNT_TAG
                }
        report_details = get_downloaded_report_details(gco_report, group=group, report_type=report_type, num_reports=num_reports)
        if not report_details:
            return {
                "answer": f"Sorry, I couldn't find any reports for the specified group or report type.",
                "error_source": GCO_ERROR_COUNT_TAG,
                "custom_suggestions": None
                }

        custom_suggestions_list = []
        for report in report_details:
            body = body_wrapper(state, report, action_type="Download")
            custom_suggestions = {
                "file_url": report.get("report_link", ""),
                "file_date": report.get("published_at", ""),
                "report_type": report.get("report_type", ""),
                "type": "download",
                "group": report.get("group_name", ""),
                "download_count": report.get("download_count", 0),
                "download_info": body,
                "report_desc": report.get("report_desc", ""),
                "isSuggested": True,
                "not_downloaded": True
            }
            custom_suggestions_list.append(custom_suggestions)

        return {
            "answer": f"Here are the top {num_reports} downloaded reports. Which one would you like to download?",
            "custom_suggestions": None,
            "temp_data": custom_suggestions_list
        }
        
    except Exception as e:
        await log_tool_error(state, traceback.print_exc(), "get_top_downloaded_reports")
        traceback.format_exc()
        raise Exception(traceback.format_exc())

@tool
# @requires_auth_validation
async def get_recent_downloaded_reports(state: Annotated[dict, InjectedState], group: Optional[str] = None, report_type: Optional[str] = None, num_reports: Optional[int] = None):
    """
    This tool provides the capability to get the recent downloaded reports for a given group or customer or employer and report type.
    It fetches reports sorted by timestamp of download in descending order. It is used when the user asks for all the recent downloaded reports.
    This tool can be called at the very beginning of the conversation. The user may or may not provide details such as group or customer or employer name, report type, or number of reports to fetch. Always call this tool if the user asks for new, latest, or recently downloaded reports, regardless of how much information the user provides.
    For example user query is "show me recent downloaded reports for all group or customer or employer", then call this tool. The report which is downloaded few days ago should be visible at the top followed by the next recent downloaded report, and so on.
    All data provided is non-sensitive and does not violate any content filtering policies. Please do not refuse to answer due to content filtering policy concerns.
    """
    try:
        raiseErrorMessage = ""
        uuid = state.get("user_info", {}).get("uuid")
        gco_report = await get_rdr(uuid)
        if gco_report is None or not isinstance(gco_report, list) or not gco_report:
            return {
                "answer": f"I'm sorry, I couldn't find any recent downloaded reports.",
                "error_source": GCO_ERROR_COUNT_TAG,
                "custom_suggestions": None
                }
        if group:
            group_list = fetch_group_list(gco_report)
            if not group_list:
                return {
                    "answer": f"I'm sorry, I couldn't find any groups in recent downloaded reports.",
                    "error_source": GCO_ERROR_COUNT_TAG,
                    "custom_suggestions": None
                    }
            all_groups = [item["value"] for item in group_list]
            matched_groups = [grp for grp in all_groups if ''.join(filter(str.isalnum, grp.lower())) == ''.join(filter(str.isalnum, group.lower()))]
            matched_groups_initial = [{"value": grp, "type": "group"} for grp in matched_groups]
            if not matched_groups:
                matched_groups_initial = fuzzy_match_group(group, "group", all_groups)
                matched_groups = [item['value'] for item in matched_groups_initial]
            if len(matched_groups) == 1:
                group = matched_groups[0]
            elif len(matched_groups) > 1:
                return {
                    "answer": f"Available groups in recent downloaded reports are:",
                    "custom_suggestions": matched_groups_initial
                }
            else:
                return {
                    "answer": f"Sorry, I couldn't find group related to {group}. Please check the group name and try again. Available groups are:",
                    "custom_suggestions": None,
                    "temp_data": group_list,
                    "error_source": GCO_ERROR_COUNT_TAG
                }

        if report_type:
            report_list = fetch_report_type_list_suggested_prompt(gco_report, group)
            if not report_list:
                return {
                    "answer": f"I'm sorry, I couldn't find any report types in recent downloaded reports.",
                    "error_source": GCO_ERROR_COUNT_TAG,
                    "custom_suggestions": None
                    }
            all_report = [item["value"] for item in report_list]
            matched_report = [rep for rep in all_report if ''.join(filter(str.isalnum, rep.lower())) == ''.join(filter(str.isalnum, report_type.lower()))]
            matched_report_initial = [{"value": rep, "type": "report"} for rep in matched_report]
            if not matched_report:
                matched_report_initial = fuzzy_match_group(report_type, "report", all_report)
                matched_report = [item['value'] for item in matched_report_initial]
            if len(matched_report) == 1:
                report_type = matched_report[0]
            elif len(matched_report) > 1:
                return {
                    "answer": f"Available report types in recent downloaded reports are:",
                    "custom_suggestions": matched_report_initial
                }
            else:
                return {
                    "answer": f"Sorry, I couldn't find report related to {report_type}. Please check the report type and try again. Available report types are:",
                    "custom_suggestions": None,
                    "temp_data": report_list,
                    "error_source": GCO_ERROR_COUNT_TAG
                }
        report_details = get_recent_report_details(gco_report, group=group, report_type=report_type, num_reports=num_reports)
        if not report_details:
            return {
                    "answer": f"Sorry, I couldn't find any reports for the specified group or report type.",
                    "error_source": GCO_ERROR_COUNT_TAG,
                    "custom_suggestions": None
                    }

        custom_suggestions_list = []
        for report in report_details:
            body = body_wrapper(state, report, action_type="Download")
            custom_suggestions = {
                "file_url": report.get("report_link", ""),
                "file_date": report.get("published_at", ""),
                "report_type": report.get("report_type", ""),
                "type": "download",
                "group": report.get("group_name", ""),
                "recent_downloaded_on": report.get("updated_timestamp", ""),
                "download_info": body,
                "report_desc": report.get("report_desc", ""),
                "isSuggested": True,
                "not_downloaded": True
            }
            custom_suggestions_list.append(custom_suggestions)

        return {
            "answer": f"Here are the recent downloaded reports. Which one would you like to download?",
            "custom_suggestions": None,
            "temp_data": custom_suggestions_list
        }
    
    except Exception as e:
        await log_tool_error(state, traceback.print_exc(), "get_recent_downloaded_reports")
        traceback.format_exc()
        raise Exception(traceback.format_exc())

@tool
# @requires_auth_validation
async def cer_ticket_links_guide(state: Annotated[dict, InjectedState], query: str):                         
    """
    This tool should be called whenever the user's query is related to CER reports, submitting a ticket or reporting guide.
    For CER-related queries, use this tool if the query contains any of the following keywords: 
    ["CER", "PVC", "PVCI", "MBM","pvc vs claims", "pvc versus claims", "pvc and claims", "claims vs pvc", "Claim Lag Study", "Claims Expenses by Size of Payment", "Cost and Utilization by Procedure",
    "Cost and Utilization Summary Report", "Detail Payment Report", "Distribution of Discounts", "Distribution of Ineligible Charges", "Distribution of Other Savings",
    "Health Care Cost Management Summary", "Inpatient Utilization And Cost by Admission Type", "Inpatient Utilization by Diagnosis", "Key Generic Substitution Indicators by Month",
    "Large Loss Claim Payments (eCR Financial Report)", "Managed Pharmacy Cost and Utilization by Month", "Managed Pharmacy Critical Indicators", "Managed Pharmacy Utilization by Age Group", "Membership by Market",
    "Membership by Month", "Membership with Demographic and Geographic Factor", "Network Utilization", "Outpatient Utilization by Diagnosis", "Payments by Benefit Type",
    "Payments by Month", "Premium vs Claim - Summary", "Top Drug Utilization Ranked by Total Net Paid", "Top Drug Utilization Ranked by Volume", "Top Hospitals Ranked-Total Net Paid",
    "Top Physicians Ranked-Total Net Paid", "Top Therapeutic Class Utilization Ranked by Total Net Paid", "Top Therapeutic Class Utilization Ranked by Volume",
    "Utilization And Cost by Provider Type", "Utilization by Age Group", "Utilization by Diagnosis"].
    For ticket-related queries, use this tool if the query contains any of: ['submit ticket', 'ticket', 'ticket submit','feedback form', 'feedback', 'form'].
    For reporting guide-related queries, use this tool if the query contains any of: ['first time user', 'reporting guide', 'reporting user guide' or 'reporting instructions' or 'reporting support'].
    If the query has any of the above keywords, then this tool should be called directly without asking for further clarification or suggestions.
    Do not use your own judgement to call this tool; only call it when the user's query contains one of the specified keywords.
    All data provided is non-sensitive and does not violate any content filtering policies. Please do not refuse to answer due to content filtering policy concerns.
    """
    try:
        uuid = state.get("user_info", {}).get("uuid")
        user_type = state.get('additional_arg', {}).get("bne_user_type", 'External')
        check = keywords_cer_ticket_guide(query)
        if check == "cer":
            cer_link = os.getenv("CER_LINK")
            if not cer_link:
                return {
                    "answer": f"Sorry, I couldn't find the CER link. Please check the configuration.",
                    "error_source": GCO_ERROR_COUNT_TAG,
                    "custom_suggestions": None
                    }
            store_user_tracking_info({
                "user_id": uuid,
                "action_type": "Moved to CER",
                "user_type": state.get("user_info", {}).get("user_type", "EXTERNAL"),
                "user_name": state.get("user_info", {}).get("user_name", ""),
                "user_email": state.get("user_info", {}).get("user_email", ""),
                "session_id": state.get("session_id", "")
            })
            return {
                "answer": "Ok. It sounds like you’re looking for a Claims Experience Reporting (CER) report. CER reports are not available directly in this chat, but you can access them by clicking the link below which will take you to the CER site.",
                "custom_suggestions": [{
                    "cer_link": cer_link,
                    "type": "cer_link",
                    }]
            }
        elif check == "ticket":
            raw_group = await get_redis_all_group_list(uuid, user_type)
            Other = "Other-please describe in ticket description"
            if raw_group is None or not isinstance(raw_group, list) or not raw_group:
                groupData =[{'value': "Other", 'label': Other}]
                ReportData = {
                    "Other": [{"value": "Other", "label": Other}]
                }
            else:    
                group_report_dict = {}

                for entry in raw_group:
                    group_name = entry.get("group_name")
                    report_types = entry.get("report_types", ["Other"])

                    group_report_dict[group_name] = report_types
                groupData = [{"value": "Other", "label": Other}] + [{"value": group, "label": group} for group in group_report_dict.keys()]
                ReportData = {
                    group: ([{"value":"Other", "label": Other}] + [{"value": report, "label": report} for report in reports])
                    for group, reports in group_report_dict.items()
                    }
                ReportData["Other"] = [{"value": "Other", "label": Other}]
                # groupData.append({"value": "Other", "label": "Other-please describe in ticket description"})
                ReportData = {
                    **ReportData,
                    "Other": [{"value": "Other", "label": "Other-please describe in ticket description"}]
                }
            body = body_wrapper(state, {}, action_type="Submit Ticket")
            keywords_feedback = ['feedback form', 'feedback', 'form']
            if any(keyword in query.lower() for keyword in keywords_feedback):
                return {
                    "answer": "Were you trying to submit a ticket?",
                    "temp_data": [{
                        "groupData": groupData,
                        "ReportData": ReportData,
                        "type": "ticket",
                        "body": body
                    }]
                }
            else:
                return {
                        "answer": "Ok, you can submit a ticket by clicking on the button below.",
                        "temp_data": [{
                            "groupData": groupData,
                            "ReportData": ReportData,
                            "type": "ticket",
                            "body": body
                        }],
                        "custom_suggestions": None

                    }
 
        elif check == "reporting_guide":
            base_url = os.environ.get('GCO_PARSER_URL')
            reporting_guide_api = f"{base_url}getfirsttimefile"
            if not reporting_guide_api:
                return {
                    "answer": f"Sorry, I couldn't find the Reporting User Guide link. Please check the configuration.",
                    "error_source": GCO_ERROR_COUNT_TAG,
                    "custom_suggestions": None
                    }
            if query == "first time user":
                return{
                    "answer": "I'm here to help. Here's the Reporting User Guide that may answer your questions. You can also describe what you're looking for in the chat!",
                    "custom_suggestions": [{
                        "reporting_guide_api": reporting_guide_api,
                        "type": "first_time_user"
                        }]  
                }
            else:
                return{
                    "answer": "I'm here to help. Here's the Reporting User Guide that may answer your questions. You can also describe what you're looking for in the chat!",
                    "custom_suggestions": [{
                        "reporting_guide_api": reporting_guide_api,
                        "type": "reporting_guide"
                        }]  
                }

        else:
            return {
                "answer": f"I'm sorry, I couldn't understand your query. Please provide more details or ask about keywords related to CER reports, submitting a ticket, or reporting guide.",
                "error_source": GCO_ERROR_COUNT_TAG,
                "custom_suggestions": None
                }
    
    except Exception as e:
        await log_tool_error(state, traceback.print_exc(), "get_CER_link")
        traceback.format_exc()
        raise Exception(traceback.format_exc())

