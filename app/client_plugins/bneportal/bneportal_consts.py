BNE_USER_TYPE_BROKER = "Broker"
BNE_USER_TYPE_EMPLOYER = "Employer"
BNE_USER_TYPE_INTERNAL = "Internal"

NONAUTH_DOC_SEARCH_THRESHOLD = 1.18
THRESHOLD_FOR_HUMAN_ASSISTANCE = 2
COUNT_OF_MSG_TO_MONITOR = 3

BNE_BROKER_DOC_SEARCH_TYLE_TYPE = "Broker Training"
BNE_EMPLOYER_DOC_SEARCH_TYLE_TYPE = "Employer Training"
BNE_INTERNAL_DOC_SEARCH_TYLE_TYPE = "Internal Training"

BNE_BROKER_TRAINING_VIDEO_TABLE_NAME = "bne_broker_training_video"
BNE_EMPLOYER_TRAINING_VIDEO_TABLE_NAME = "bne_employer_training_video"
BNE_INTERNAL_TRAINING_VIDEO_TABLE_NAME = "bne_internal_training_video"

BNE_BROKER_TRAINING_MATERIAL_TABLE_NAME = "bne_broker_training_material"
BNE_EMPLOYER_TRAINING_MATERIAL_TABLE_NAME = "bne_employer_training_material"
BNE_INTERNAL_TRAINING_MATERIAL_TABLE_NAME = "bne_internal_training_material"

STATE_OPTION_MAP = {
    'Alabama': 'AL',
    'Alaska': 'AK',
    'Arizona': 'AZ',
    'Arkansas': 'AR',
    'California': 'CA',
    'Colorado': 'CO',
    'Connecticut': 'CT',
    'Delaware': 'DE',
    'Florida': 'FL',
    'Georgia': 'GA',
    'Hawaii': 'HI',
    'Idaho': 'ID',
    'Illinois': 'IL',
    'Indiana': 'IN',
    'Iowa': 'IA',
    'Kansas': 'KS',
    'Kentucky': 'KY',
    'Louisiana': 'LA',
    'Maine': 'ME',
    'Maryland': 'MD',
    'Massachusetts': 'MA',
    'Michigan': 'MI',
    'Minnesota': 'MN',
    'Mississippi': 'MS',
    'Missouri': 'MO',
    'Montana': 'MT',
    'Nebraska': 'NE',
    'New Jersey': 'NJ',
    'New York': 'NY',
    'Nevada': 'NV',
    'New Hampshire': 'NH',
    'New Mexico': 'NM',
    'North Carolina': 'NC',
    'North Dakota': 'ND',
    'Ohio': 'OH',
    'Oklahoma': 'OK',
    'Oregon': 'OR',
    'Pennsylvania': 'PA',
    'Rhode Island': 'RI',
    'South Carolina': 'SC',
    'South Dakota': 'SD',
    'Tennessee': 'TN',
    'Texas': 'TX',
    'Utah': 'UT',
    'Vermont': 'VT',
    'Virginia': 'VA',
    'Washington': 'WA',
    'West Virginia': 'WV',
    'Wisconsin': 'WI',
    'Wyoming': 'WY',
    'Virgin Islands': 'USVI'
}


GROUP_SIZE_OPTION_MAP = {
    "50 or fewer": "2-50",
    "51-100": "51-100",
    "100+": "100+",
    "All group sizes": "ALL"
}

LANGUAGE_OPTION_MAP = {
    "English": "ENGLISH",
    "Spanish": "SPANISH",
    "Vietnamese": "VIETNAMESE",
    "Chinese": "CHINESE",
    "Japanese": "JAPANESE",
    "Korean": "KOREAN",
    "Tagalog": "TAGALOG"
}