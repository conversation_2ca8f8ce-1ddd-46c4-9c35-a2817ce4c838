from utils.general_utils import extract_values
from client_plugins.bnemobile.bnemobile_router_tools import ToCommCentreAssistant, ToSbcRetrievalAssistant, bnemobile_router_tools
from graph.state import State
from typing import Literal
from services.event_logger_service import logger
from utils.general_utils import validate_tool_call
from langgraph.prebuilt import tools_condition
from langgraph.graph import END
from services.get_client_ids_and_assistants_service import validate_assistant

async def route_to_workflow(
    state: State,
) -> Literal[
    "primary",
    "sbc_retrieval_agent_subgraph",
    "comm_centre_subgraph"
]:
    """If we are in a delegated state, route directly to the appropriate assistant."""
    dialog_state = state.get("dialog_state")
    dialog_state = extract_values(dialog_state)
    #INFO: Log agent traversal
    uuid = state.get("user_info")["uuid"]
    user_name = state.get("user_info").get("user_name", None)
    session_id = state.get("user_info")["session_id"]
    request_id = state.get("user_info").get("request_id", None)
    client_id = state.get("user_info")["client_id"]
    await logger.info(uuid, user_name, session_id, request_id, client_id, "router", "route_to_workflow", "State", dialog_state[-1] if dialog_state else "primary", None, None)
    if not dialog_state:
        return "primary"
    
    return dialog_state[-1]

async def route_bnemobile_assistant(
    state: State,
):  
    available_tools = [ToCommCentreAssistant, ToSbcRetrievalAssistant]
    route = tools_condition(state)
    if route == END:
        return END
    if route == "tools" and validate_tool_call(state["messages"][-1].tool_calls, available_tools):
        tool_calls = state["messages"][-1].tool_calls
        if len(tool_calls) > 1:
            return "restrict_parallel_agents_run"
        
        if not await validate_assistant(state.get("user_info")["client_id"], tool_calls[0]["name"]):
            uuid = state.get("user_info")["uuid"]
            user_name = state.get("user_info").get("user_name", None)
            session_id = state.get("user_info")["session_id"]
            request_id = state.get("user_info").get("request_id", None)
            client_id = state.get("user_info")["client_id"]
            await logger.error(uuid, user_name, session_id, request_id, client_id, "router", "route_bnemobile_assistant", "State", tool_calls[0]["name"], None, 400, "Routed agent is disabled.")
            return "invalid_assistant"
        
        if tool_calls[0]["name"] in bnemobile_router_tools:
            uuid = state.get("user_info")["uuid"]
            user_name = state.get("user_info").get("user_name", None)
            session_id = state.get("user_info")["session_id"]
            request_id = state.get("user_info").get("request_id", None)
            client_id = state.get("user_info")["client_id"]
            await logger.info(uuid, user_name, session_id, request_id, client_id, "router", "route_bnemobile_assistant", "State", tool_calls[0]["name"], None, None)
        if tool_calls[0]["name"] == ToCommCentreAssistant.__name__ :
            return "enter_comm_centre_assistant"
        elif tool_calls[0]["name"] == ToSbcRetrievalAssistant.__name__ :
            return "enter_sbc_retrieval_agent_assistant"
    elif route == "tools":
        return "invalid_tool"
    return "primary"