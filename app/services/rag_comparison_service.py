"""
RAG Comparison Service for A/B Testing PostgreSQL vs Azure AI Search

This service provides comprehensive comparison capabilities between PostgreSQL pgvector
and Azure AI Search for RAG operations, including performance metrics, result quality,
and detailed analytics.
"""

import asyncio
import time
import json
import uuid
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum

# PostgreSQL imports
from config.postgres import get_pg_connection, return_pg_connection
from config.openAI import embeddings
from client_plugins.surest.surest_consts import SUREST_TRAINING_MATERIAL_TABLE_NAME

# Azure AI Search imports
from services.azure_ai_search_service import azure_similarity_search, AZURE_INDEX_MAPPINGS

class SearchProvider(Enum):
    POSTGRESQL = "postgresql"
    AZURE_AI_SEARCH = "azure_ai_search"

@dataclass
class SearchResult:
    """Standardized search result format"""
    file_name: str
    file_url: str
    page_content: str
    score: float
    created_at: Optional[str] = None
    provider: Optional[str] = None
    metadata: Optional[Dict] = None

@dataclass
class PerformanceMetrics:
    """Performance metrics for search operations"""
    provider: str
    query: str
    embedding_time_ms: float
    search_time_ms: float
    total_time_ms: float
    results_count: int
    embedding_dimension: int
    timestamp: str
    error: Optional[str] = None

@dataclass
class ComparisonResult:
    """Complete comparison result between providers"""
    query: str
    postgresql_results: List[SearchResult]
    azure_results: List[SearchResult]
    postgresql_metrics: PerformanceMetrics
    azure_metrics: PerformanceMetrics
    quality_metrics: Dict[str, Any]
    timestamp: str
    comparison_id: str

class RAGComparisonService:
    """Service for comparing PostgreSQL and Azure AI Search RAG performance"""
    
    def __init__(self):
        self.comparison_history: List[ComparisonResult] = []
    
    async def compare_search_providers(
        self,
        query: str,
        top_k: int = 5,
        include_quality_analysis: bool = True
    ) -> ComparisonResult:
        """
        Compare search results between PostgreSQL and Azure AI Search
        
        Args:
            query: Search query
            top_k: Number of results to return
            include_quality_analysis: Whether to include quality metrics
            
        Returns:
            ComparisonResult with detailed comparison data
        """
        comparison_id = str(uuid.uuid4())
        timestamp = datetime.now().isoformat()
        
        # Run both searches concurrently
        postgresql_task = self._search_postgresql(query, top_k)
        azure_task = self._search_azure_ai_search(query, top_k)
        
        postgresql_results, postgresql_metrics = await postgresql_task
        azure_results, azure_metrics = await azure_task
        
        # Calculate quality metrics if requested
        quality_metrics = {}
        if include_quality_analysis:
            quality_metrics = await self._calculate_quality_metrics(
                query, postgresql_results, azure_results
            )
        
        # Create comparison result
        comparison = ComparisonResult(
            query=query,
            postgresql_results=postgresql_results,
            azure_results=azure_results,
            postgresql_metrics=postgresql_metrics,
            azure_metrics=azure_metrics,
            quality_metrics=quality_metrics,
            timestamp=timestamp,
            comparison_id=comparison_id
        )
        
        # Store in history
        self.comparison_history.append(comparison)
        
        return comparison
    
    async def _search_postgresql(
        self,
        query: str,
        top_k: int
    ) -> Tuple[List[SearchResult], PerformanceMetrics]:
        """Search using PostgreSQL pgvector"""
        start_time = time.time()
        error = None
        results = []
        
        try:
            # Generate embedding
            embedding_start = time.time()
            question_embedding = await embeddings.aembed_query(query)
            embedding_time = (time.time() - embedding_start) * 1000
            
            # Perform PostgreSQL search
            search_start = time.time()
            pg_connection = await get_pg_connection()
            cursor = pg_connection.cursor()
            
            similarity_search_query = f"""
            SELECT file_name, file_url, page_content, created_at, embedding <-> %s::vector AS score
            FROM {SUREST_TRAINING_MATERIAL_TABLE_NAME}
            ORDER BY score
            LIMIT %s
            """
            
            await cursor.execute(similarity_search_query, (question_embedding, top_k))
            raw_results = await cursor.fetchall()
            search_time = (time.time() - search_start) * 1000
            
            # Convert to standardized format
            for row in raw_results:
                file_name, file_url, page_content, created_at, score = row
                results.append(SearchResult(
                    file_name=file_name or "",
                    file_url=file_url or "",
                    page_content=page_content or "",
                    score=float(score),
                    created_at=created_at.isoformat() if created_at else None,
                    provider=SearchProvider.POSTGRESQL.value,
                    metadata={"cosine_distance": float(score)}
                ))
            
            await return_pg_connection(pg_connection)
            
        except Exception as e:
            error = str(e)
            embedding_time = 0
            search_time = 0
        
        total_time = (time.time() - start_time) * 1000
        
        metrics = PerformanceMetrics(
            provider=SearchProvider.POSTGRESQL.value,
            query=query,
            embedding_time_ms=embedding_time,
            search_time_ms=search_time,
            total_time_ms=total_time,
            results_count=len(results),
            embedding_dimension=1536,
            timestamp=datetime.now().isoformat(),
            error=error
        )
        
        return results, metrics
    
    async def _search_azure_ai_search(
        self,
        query: str,
        top_k: int
    ) -> Tuple[List[SearchResult], PerformanceMetrics]:
        """Search using Azure AI Search"""
        start_time = time.time()
        error = None
        results = []
        
        try:
            # Generate embedding
            embedding_start = time.time()
            question_embedding = await embeddings.aembed_query(query)
            embedding_time = (time.time() - embedding_start) * 1000
            
            # Perform Azure AI Search
            search_start = time.time()
            raw_results = await azure_similarity_search(
                index_name=AZURE_INDEX_MAPPINGS["surest_training_material"],
                query=query,
                top_k=top_k
            )
            search_time = (time.time() - search_start) * 1000
            
            # Convert to standardized format
            for result in raw_results:
                results.append(SearchResult(
                    file_name=result.get('file_name', ''),
                    file_url=result.get('file_url', ''),
                    page_content=result.get('page_content', ''),
                    score=result.get('score', 0.0),
                    created_at=result.get('created_at'),
                    provider=SearchProvider.AZURE_AI_SEARCH.value,
                    metadata={"azure_score": result.get('score', 0.0)}
                ))
            
        except Exception as e:
            error = str(e)
            embedding_time = 0
            search_time = 0
        
        total_time = (time.time() - start_time) * 1000
        
        metrics = PerformanceMetrics(
            provider=SearchProvider.AZURE_AI_SEARCH.value,
            query=query,
            embedding_time_ms=embedding_time,
            search_time_ms=search_time,
            total_time_ms=total_time,
            results_count=len(results),
            embedding_dimension=1536,
            timestamp=datetime.now().isoformat(),
            error=error
        )
        
        return results, metrics
    
    async def _calculate_quality_metrics(
        self,
        query: str,
        postgresql_results: List[SearchResult],
        azure_results: List[SearchResult]
    ) -> Dict[str, Any]:
        """Calculate quality metrics comparing the two result sets"""
        
        # Basic overlap analysis
        pg_files = {r.file_name for r in postgresql_results}
        azure_files = {r.file_name for r in azure_results}
        
        overlap = len(pg_files.intersection(azure_files))
        union = len(pg_files.union(azure_files))
        jaccard_similarity = overlap / union if union > 0 else 0
        
        # Score distribution analysis
        pg_scores = [r.score for r in postgresql_results]
        azure_scores = [r.score for r in azure_results]
        
        quality_metrics = {
            "result_overlap": {
                "common_files": overlap,
                "total_unique_files": union,
                "jaccard_similarity": jaccard_similarity,
                "postgresql_unique": len(pg_files - azure_files),
                "azure_unique": len(azure_files - pg_files)
            },
            "score_analysis": {
                "postgresql": {
                    "min_score": min(pg_scores) if pg_scores else 0,
                    "max_score": max(pg_scores) if pg_scores else 0,
                    "avg_score": sum(pg_scores) / len(pg_scores) if pg_scores else 0
                },
                "azure": {
                    "min_score": min(azure_scores) if azure_scores else 0,
                    "max_score": max(azure_scores) if azure_scores else 0,
                    "avg_score": sum(azure_scores) / len(azure_scores) if azure_scores else 0
                }
            },
            "content_analysis": {
                "avg_content_length_pg": sum(len(r.page_content) for r in postgresql_results) / len(postgresql_results) if postgresql_results else 0,
                "avg_content_length_azure": sum(len(r.page_content) for r in azure_results) / len(azure_results) if azure_results else 0
            }
        }
        
        return quality_metrics
    
    def get_performance_summary(self, limit: int = 10) -> Dict[str, Any]:
        """Get performance summary from recent comparisons"""
        recent_comparisons = self.comparison_history[-limit:]
        
        if not recent_comparisons:
            return {"message": "No comparisons available"}
        
        pg_times = []
        azure_times = []
        pg_errors = 0
        azure_errors = 0
        
        for comp in recent_comparisons:
            if comp.postgresql_metrics.error:
                pg_errors += 1
            else:
                pg_times.append(comp.postgresql_metrics.total_time_ms)
            
            if comp.azure_metrics.error:
                azure_errors += 1
            else:
                azure_times.append(comp.azure_metrics.total_time_ms)
        
        summary = {
            "total_comparisons": len(recent_comparisons),
            "postgresql": {
                "avg_response_time_ms": sum(pg_times) / len(pg_times) if pg_times else 0,
                "min_response_time_ms": min(pg_times) if pg_times else 0,
                "max_response_time_ms": max(pg_times) if pg_times else 0,
                "error_count": pg_errors,
                "success_rate": (len(pg_times) / len(recent_comparisons)) * 100
            },
            "azure_ai_search": {
                "avg_response_time_ms": sum(azure_times) / len(azure_times) if azure_times else 0,
                "min_response_time_ms": min(azure_times) if azure_times else 0,
                "max_response_time_ms": max(azure_times) if azure_times else 0,
                "error_count": azure_errors,
                "success_rate": (len(azure_times) / len(recent_comparisons)) * 100
            }
        }
        
        return summary
    
    def export_comparison_data(self, format: str = "json") -> str:
        """Export comparison data for analysis"""
        if format.lower() == "json":
            return json.dumps([asdict(comp) for comp in self.comparison_history], indent=2, default=str)
        else:
            raise ValueError("Only JSON format is currently supported")

# Global service instance
rag_comparison_service = RAGComparisonService()

# Convenience functions for easy integration
async def compare_rag_providers(query: str, top_k: int = 5) -> ComparisonResult:
    """Convenience function to compare RAG providers"""
    return await rag_comparison_service.compare_search_providers(query, top_k)

async def run_batch_comparison(queries: List[str], top_k: int = 5) -> List[ComparisonResult]:
    """Run comparison on multiple queries"""
    results = []
    for query in queries:
        result = await rag_comparison_service.compare_search_providers(query, top_k)
        results.append(result)
    return results
