from langgraph.graph import StateGraph, START, END
from graph.state import State, postgres_checkpointer
from graph.auth_nodes.auth_nodes import authorize_and_route_to_workflow
import asyncio

class BaseSubgraphBuilder:
    """
    Base class for standardized subgraph creation that automatically 
    adds a common entry node to all subgraphs.
    """
    
    def __init__(self, state_class=State):
        """
        Initialize a new subgraph builder with the given state class.
        
        Args:
            state_class: The state class to use for the StateGraph
        """
        self.builder = StateGraph(state_class)
        self.common_nodes_added = False
        self.has_start_edge = False
        self.start_edge_type = None
        self.start_edge_name = None
        self.start_edge_router = None
    
    def add_common_entry(self):
        """
        Add the common entry node to the graph.
        """
        if not self.common_nodes_added:
    
            entry_node_name = "authorize_and_route_to_workflow"
            common_entry_fn = authorize_and_route_to_workflow
            
            # Add the common entry node
            self.builder.add_node(entry_node_name, common_entry_fn)
            self.common_nodes_added = True
            self.entry_node_name = entry_node_name
            

            if self.has_start_edge and self.start_edge_type == "normal":
                self.builder.set_entry_point(entry_node_name)
          
                def early_exit_router(state):
                    if state.get("return_to_user"):
                        return END
                    return self.start_edge_name
                
                self.builder.add_conditional_edges(
                    entry_node_name,
                    early_exit_router
                )
                
                
            elif self.has_start_edge and self.start_edge_type == "conditional":
                
                self.builder.set_entry_point(entry_node_name)
                original_router = self.start_edge_router
                
                if asyncio.iscoroutinefunction(original_router):
                    async def early_exit_wrapper(state):
                        if state.get("return_to_user"):
                            return END
                        return await original_router(state)
                else:
                    def early_exit_wrapper(state):
                        if state.get("return_to_user"):
                            return END
                        return original_router(state)
                
                self.builder.add_conditional_edges(
                    entry_node_name,
                    early_exit_wrapper
                )
                
            else:
                raise ValueError("No entry point or START edge found to connect to.")
        
        return self
    
    def set_entry_point(self, node_name):
        """
        Set the entry point for the graph.
        
        Args:
            node_name: The name of the node to set as the entry point
            
        Returns:
            self for method chaining
        """
        self.has_start_edge = True
        self.start_edge_type = "normal"
        self.start_edge_name = node_name
        return self
    
    def set_conditional_entry_point(self, router_fn):
        """
        Set a conditional entry point for the graph.
        
        Args:
            router_fn: The router function for conditional entry
            
        Returns:
            self for method chaining
        """
        self.has_start_edge = True
        self.start_edge_type = "conditional"
        self.start_edge_router = router_fn
        return self
    
    def add_node(self, node_name, node_fn, **kwargs):
        """
        Add a node to the graph.
        
        Args:
            node_name: The name of the node
            node_fn: The node function
            kwargs: Additional arguments to pass to add_node
            
        Returns:
            self for method chaining
        """
        self.builder.add_node(node_name, node_fn, **kwargs)
        return self
    
    def add_edge(self, start_node, end_node):
        """
        Add an edge to the graph.
        
        Args:
            start_node: The name of the start node
            end_node: The name of the end node
            
        Returns:
            self for method chaining
        """
        # Check if this is a START edge
        if start_node == START:
            self.has_start_edge = True
            self.start_edge_type = "normal"
            self.start_edge_name = end_node
            return self
        # Otherwise, add the edge normally
            
        self.builder.add_edge(start_node, end_node)
        return self
    
    def add_conditional_edges(self, start_node, router_fn):
        """
        Add conditional edges to the graph.
        
        Args:
            start_node: The name of the start node
            router_fn: The router function
            
        Returns:
            self for method chaining
        """
        if start_node == START:
            self.has_start_edge = True
            self.start_edge_type = "conditional"
            self.start_edge_router = router_fn
            return self
        
        self.builder.add_conditional_edges(start_node, router_fn)
        return self
    
    def compile(self, checkpointer=postgres_checkpointer):
        """
        Compile the graph, ensuring the common entry node is added.
        
        Args:
            checkpointer: The checkpointer to use for compilation
            
        Returns:
            The compiled graph
        """
        if not self.common_nodes_added:
            self.add_common_entry()

        return self.builder.compile(checkpointer=checkpointer)
    