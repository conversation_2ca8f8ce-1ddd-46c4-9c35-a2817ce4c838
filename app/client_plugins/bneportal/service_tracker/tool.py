
from langchain_core.tools import tool
from typing import Annotated, Optional 
from langgraph.prebuilt.tool_node import InjectedState
from utils.tool_utils import log_tool_error
import traceback        
                                    

# @requires_auth_validation 
@tool
async def get_service_issues_dashboard(state: Annotated[dict, InjectedState], group_id: Optional[str] = None) -> dict: # add arguments if required
    """
        Handles all queries related to Service tracker, open issues, service tickets, service tracking, 
        service tracker details, service tracker status, service tracker updates, service tracker history, service tracker reports, 
        and service tracker inquiries ETC.
        This tool retrieves the service issues dashboard for the user.

    """
    try:
        if group_id is None:
            return {
                "answer": "Please Provide a Valid Group Id",
                "follow_up_questions": []
            }
        return {
            "client": "bne_client",
            "response_type": "service_issue_dashboard",
            "custom_chatbot_json_message": "I've pulled up a list of all your current service interactions. Please expand to view them."
        }
    except Exception as e:
        await log_tool_error(state, traceback.format_exc(), 'get_service_issues_dashboard')
        raise Exception(traceback.format_exc())
