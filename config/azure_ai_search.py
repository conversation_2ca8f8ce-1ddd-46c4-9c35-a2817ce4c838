import os
from azure.search.documents import Search<PERSON>lient
from azure.search.documents.indexes import SearchIndex<PERSON>lient
from azure.core.credentials import AzureKeyCredential
from azure.identity import DefaultAzureCredential
from auth.openAI_auth import get_openai_token
from typing import Optional
import asyncio
from functools import wraps

class AzureAISearchConfig:
    """Configuration class for Azure AI Search"""
    
    def __init__(self):
        self.endpoint = os.getenv("AZURE_AI_SEARCH_ENDPOINT", "https://zkm8aewixx3d7er-acss.search.windows.net")
        self.api_version = os.getenv("AZURE_AI_SEARCH_API_VERSION", "2024-07-01")
        self.auth_method = os.getenv("AZURE_AI_SEARCH_AUTH_METHOD", "TOKEN")  # TOKEN or KEY
        
        # For key-based auth (fallback)
        self.api_key = os.getenv("AZURE_AI_SEARCH_API_KEY")
        
        # For token-based auth (preferred - reuse OpenAI token provider)
        self.token_provider = get_openai_token if self.auth_method == "TOKEN" else None
        
    def get_credential(self):
        """Get appropriate credential based on auth method"""
        if self.auth_method == "TOKEN" and self.token_provider:
            # Use the same token provider as OpenAI
            return TokenCredential(self.token_provider)
        elif self.api_key:
            return AzureKeyCredential(self.api_key)
        else:
            # Fallback to default Azure credential
            return DefaultAzureCredential()

class TokenCredential:
    """Custom credential class that uses the OpenAI token provider"""
    
    def __init__(self, token_provider):
        self.token_provider = token_provider
        
    def get_token(self, *scopes, **kwargs):
        """Get token using the OpenAI token provider"""
        token = self.token_provider()
        # Return a token object with the required attributes
        return TokenObject(token)

class TokenObject:
    """Token object that mimics Azure SDK token structure"""
    
    def __init__(self, token):
        self.token = token
        # Set expiry to 1 hour from now (matching OpenAI token expiry)
        import time
        self.expires_on = int(time.time()) + 3600

# Global configuration instance
azure_ai_search_config = AzureAISearchConfig()

def get_search_client(index_name: str) -> SearchClient:
    """Get a SearchClient for the specified index"""
    return SearchClient(
        endpoint=azure_ai_search_config.endpoint,
        index_name=index_name,
        credential=azure_ai_search_config.get_credential(),
        api_version=azure_ai_search_config.api_version
    )

def get_index_client() -> SearchIndexClient:
    """Get a SearchIndexClient for index management"""
    return SearchIndexClient(
        endpoint=azure_ai_search_config.endpoint,
        credential=azure_ai_search_config.get_credential(),
        api_version=azure_ai_search_config.api_version
    )

# Async wrapper functions for better integration with existing async codebase
def async_wrapper(func):
    """Decorator to make sync Azure SDK calls async"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, func, *args, **kwargs)
    return wrapper

class AsyncSearchClient:
    """Async wrapper for Azure Search Client"""
    
    def __init__(self, index_name: str):
        self.client = get_search_client(index_name)
        self.index_name = index_name
    
    @async_wrapper
    def search(self, search_text: str = None, **kwargs):
        """Async search wrapper"""
        return self.client.search(search_text, **kwargs)
    
    @async_wrapper
    def upload_documents(self, documents):
        """Async document upload wrapper"""
        return self.client.upload_documents(documents)
    
    @async_wrapper
    def merge_or_upload_documents(self, documents):
        """Async document merge/upload wrapper"""
        return self.client.merge_or_upload_documents(documents)
    
    @async_wrapper
    def delete_documents(self, documents):
        """Async document delete wrapper"""
        return self.client.delete_documents(documents)

class AsyncIndexClient:
    """Async wrapper for Azure Search Index Client"""
    
    def __init__(self):
        self.client = get_index_client()
    
    @async_wrapper
    def create_index(self, index):
        """Async index creation wrapper"""
        return self.client.create_index(index)
    
    @async_wrapper
    def create_or_update_index(self, index):
        """Async index create/update wrapper"""
        return self.client.create_or_update_index(index)
    
    @async_wrapper
    def delete_index(self, index_name):
        """Async index deletion wrapper"""
        return self.client.delete_index(index_name)
    
    @async_wrapper
    def get_index(self, index_name):
        """Async get index wrapper"""
        return self.client.get_index(index_name)
    
    @async_wrapper
    def list_indexes(self):
        """Async list indexes wrapper"""
        return self.client.list_indexes()

# Index name constants
SUREST_TRAINING_MATERIAL_INDEX = "surest-training-material"
SUREST_TRAINING_VIDEO_INDEX = "surest-training-video"
BNE_TRAINING_DOCUMENT_INDEX = "bne-training-document"

# Helper function to get async clients
def get_async_search_client(index_name: str) -> AsyncSearchClient:
    """Get an async SearchClient for the specified index"""
    return AsyncSearchClient(index_name)

def get_async_index_client() -> AsyncIndexClient:
    """Get an async SearchIndexClient for index management"""
    return AsyncIndexClient()
