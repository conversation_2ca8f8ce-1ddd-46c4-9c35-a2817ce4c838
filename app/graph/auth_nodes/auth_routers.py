from langgraph.graph import <PERSON><PERSON>
from graph.state import State
from typing import Literal
from services.event_logger_service import logger
from utils.general_utils import extract_values

#ONLY FOR LEGACY GRAPH

async def route_to_workflow(
    state: State,
) -> Literal[
    "primary_assistant",
    "cirrus_assistant",
    "plan_recommendation_assistant",
    "data_converter_assistant",
    "surest_assistant",
    "__end__"
]:
    """If we are in a delegated state, route directly to the appropriate assistant."""
    if state.get("return_to_user"):
        return END
    
    dialog_state = state.get("dialog_state")
    dialog_state = extract_values(dialog_state)
    #INFO: Log agent traversal
    uuid = state.get("user_info")["uuid"]
    user_name = state.get("user_info")["user_name"]
    session_id = state.get("user_info")["session_id"]
    client_id = state.get("user_info")["client_id"]
    request_id = state.get("user_info").get("request_id", None)
    await logger.info(uuid, user_name, session_id, request_id, client_id, "router", "route_to_workflow", "State", dialog_state[-1] if dialog_state else "primary_assistant", None, None)
    if not dialog_state:
        return "primary_assistant"
    return dialog_state[-1]
