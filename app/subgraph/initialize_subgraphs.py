from subgraph.cirrus_subgraph import init_cirrus_graph
from client_plugins.surest.surest.surest_subgraph import init_surest_graph
from client_plugins.plan_recommendation.plan_recommendation_assistant.plan_recomm_subgraph import init_plan_recomm_graph
from client_plugins.samx_one.samx_one.samxone_subgraph import init_samx_one_graph
from client_plugins.samx_one.samxone_multiagent_subgraph import init_samxone_multiagent_graph
from client_plugins.bneportal.bneportal_subgraph import init_bneportal_graph
from client_plugins.bnemobile.bnemobile_subgraph import init_bnemobile_graph
from client_plugins.bnemobile.comm_center.comm_center_subgraph import init_comm_center_graph
from client_plugins.bnemobile.sbc_retrieval_agent.sbc_retrieval_agent_subgraph import init_sbc_retrieval_agent_graph
from client_plugins.bneportal.nonauth_documents_search.document_search_subgraph import init_nonauth_documents_search_graph
from client_plugins.bneportal.portal_form_enrollment.portal_form_enrollment_subgraph import init_portal_form_enrollment_graph
from client_plugins.gco_reporting.gco_reporting.gco_reporting_subgraph import init_gco_reporting_graph
from client_plugins.bneportal.compensation.compensation_subgraph import init_compensation_graph
from client_plugins.bneportal.service_tracker.service_tracker_subgraph import init_service_tracker_graph
from subgraph.primary_subgraph import init_supervisor_graph

async def initialize_subgraphs_on_startup():
    """
    Initialize the subgraphs on startup.
    """
    # Do not change the order of initialization.
    await init_plan_recomm_graph()
    await init_surest_graph()
    await init_samx_one_graph()
    await init_samxone_multiagent_graph()
    await init_cirrus_graph()
    await init_nonauth_documents_search_graph()
    await init_portal_form_enrollment_graph()
    await init_gco_reporting_graph()
    await init_compensation_graph()
    await init_service_tracker_graph()
    await init_bneportal_graph()
    await init_comm_center_graph()
    await init_sbc_retrieval_agent_graph()
    await init_bnemobile_graph()
    # Add any other subgraph initializations here. Make sure you compile supervisor graph last as it depends on all other subgraphs.
    await init_supervisor_graph()
    print("All registered subgraphs have been initialized")