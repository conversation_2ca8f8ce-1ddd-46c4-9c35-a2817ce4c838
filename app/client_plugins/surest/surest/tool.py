from langchain_core.tools import tool
from config.openAI import json_model, embeddings
from config.postgres import get_pg_connection, return_pg_connection
from client_plugins.surest.surest_consts import SUREST_PLAN_COMPARISION_DATA, SUREST_TRAINING_MATERIAL_TABLE_NAME, SUREST_TRAINING_MATERIAL_VIDEO_TABLE_NAME
import json
from typing import Annotated
from langgraph.prebuilt.tool_node import InjectedState
from utils.tool_utils import log_tool_error
import traceback
from client_plugins.surest.surest_utils.utils import log_surest_qna_data
from utils.tool_invocation_logger import ToolInvocationLogger
import time
from services.event_logger_service import logger
from services.user_info_service import extract_user_info_from_state


surest_training_material_fallback_message = "Sorry we cannot find the answer to your question. Please contact UHC Representative."

@tool
@ToolInvocationLogger.log_tool_invocation_decorator
async def surest_plan_comparision(state: Annotated[dict, InjectedState], question):
    "If a question is related to plan comparision of a surest plan with uhc or unitedhealthcare plan then this should be tagged with tool"
    try:
        return {
            "client": "surest", 
            "response_type": "plan_comparision", 
            "comparision": SUREST_PLAN_COMPARISION_DATA
        }
    except Exception as e:
        await log_tool_error(state, traceback.print_exc(), "surest_plan_comparision")
        raise Exception (traceback.format_exc())

@tool
@ToolInvocationLogger.log_tool_invocation_decorator
async def surest_training_video_qa(state: Annotated[dict, InjectedState], surest_question: str):
    """
    This tool provides access to videos related to 'surest'. It should be used whenever a user wants to watch or view training videos or any other video content related to 'surest'.
    """
    try:
        # Connect with the database
        pg_connection = await get_pg_connection()
        cursor = pg_connection.cursor()
 
        # Generate embedding for the question
        question_embedding = await embeddings.aembed_query(surest_question)
        
        # Perform similarity search using the embedding
        similarity_search_query = f"""
        SELECT file_url,file_name, embedding <-> %s::vector AS score
        FROM {SUREST_TRAINING_MATERIAL_VIDEO_TABLE_NAME}
        ORDER BY score
        LIMIT 4
        """
        await cursor.execute(similarity_search_query, (question_embedding,))
        results = await cursor.fetchall()
        
        if not results or len(results) == 0:
            return surest_training_material_fallback_message
        
        # Extract the first video's URL and the names of the other three videos
        first_video_url = results[0][0]
        follow_up_video_names = [f"Watch a video about {result[1]}" for result in results[1:]]
        
        if not first_video_url:
            return surest_training_material_fallback_message
        
        return {
           "answer": { 
               "client": "surest",
               "response_type": "video",
               "url": first_video_url
           },
           "follow_up_questions": follow_up_video_names
        }
    
    except Exception as e:
        await log_tool_error(state, traceback.print_exc(), "surest_training_video_qa")
        raise Exception (traceback.format_exc())
    finally:
        await pg_connection.commit()
        await cursor.close()
        await return_pg_connection(pg_connection)
 

 
@tool
@ToolInvocationLogger.log_tool_invocation_decorator
async def surest_training_material_qa(state: Annotated[dict, InjectedState], surest_question: str):
    """
    Provides answers to questions related to 'surest', including details about the surest plan, training materials and any related information.
    """
    try:
        # Generate embedding for the question
        pg_connection = await get_pg_connection()
        cursor = pg_connection.cursor()
        
        # Perform similarity search using the embedding with type cast
        similarity_search_query = f"""
        SELECT file_name, file_url, page_content, created_at, embedding <-> %s::vector AS score
        FROM {SUREST_TRAINING_MATERIAL_TABLE_NAME}
        ORDER BY score
        LIMIT 5
        """

        question_embedding = await embeddings.aembed_query(surest_question)

        # Log DB operation before executing query
        user_info = extract_user_info_from_state(state)
        
        start_time = time.time()
        await cursor.execute(similarity_search_query, (question_embedding,))
        execution_time_ms = (time.time() - start_time) * 1000
        
        vector_results = await cursor.fetchall()
        
        # Log DB operation details
        db_event_details = {
            "operation": "select",
            "collection": SUREST_TRAINING_MATERIAL_TABLE_NAME,
            "query": similarity_search_query,
            "update": None,
            "document_count": len(vector_results) if vector_results else 0,
            "execution_time_ms": execution_time_ms,
            "db_name": "postgres",
            "index_used": None,
            "error": None,
            "metadata": {
                "embedding_length": len(question_embedding) if question_embedding else 0
            }
        }
        
        await logger.info(
            user_info["uuid"], user_info["user_name"], user_info["session_id"], 
            user_info["request_id"], user_info["client_id"], "DB", "postgres_query",
            similarity_search_query, str(vector_results[:2]) + "...", None, None, event_details=db_event_details
        )
        
        if not vector_results or len(vector_results) == 0:
            await log_surest_qna_data(surest_question, "", surest_training_material_fallback_message, state)
            return surest_training_material_fallback_message

        vector_result_content = ""
        vector_results_file_url = ""
        highest_score_page = None

        for res in vector_results:
            file_name, file_url, page_content, created_at, score = res
            vector_result_content += page_content + "\n\n"
            if not highest_score_page:
                highest_score_page = {
                    "score": score,
                    "file_url": file_url
                }
            elif score > highest_score_page["score"]:
                highest_score_page = {
                    "score": score,
                    "file_url": file_url
                }
        
        vector_results_file_url = highest_score_page["file_url"]
        
        prompt = f"""
        Follow the given instructions and answer the question provided:
        Instructions:
        1. If the context has semantic meaning related to the question, then only answer the question. Otherwise, return the message: "{surest_training_material_fallback_message}".
        2. Do not assume anything, Do not hallucinate. Do not make up any information. Answer the question based on the provided context only donot use any external knowledge.
        3. All numeric information (numbers, statistics, percentages, dates, dollar amounts, etc.) MUST be taken directly from the context without any modification. DO NOT hallucinate or estimate any numerical values that aren't explicitly stated in the context.
        4. Based on the context, generate exactly 3 follow-up questions. Ensure these questions are semantically related to the context and are short, precise, and relevant.
        5. Make sure that all follow-up questions MUST be directly answerable using ONLY the provided context - do not suggest questions that would require additional information.
        6. Use Markdown formatting to craft the `answer`. Your response can include tables, bullet points, numbered lists, headings, code blocks, or any other Markdown elements that enhance clarity and presentation. Be creative and adapt your response style to best suit the content and context. Do not limit yourself to just paragraphs.
        7. Ensure the `answer` and `follow_up_questions` are always separate nodes in the response. Do not merge them under any circumstances.
        8. For the values which are premium or price or range which corresponds pricing, always add a disclaimer.
            Ex:- $50 -> $50(This is just for illustrative purpose real value may vary.)
        9. Remember, Surest plans are offered for group size or employee size of 2 to 50 employees. use this information if required
        10. Strictly follow this JSON response format:
        {{
            "answer": "Your answer (must always be a string in Markdown format and should not include follow-up questions)",
            "follow_up_questions": ["Question 1", "Question 2", "Question 3"]
        }}
        11. If the context does not provide enough information to answer the question, return the fallback message in the `answer` field and an empty list for `follow_up_questions`.
        Context: {vector_result_content}
        Question: {surest_question}
        Answer:
        """
        
        response = await json_model.ainvoke(prompt)
        json_response = json.loads(response.content)
        answer = json_response.get("answer", "") + "[view-source-hyper-link]" + vector_results_file_url
        follow_up_questions = json_response.get("follow_up_questions", [])
        if surest_training_material_fallback_message.lower() in answer.lower():
            await log_surest_qna_data(surest_question, vector_result_content, surest_training_material_fallback_message, state)
            return {
                "answer": surest_training_material_fallback_message,
                "follow_up_questions": []
            }
        
        # Log the Q&A data
        await log_surest_qna_data(surest_question, vector_result_content, answer, state)
        
        return {
            "answer": answer,
            "follow_up_questions": follow_up_questions
        }

    except Exception as e:
        await pg_connection.rollback()
        traceback.print_exc()
        await log_tool_error(state, traceback.print_exc(), "surest_training_material_qa")
        raise Exception (traceback.format_exc())
    finally:
        await pg_connection.commit()
        await cursor.close()
        await return_pg_connection(pg_connection)

@tool
@ToolInvocationLogger.log_tool_invocation_decorator
async def surest_training_material_compare_features(state: Annotated[dict, InjectedState], surest_question: str) -> dict:
    """
    This tool is responsible for comparing or differentiating features or anything for surest and provides a comparison result in a structured tabular format.
    This tool should be used when the question contains comparison-related terms like "pros and cons", "versus", "vs", "compare", "differences", "similarities", etc.
    Args:
        state (InjectedState): The current state of the graph.
        surest_question (str): The question to be answered.
    """
    try:
        # Generate embedding for the question
        pg_connection = await get_pg_connection()
        cursor = pg_connection.cursor()
        
        # Perform similarity search using the embedding with type cast
        similarity_search_query = f"""
        SELECT file_name, file_url, page_content, created_at, embedding <-> %s::vector AS score
        FROM {SUREST_TRAINING_MATERIAL_TABLE_NAME}
        ORDER BY score
        LIMIT 5
        """

        question_embedding = await embeddings.aembed_query(surest_question)

        await cursor.execute(similarity_search_query, (question_embedding,))
        vector_results = await cursor.fetchall()
        
        if not vector_results or len(vector_results) == 0:
            await log_surest_qna_data(surest_question, "", surest_training_material_fallback_message, state)
            return surest_training_material_fallback_message

        vector_result_content = ""

        for res in vector_results:
            file_name, file_url, page_content, created_at, score = res
            vector_result_content += page_content + "\n\n"

        # Generate structured comparison response using the context
        prompt = f"""
        Compare the following features or aspect based on the provided context and question. Create a structured comparison table.
        features or aspect: extract from the question like "deductibles", "out-of-pocket maximums", "network coverage", etc.
        Instructions:
        1. Thoroughly analyze the context provided for each features or aspect.
        2. Identify 5-8 key comparison features or aspect that best differentiate these features or aspect.
        3. If the question is about "pros and cons", adjust the column headers accordingly to "Feature/Aspect", "Pros", and "Cons" and structure the comparison to highlight advantages and disadvantages.
        4. Create a structured table with clear column headers using the exact features or aspect names from the context.
        5. Use consistent formatting for each cell - keep similar types of information in the same format across rows.
        6. The first column should be the comparison points/features.
        7. The subsequent columns should be the features or aspect being compared.
        8. Do not hallucinate or make up information.
        9. Only include information explicitly stated in the context - do not infer, assume, or add information not present.
        10. Based on the context, generate exactly 3 follow-up questions. Ensure these questions are semantically related to the context and are short, precise, and relevant. Include features or aspect name in each question.
        11. Make sure that all follow-up questions MUST be directly answerable using ONLY the provided context - do not suggest questions that would require additional information.
        12. Ensure the `follow_up_questions` are always separate nodes in the response. Do not merge it under any circumstances.
        Respond in this JSON format:
        {{
            "columns": ["Feature/Aspect", ...add the actual features or aspect names from context...],
            "rows": [
                ["Feature1", "Value for product 1", "Value for product 2", ...],
                ["Feature2", "Value for product 1", "Value for product 2", ...],
                ...
            ],
            "summary": "Brief summary highlighting key differences between the features or aspect",
            "follow_up_questions": ["Question 1", "Question 2", "Question 3"]
        }}

        Context: {vector_result_content}

        Question: {surest_question}

        Answer:
        """
        
        response = await json_model.ainvoke(prompt)
        comparison_data = json.loads(response.content)
        await log_surest_qna_data(surest_question, vector_result_content, comparison_data, state)

        return {
            "answer": {
                "client": "surest",
                "response_type": "surest_training_material_compare_features",
                "comparison_table": {
                    "columns": comparison_data.get("columns", []),
                    "rows": comparison_data.get("rows", [])
                },
                "summary": comparison_data.get("summary", ""),
            },
            "follow_up_questions": comparison_data.get("follow_up_questions", []),
        }
    except Exception as e:
        await pg_connection.rollback()
        traceback.print_exc()
        await log_tool_error(state, traceback.print_exc(), "surest_training_material_compare_features")
        raise Exception (traceback.format_exc())
    finally:
        await pg_connection.commit()
        await cursor.close()
        await return_pg_connection(pg_connection)

@tool
@ToolInvocationLogger.log_tool_invocation_decorator
async def surest_training_material_benefits_or_features(state: Annotated[dict, InjectedState], surest_question: str) -> dict:
    """
    This tool is responsible for providing benefits or list of benefits for surest based on the question.
    This tool should be used when questions are about benefits, features, advantages, "why choose", or value of surest based on the question.
    Use this tool for questions like "What are the benefits of X?", "Features of X", "Why should I choose X?", "What's the value of X?" etc.
    Args:
        state (InjectedState): The current state of the graph.
        surest_question (str): The question to be answered.
    """
    try:
        # Generate embedding for the question
        pg_connection = await get_pg_connection()
        cursor = pg_connection.cursor()
        
        # Perform similarity search using the embedding with type cast
        similarity_search_query = f"""
        SELECT file_name, file_url, page_content, created_at, embedding <-> %s::vector AS score
        FROM {SUREST_TRAINING_MATERIAL_TABLE_NAME}
        ORDER BY score
        LIMIT 5
        """

        question_embedding = await embeddings.aembed_query(surest_question)

        await cursor.execute(similarity_search_query, (question_embedding,))
        vector_results = await cursor.fetchall()
        
        if not vector_results or len(vector_results) == 0:
            await log_surest_qna_data(surest_question, "", surest_training_material_fallback_message, state)
            return surest_training_material_fallback_message

        vector_result_content = ""

        for res in vector_results:
            file_name, file_url, page_content, created_at, score = res
            vector_result_content += page_content + "\n\n"
        
        prompt = f"""
        Follow the given instructions and list the benefits for the Surest by using the context provided and question asked:
        Instructions:
        1. Carefully analyze the context to identify ALL benefits associated with Surest.
        2. For each benefit you identify:
            - Create a clear, concise title that captures the main advantage
            - Include a comprehensive description using ONLY information explicitly stated in the context
        3. Do not assume, infer, or add any information not explicitly stated in the context
        4. If the context doesn't contain clear benefits information, return the message: {surest_training_material_fallback_message}
        5. Always include title of the benefit and its description.
        6. Use exact terminology, numbers, and percentages from the context when describing benefits
        7. Based on the context, generate exactly 3 follow-up questions. Ensure these questions are semantically related to the context and are short, precise, and relevant. Include Surest in each question.
        8. Make sure that all follow-up questions MUST be directly answerable using ONLY the provided context - do not suggest questions that would require additional information.
        9. Ensure the `benefits_list` and `follow_up_questions` are always separate nodes in the response. Do not merge them under any circumstances.
        10. Respond in a JSON format with the following structure:
        {{
            "benefits_list": [
                {{
                    "title": "Clear Benefit Title",
                    "description": "Detailed description of the benefit using only information from the context"
                }},
                {{
                    "title": "Another Benefit Title",
                    "description": "Another detailed description with specific details from the context"
                }},
                ...
            ],
            "follow_up_questions": ["Question 1", "Question 2", "Question 3"]
        }}

        Context: {vector_result_content}
        Question: {surest_question}

        Answer:
        """
        
        response = await json_model.ainvoke(prompt)
        json_response = json.loads(response.content)
        benefits_list = json_response.get("benefits_list", "No specific benefits found in the documentation.")
        follow_up_questions = json_response.get("follow_up_questions", [])

        # Log the Q&A data
        await log_surest_qna_data(surest_question, vector_result_content, benefits_list, state)
        
        return {
            "answer": {
                "client": "surest",
                "response_type": "surest_training_material_benefits_or_features",
                "benefits_list": benefits_list
            },
            "follow_up_questions": follow_up_questions,
        }
    except Exception as e:
        await pg_connection.rollback()
        traceback.print_exc()
        await log_tool_error(state, traceback.print_exc(), "surest_training_material_benefits_or_features")
        raise Exception (traceback.format_exc())
    finally:
        await pg_connection.commit()
        await cursor.close()
        await return_pg_connection(pg_connection)
    
@tool
@ToolInvocationLogger.log_tool_invocation_decorator
async def surest_training_material_price_or_percentage_qa(state: Annotated[dict, InjectedState], surest_question: str) -> dict:
    """
    This tool is responsible for answering the questions where user is asking for price or quotation or percentage related information for surest.
    Args:
        state (InjectedState): The current state of the graph.
        surest_question (str): The question to be answered.
    """
    try:
        # Generate embedding for the question
        pg_connection = await get_pg_connection()
        cursor = pg_connection.cursor()
        
        # Perform similarity search using the embedding with type cast
        similarity_search_query = f"""
        SELECT file_name, file_url, page_content, created_at, embedding <-> %s::vector AS score
        FROM {SUREST_TRAINING_MATERIAL_TABLE_NAME}
        ORDER BY score
        LIMIT 5
        """

        question_embedding = await embeddings.aembed_query(surest_question)

        await cursor.execute(similarity_search_query, (question_embedding,))
        vector_results = await cursor.fetchall()
        
        if not vector_results or len(vector_results) == 0:
            await log_surest_qna_data(surest_question, "", surest_training_material_fallback_message, state)
            return surest_training_material_fallback_message

        vector_result_content = ""

        for res in vector_results:
            file_name, file_url, page_content, created_at, score = res
            vector_result_content += page_content + "\n\n"

        prompt = f"""
            Follow the given instructions and answer the question provided:
            Instructions:
            1. If the context has semantic meaning related to the question, then only answer the question. Otherwise, return the message: {surest_training_material_fallback_message}."
            2. Do not assume anything, do not hallucinate, and do not make up any information.
            3. All numeric information (numbers, statistics, percentages, dates, dollar amounts, etc.) MUST be taken directly from the context without any modification. DO NOT hallucinate or estimate any numerical values that aren't explicitly stated in the context.
            4. Based on the context, generate exactly 3 follow-up questions. Ensure these questions are semantically related to the context and are short, precise, and relevant. Include Surest in each question.
            5. Make sure that all follow-up questions MUST be directly answerable using ONLY the provided context - do not suggest questions that would require additional information.
            6. Respond in a JSON format with the following structure:
            {{
                "answer": [
                    {{
                        type: "percentage" or "price",
                        "title": "Clear Title for the Price or Percentage",
                        "description": "Detailed description of the price or percentage using only information from the context"
                    }},
                    ...
                ],
                "follow_up_questions": ["Question 1", "Question 2", "Question 3"]
            }}
            
            Context: {vector_result_content}
            Question: {surest_question}
            Answer:
            """
        response = await json_model.ainvoke(prompt)
        json_response = json.loads(response.content)
        answer = json_response.get("answer", [])
        follow_up_questions = json_response.get("follow_up_questions", [])
        if not answer:
            await log_surest_qna_data(surest_question, vector_result_content, surest_training_material_fallback_message, state)
            return {
                "answer": surest_training_material_fallback_message,
                "follow_up_questions": []
            }
            
        # Log the Q&A data
        await log_surest_qna_data(surest_question, vector_result_content, answer, state)
        
        return {
            "answer": {
                "client": "surest",
                "response_type": "surest_training_material_price_or_percentage_qa",
                "price_or_percentage_list": answer
            },
            "follow_up_questions": follow_up_questions,
        }
    except Exception as e:
        await pg_connection.rollback()
        traceback.print_exc()
        await log_tool_error(state, traceback.print_exc(), "surest_training_material_price_or_percentage_qa")
        raise Exception (traceback.format_exc())
    finally:
        await pg_connection.commit()
        await cursor.close()
        await return_pg_connection(pg_connection)