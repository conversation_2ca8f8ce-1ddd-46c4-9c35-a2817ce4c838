import httpx
import os
from client_plugins.samx_one.samx_one_utils.utils import get_quotes_url

class SamxOneService:
    """
    Enhanced service class for SAMx One operations.
    Centralizes common state extractions and API calls to reduce code duplication.
    """
    
    def __init__(self, state: dict):
        """
        Initialize the service with state information.
        
        Args:
            state: The conversation state containing environment and other context
        """
        self.state = state
        self.env = state.get("additional_arg", {}).get("env", "")
        self.quote_id = state.get("additional_arg", {}).get("quote_id", "")
        self.page_validations = state.get("additional_arg", {}).get("page_validations", {})
        self.current_page = state.get("additional_arg", {}).get("current_page", "")
    
    def get_env(self):
        """Get the environment from state."""
        return self.env
    
    def has_quote_id(self):
        """Check if quote ID is available."""
        return bool(self.quote_id)
    
    def get_quote_id(self):
        """Get the quote ID from state."""
        return self.quote_id
    
    def get_page_validations(self):
        """Get the page validations from state."""
        return self.page_validations
    
    def get_current_page(self):
        """Get the current page from state."""
        return self.current_page
    
    def get_fallback_message(self):
        """Get the standard fallback message when no quote ID is available."""
        return "Please view the case or start a new quote in order to get the response."
    
    async def get_quote(self, query: dict = None, projection: dict = None):
        """
        Get quote using query and projection.
        If no query provided, uses quote_id from state.
        """
        if not query and self.quote_id:
            query = {"quoteId": self.quote_id}
        elif not query:
            return None
        
        projection = projection or {}
        
        url = get_quotes_url(self.env)
        request_payload = {
            "searchQuery": query,
            "projectQuery": projection
        }
        
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
            response = await client.post(
                url + '/lingo/get/quote',
                json=request_payload,
                headers={"Content-Type": "application/json"}
            )
            if response.status_code == 200:
                try:
                    quote = response.json()
                    return quote.get('result', None)
                except ValueError:
                    raise ValueError("Invalid JSON response from SAMX One service")
            else:
                raise ValueError(f"Failed to fetch quote")
    
    async def get_quote_by_id(self, quote_id: str = None, projection: dict = None):
        """Get quote by quote ID."""
        if not quote_id:
            quote_id = self.quote_id
        if not quote_id:
            return None
        return await self.get_quote({"quoteId": quote_id}, projection)
    
    async def get_quote_by_case_id(self, case_id: int, projection: dict = None):
        """Get quote by case ID."""
        return await self.get_quote({"caseId": case_id}, projection)
    
    async def update_quote(self, update_data: dict, quote_id: str = None):
        """Update quote with provided data."""
        if not quote_id:
            quote_id = self.quote_id
        if not quote_id:
            raise ValueError("No quote ID available for update")
            
        url = get_quotes_url(self.env)
        request_payload = {
            "quoteId": quote_id,
            "updateData": update_data
        }
        
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
            response = await client.put(
                url + '/lingo/update/quote',
                json=request_payload,
                headers={"Content-Type": "application/json"}
            )
            if response.status_code == 200:
                try:
                    return response.json()
                except ValueError:
                    raise ValueError("Invalid JSON response from SAMX One service")
            else:
                raise ValueError(f"Failed to update quote")
    
    async def create_quote(self, quote_data: dict):
        """Create a new quote."""
        url = get_quotes_url(self.env)
        request_payload = {
            "quoteData": quote_data
        }
        
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
            response = await client.post(
                url + '/lingo/create/quote',
                json=request_payload,
                headers={"Content-Type": "application/json"}
            )
            if response.status_code == 200:
                try:
                    return response.json()
                except ValueError:
                    raise ValueError("Invalid JSON response from SAMX One service")
            else:
                raise ValueError(f"Failed to create quote")
    
    async def generate_case_id(self):
        """Generate a new case ID."""
        url = get_quotes_url(self.env)
        
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
            response = await client.get(
                url + '/lingo/caseId/create',
                headers={"Content-Type": "application/json"}
            )
            if response.status_code == 200:
                try:
                    return response.json()
                except ValueError:
                    raise ValueError("Invalid JSON response from common api service")
            else:
                raise ValueError(f"Failed to generate case ID")