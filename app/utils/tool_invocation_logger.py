from services.event_logger_service import logger
import functools
import inspect

class ToolInvocationLogger:
    @staticmethod
    async def log_tool_invocation(
        tool_name,
        state,
        tool_args=None,
        tool_output=None,
        extra_details=None,
        status_code=200,
        message="Tool executed",
        url=None,
        feedback=None
    ):
        user_info = state.get("user_info", {}) if state else {}
        event_details = {
            "tool_name": tool_name,
            "input": tool_args or {},
            "output": tool_output,
        }
        if extra_details:
            event_details.update(extra_details)
        await logger.info(
            user_info.get("uuid"),
            user_info.get("user_name"),
            user_info.get("session_id"),
            user_info.get("request_id"),
            user_info.get("client_id"),
            "tool",  # event_type
            tool_name,  # event_name
            tool_args,
            tool_output,
            status_code,
            message,
            url,
            feedback,
            event_details
        )

    @staticmethod
    def log_tool_invocation_decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            tool_name = func.__name__
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
 
            arguments = bound_args.arguments
            state = arguments.get("state")
            tool_args = {k: v for k, v in arguments.items() if k != "state"}
 
            await ToolInvocationLogger.log_tool_invocation(
                tool_name=tool_name, state=state, tool_args=tool_args, message="Tool invoked"
            )
 
            tool_output = await func(*args, **kwargs)
 
            await ToolInvocationLogger.log_tool_invocation(
                tool_name=tool_name,
                state=state,
                tool_args=tool_args,
                tool_output=tool_output,
                message="Tool executed",
            )
            return tool_output
 
        return wrapper
 