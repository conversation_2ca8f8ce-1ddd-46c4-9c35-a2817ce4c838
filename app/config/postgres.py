from psycopg_pool import AsyncConnectionPool
import os
import traceback
from services import log_error_to_db_service
from utils.helpers.constants import OEC_DEFAULT_CLIENT_ID
from urllib.parse import quote  

pg_db_name = os.getenv("POSTGRES_DATABASE_NAME")
pg_user = os.getenv("POSTGRES_USER")
pg_password = os.getenv("POSTGRES_PASSWORD")
pg_host = os.getenv("POSTGRES_HOST")

if pg_password:
    pg_password = quote(pg_password)  # URL encode the password to handle special characters

auto_commit_pool = None
connection_pool = None

async def init_auto_commit_pool() -> None:
    global auto_commit_pool
    
    if auto_commit_pool is None:
        try:
            auto_commit_pool = AsyncConnectionPool(
                max_size=280,
                max_idle=14400,
                max_lifetime=28800,
                conninfo=f"postgresql://{pg_user}:{pg_password}@{pg_host}/{pg_db_name}",
                kwargs={"autocommit": True, "prepare_threshold": 0}
            )
            await auto_commit_pool.open()
        except Exception as e:
            await log_error_to_db_service.log_error_to_db(traceback.format_exc(), OEC_DEFAULT_CLIENT_ID, None, None, "Postgres connection error for auto_commit_pool")
            traceback.print_exc()

async def init_connection_pool() -> None:
    global connection_pool
    
    if connection_pool is None:
        try:
           connection_pool = AsyncConnectionPool(
                max_size=20,
                max_idle=14400,
                max_lifetime=28800,
                conninfo=f"postgresql://{pg_user}:{pg_password}@{pg_host}/{pg_db_name}",
            )
           await connection_pool.open()
        except Exception as e:
            await log_error_to_db_service.log_error_to_db(traceback.format_exc(), OEC_DEFAULT_CLIENT_ID, None, None, "Postgres connection error for connection_pool")
            traceback.print_exc()

async def get_pg_connection() -> AsyncConnectionPool:
    if connection_pool is None:
        await log_error_to_db_service.log_error_to_db("Connection pool is not initialized.", OEC_DEFAULT_CLIENT_ID, None, None, "Postgres connection error for connection_pool")
        raise ValueError("Connection pool is not initialized.")
    return await connection_pool.getconn()

async def return_pg_connection(connection) -> None:
    if connection:
        await connection_pool.putconn(connection)

async def close_connection_pools() -> None:
    if connection_pool:
        await connection_pool.close()
    if auto_commit_pool:
        await auto_commit_pool.close()
