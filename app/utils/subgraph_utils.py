from langchain_core.messages import ToolMessage
from typing import Callable

def create_entry_message(assistant_name: str, new_dialog_state: str) -> Callable:
        async def entry_node(state) -> dict:
            tool_call_id = state["messages"][-1].tool_calls[0]["id"]
            return {
                "messages": ToolMessage(
                        content=f"The assistant is now the {assistant_name}."
                        " If the user changes their mind or needs help for other tasks, call the CompleteOrEscalate function to let the primary host assistant take control."
                        "If user is asking for multiple intents where any of the intent is not in your capability then strictly 'CompleteOrEscalate' the dialog to the host assistant without processing any intent."
                        " Do not mention who you are - just act as the proxy for the assistant.",
                        tool_call_id=tool_call_id,
            ),
                "dialog_state": new_dialog_state
            }
        return entry_node