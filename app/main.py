from dotenv import load_dotenv
load_dotenv()

from fastapi import Fast<PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from routes import api, member_enroll_api, gco, bne_api, rag_comparison_api
from fastapi.exceptions import RequestValidationError  
from fastapi.responses import JSONResponse  
from starlette.middleware.base import BaseHTTPMiddleware
from utils.api_security_utils import authorize_request
from services.event_logger_service import logger
from contextlib import asynccontextmanager
from config.langfuse import httpx_client, initialize_handler
from config.postgres import init_auto_commit_pool, init_connection_pool, close_connection_pools
from graph.state import get_postgres_checkpointer
from graph.graph import init_graph
from subgraph.initialize_subgraphs import initialize_subgraphs_on_startup
from graph.auth_nodes.helpers import initialize_auth_maps
from copilotkit.integrations.fastapi import add_fastapi_endpoint
from copilotkit import CopilotKitRemoteEndpoint, LangGraphAgent
from utils.subgraph_registry import SubgraphRegistry
from utils.helpers.constants import OEC_BNEMOBILE_CLIENT_ID, OEC_DEFAULT_CLIENT_ID
import traceback
from client_plugins.samx_one.samxone_api import router as samxOneRemoteEndpoint
from services.log_error_to_db_service import log_error_to_db
from utils.copilotkit_utils import add_copilotkit_endpoint

EXCLUDED_ENDPOINTS = ["/", "/health", "/userAuth", "/OhidLogin", "/generateLingoToken", "/agenticOfflineEvaluation", "/fetchAllowedOrigin", "/docs", "/openapi.json", "/refreshLingoToken","/docExtract","/docExtractionCallback","/bne-training-material","/getEnvVariables", "/generateResponseStargate"]

class AuthorizationMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        if (
            request.url.path not in EXCLUDED_ENDPOINTS
            and not request.url.path.startswith("/copilotkit") # Exclude CopilotKit endpoints from authorization
        ):
            try:
                authorization = request.headers.get("Authorization")
                if not authorization:
                    raise HTTPException(status_code=401, detail="Unauthorized: Missing Authorization header")
                client_type = request.headers.get("Client-Type")
                await authorize_request(authorization=authorization, request=request,client_type=client_type)
            except HTTPException as e:
                return JSONResponse(status_code=e.status_code, content={"status": "error", "message": e.detail})

        response = await call_next(request)
        return response

@asynccontextmanager
async def lifespan(app: FastAPI):
    try:
        await initialize_handler()
        await initialize_auth_maps()
        await init_auto_commit_pool()
        await init_connection_pool()
        await get_postgres_checkpointer()
        await initialize_subgraphs_on_startup()
        await init_graph() #INITIALIZATION OF THE LEGACY GRAPH, NOT IN USE
        # We create the `CopilotKitRemoteEndpoint` in the lifespan context manager
        # to ensure the subgraphs are initialized before the endpoint is registered.
        add_copilotkit_endpoint(app)
        yield
        httpx_client.close()
        await close_connection_pools()
    except Exception as e:
        await close_connection_pools()
        await log_error_to_db(
            error=f"Error during FastAPI startup: {str(e)}\n{traceback.format_exc()}",
            client_id=OEC_DEFAULT_CLIENT_ID,
            session_id=None,
            user_name=None,
            error_type="startup_error"
        )


app = FastAPI(title="LingoOEC FastAPI", version="0.1.0",lifespan=lifespan)


@app.exception_handler(RequestValidationError)   
async def validation_exception_handler(request: Request, exc: RequestValidationError):  
    try:
        req = await request.json()
        uuid = req.get("uuid", None)
        user_name = req.get("user_name", None)
        client_id = req.get("client_id", None)
        session_id = req.get("session_id", None)
        request_id = req.get("request_id", None)
        await logger.error(uuid, user_name, session_id, request_id, client_id, "api", "validation_error", req, None, 422, str(exc.errors()), str(request.url))
    except Exception as e:
        try:
            form = await request.form()
            req = {field: value for field, value in form.items()}  
            uuid = req.get("uuid", None)
            user_name = req.get("user_name", None) 
            client_id = req.get("client_id", None)
            session_id = req.get("session_id", None)
            request_id = req.get("request_id", None)
            await logger.error(uuid, user_name, session_id, request_id, client_id, "api", "validation_error", req, None, 422, str(exc.errors()), str(request.url))
        except Exception as e:
            await logger.error(None, None, None, None, None, "api", "validation_error", None, None, 422, str(exc.errors()), str(request.url))
    finally:
        return JSONResponse(          
        status_code=422,          
        content={    
                "status": "error",   
                "response": str(exc),                      
            }  
        )

app.add_middleware(AuthorizationMiddleware)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(api.router)
app.include_router(bne_api.router)
app.include_router(member_enroll_api.router)
app.include_router(gco.router)
app.include_router(rag_comparison_api.router)
app.include_router(samxOneRemoteEndpoint)

@app.get("/")
async def root():
    return {"message": "LingoOEC FastAPI is Up & Running"}

@app.get("/health")
async def health():
    return {"200 OK"}
