apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: lingo-api-cert
spec:
  secretName: lingo-api-ingress-secret
  duration: 2160h # 90d
  renewBefore: 360h # 15d
  subject:
    organizationalUnits:
      - Optum Technology
  privateKey:
    encoding: PKCS8
    rotationPolicy: Always
  commonName: "lingoai-api.optum.com" # e.g. test.optum.com
  dnsNames:
    - "lingoai-api.optum.com" # e.g. test-ctc.optum.com
  issuerRef:
    name: optum-app
    kind: ClusterIssuer