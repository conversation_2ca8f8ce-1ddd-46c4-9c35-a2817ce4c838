apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: magnus-oec-api-demo-cert
spec:
  secretName: magnus-oec-api-demo-ingress-secret
  duration: 2160h # 90d
  renewBefore: 360h # 15d
  subject:
    organizationalUnits:
      - Optum Technology
  privateKey:
    encoding: PKCS8
    rotationPolicy: Always
  commonName: "magnus-oec-api-demo.hcck8s-ctc-np101.optum.com" # e.g. test.optum.com
  dnsNames:
    - "magnus-oec-api-demo.hcck8s-ctc-np101.optum.com" # e.g. test-ctc.optum.com
  issuerRef:
    name: optum-app
    kind: ClusterIssuer