from graph.nodes import primary_assistant_tool_node, plan_recommendation_tool_node, cirrus_assistant_tool_node, data_converter_assistant_tool_node
from graph.chains import primary_assistant_runnable, plan_recommendation_runnable, cirrus_assistant_runnable, data_converter_assistant_runnable
from graph.routers import route_primary_assistant, route_plan_recommendation_assistant, route_cirrus_assistant, route_data_converter_assistant,route_escalation_fallback
from client_plugins.samx_one.samx_one.main import samx_one_runnable, samx_one_tool_node
from client_plugins.surest.surest.main import surest_runnable, surest_tool_node
from client_plugins.bneportal.portal_form_enrollment.main import portal_form_enrollment_runnable, portal_form_enrollment_tool_node
from client_plugins.bneportal.nonauth_documents_search.main import nonauth_documents_search_runnable, nonauth_documents_search_tool_node
from client_plugins.surest.surest.router import route_surest_assistant
from graph.auth_nodes.auth_routers import route_to_workflow
from client_plugins.samx_one.samx_one.router import route_samx_one_assistant
from client_plugins.bneportal.portal_form_enrollment.router import route_portal_form_enrollment_assistant
from client_plugins.bneportal.nonauth_documents_search.router import route_nonauth_documents_search_assistant

START = "start"
END = "end"
IS_CONDITIONAL = "is_conditional"

assistants_registry = [
    ("primary", primary_assistant_runnable, primary_assistant_tool_node, False, False),
    ("plan_recommendation", plan_recommendation_runnable, plan_recommendation_tool_node, False, True),
    ("cirrus", cirrus_assistant_runnable, cirrus_assistant_tool_node, True, True),
    ("data_converter", data_converter_assistant_runnable, data_converter_assistant_tool_node, False, True),
    ("surest", surest_runnable, surest_tool_node, False, True),
    ("samxone", samx_one_runnable, samx_one_tool_node, False, True),
    ("portal_form_enrollment", portal_form_enrollment_runnable, portal_form_enrollment_tool_node, False, True),
    ("nonauth_documents_search", nonauth_documents_search_runnable, nonauth_documents_search_tool_node, False, True),
]

edges_registry = [
    
    {START: "authorize_and_route_to_workflow", END: route_to_workflow, IS_CONDITIONAL: True},
        
    {START: "primary_assistant", END: route_primary_assistant, IS_CONDITIONAL: True},
    
    {START: "enter_plan_recommendation_assistant", END: "plan_recommendation_assistant", IS_CONDITIONAL: False},
    {START: "plan_recommendation_assistant", END: route_plan_recommendation_assistant, IS_CONDITIONAL: True},

    {START: "enter_cirrus_assistant", END: "cirrus_assistant", IS_CONDITIONAL: False},
    {START: "cirrus_assistant", END: route_cirrus_assistant, IS_CONDITIONAL: True},
    {START: "call_cirrus_tool_with_assistant", END: "cirrus_assistant", IS_CONDITIONAL: False},

    {START: "enter_data_converter_assistant", END: "data_converter_assistant", IS_CONDITIONAL: False},
    {START: "data_converter_assistant", END: route_data_converter_assistant, IS_CONDITIONAL: True},

    {START: "enter_surest_assistant", END: "surest_assistant", IS_CONDITIONAL: False},
    {START: "surest_assistant", END: route_surest_assistant, IS_CONDITIONAL: True},

    {START: "enter_samxone_assistant", END: "samxone_assistant", IS_CONDITIONAL: False},
    {START: "samxone_assistant", END: route_samx_one_assistant, IS_CONDITIONAL: True},

    {START: "enter_portal_form_enrollment_assistant", END: "portal_form_enrollment_assistant", IS_CONDITIONAL: False},
    {START: "portal_form_enrollment_assistant", END: route_portal_form_enrollment_assistant, IS_CONDITIONAL: True},

    {START: "enter_nonauth_documents_search_assistant", END: "nonauth_documents_search_assistant", IS_CONDITIONAL: False},
    {START: "nonauth_documents_search_assistant", END: route_nonauth_documents_search_assistant, IS_CONDITIONAL: True},

    {START: "invalid_tool", END: "primary_assistant", IS_CONDITIONAL: False},
    {START: "escalation_fallback", END: route_escalation_fallback, IS_CONDITIONAL: True},
    {START: "leave_skill", END: "primary_assistant", IS_CONDITIONAL: False},
    {START: "restrict_parallel_agents_run", END: "primary_assistant", IS_CONDITIONAL: False},
]