"""
RAG Comparison API Endpoints

Provides REST API endpoints for comparing PostgreSQL and Azure AI Search RAG performance.
"""

from fastapi import APIRouter, HTTPException, Query, Body
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
import sys
import os

# Add the app directory to the Python path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from services.rag_comparison_service import (
    rag_comparison_service,
    compare_rag_providers,
    run_batch_comparison,
    ComparisonResult,
    PerformanceMetrics,
    SearchResult
)

router = APIRouter(prefix="/rag-comparison", tags=["RAG Comparison"])

class ComparisonRequest(BaseModel):
    """Request model for single comparison"""
    query: str = Field(..., description="Search query to compare")
    top_k: int = Field(default=5, ge=1, le=20, description="Number of results to return")
    include_quality_analysis: bool = Field(default=True, description="Include quality metrics")

class BatchComparisonRequest(BaseModel):
    """Request model for batch comparison"""
    queries: List[str] = Field(..., description="List of search queries to compare")
    top_k: int = Field(default=5, ge=1, le=20, description="Number of results to return per query")

class ComparisonResponse(BaseModel):
    """Response model for comparison results"""
    comparison_id: str
    query: str
    timestamp: str
    postgresql_results: List[Dict[str, Any]]
    azure_results: List[Dict[str, Any]]
    postgresql_metrics: Dict[str, Any]
    azure_metrics: Dict[str, Any]
    quality_metrics: Dict[str, Any]

class PerformanceSummaryResponse(BaseModel):
    """Response model for performance summary"""
    total_comparisons: int
    postgresql: Dict[str, Any]
    azure_ai_search: Dict[str, Any]

@router.post("/compare", response_model=ComparisonResponse)
async def compare_search_providers(request: ComparisonRequest):
    """
    Compare search results between PostgreSQL and Azure AI Search for a single query
    """
    try:
        result = await rag_comparison_service.compare_search_providers(
            query=request.query,
            top_k=request.top_k,
            include_quality_analysis=request.include_quality_analysis
        )
        
        # Convert dataclass to dict for JSON response
        return ComparisonResponse(
            comparison_id=result.comparison_id,
            query=result.query,
            timestamp=result.timestamp,
            postgresql_results=[{
                "file_name": r.file_name,
                "file_url": r.file_url,
                "page_content": r.page_content[:500] + "..." if len(r.page_content) > 500 else r.page_content,
                "score": r.score,
                "created_at": r.created_at,
                "provider": r.provider,
                "metadata": r.metadata
            } for r in result.postgresql_results],
            azure_results=[{
                "file_name": r.file_name,
                "file_url": r.file_url,
                "page_content": r.page_content[:500] + "..." if len(r.page_content) > 500 else r.page_content,
                "score": r.score,
                "created_at": r.created_at,
                "provider": r.provider,
                "metadata": r.metadata
            } for r in result.azure_results],
            postgresql_metrics={
                "provider": result.postgresql_metrics.provider,
                "embedding_time_ms": result.postgresql_metrics.embedding_time_ms,
                "search_time_ms": result.postgresql_metrics.search_time_ms,
                "total_time_ms": result.postgresql_metrics.total_time_ms,
                "results_count": result.postgresql_metrics.results_count,
                "error": result.postgresql_metrics.error
            },
            azure_metrics={
                "provider": result.azure_metrics.provider,
                "embedding_time_ms": result.azure_metrics.embedding_time_ms,
                "search_time_ms": result.azure_metrics.search_time_ms,
                "total_time_ms": result.azure_metrics.total_time_ms,
                "results_count": result.azure_metrics.results_count,
                "error": result.azure_metrics.error
            },
            quality_metrics=result.quality_metrics
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Comparison failed: {str(e)}")

@router.post("/batch-compare")
async def batch_compare_search_providers(request: BatchComparisonRequest):
    """
    Compare search results for multiple queries
    """
    try:
        results = await run_batch_comparison(request.queries, request.top_k)
        
        # Convert results to response format
        response_data = []
        for result in results:
            response_data.append({
                "comparison_id": result.comparison_id,
                "query": result.query,
                "timestamp": result.timestamp,
                "postgresql_metrics": {
                    "total_time_ms": result.postgresql_metrics.total_time_ms,
                    "results_count": result.postgresql_metrics.results_count,
                    "error": result.postgresql_metrics.error
                },
                "azure_metrics": {
                    "total_time_ms": result.azure_metrics.total_time_ms,
                    "results_count": result.azure_metrics.results_count,
                    "error": result.azure_metrics.error
                },
                "quality_metrics": result.quality_metrics
            })
        
        return {
            "total_queries": len(request.queries),
            "results": response_data,
            "summary": rag_comparison_service.get_performance_summary()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Batch comparison failed: {str(e)}")

@router.get("/performance-summary", response_model=PerformanceSummaryResponse)
async def get_performance_summary(limit: int = Query(default=10, ge=1, le=100)):
    """
    Get performance summary from recent comparisons
    """
    try:
        summary = rag_comparison_service.get_performance_summary(limit)
        return PerformanceSummaryResponse(**summary)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get performance summary: {str(e)}")

@router.get("/export-data")
async def export_comparison_data(format: str = Query(default="json", regex="^(json)$")):
    """
    Export comparison data for analysis
    """
    try:
        data = rag_comparison_service.export_comparison_data(format)
        return {"format": format, "data": data}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to export data: {str(e)}")

@router.get("/test-queries")
async def get_test_queries():
    """
    Get predefined test queries for comparison testing
    """
    test_queries = [
        "What are the benefits of Surest health plans?",
        "How does Surest compare to traditional health insurance?",
        "What is the cost structure of Surest plans?",
        "How do I enroll in a Surest plan?",
        "What providers are covered under Surest?",
        "What are the deductibles for Surest plans?",
        "How does Surest handle prescription drug coverage?",
        "What preventive care is covered by Surest?",
        "How do I file a claim with Surest?",
        "What is the difference between Surest and HSA plans?"
    ]
    
    return {
        "test_queries": test_queries,
        "description": "Predefined queries for testing RAG comparison functionality"
    }

@router.post("/quick-test")
async def quick_comparison_test():
    """
    Run a quick comparison test with predefined queries
    """
    try:
        test_queries = [
            "What are the benefits of Surest health plans?",
            "How does Surest compare to traditional health insurance?",
            "What is the cost structure of Surest plans?"
        ]
        
        results = await run_batch_comparison(test_queries, top_k=3)
        
        # Create simplified summary
        summary = {
            "test_completed": True,
            "queries_tested": len(test_queries),
            "results": []
        }
        
        for result in results:
            summary["results"].append({
                "query": result.query,
                "postgresql_time_ms": result.postgresql_metrics.total_time_ms,
                "azure_time_ms": result.azure_metrics.total_time_ms,
                "postgresql_results": len(result.postgresql_results),
                "azure_results": len(result.azure_results),
                "overlap_score": result.quality_metrics.get("result_overlap", {}).get("jaccard_similarity", 0)
            })
        
        return summary
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Quick test failed: {str(e)}")

@router.delete("/clear-history")
async def clear_comparison_history():
    """
    Clear comparison history (useful for testing)
    """
    try:
        rag_comparison_service.comparison_history.clear()
        return {"message": "Comparison history cleared successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to clear history: {str(e)}")

@router.get("/health")
async def health_check():
    """
    Health check endpoint for the comparison service
    """
    return {
        "status": "healthy",
        "service": "RAG Comparison API",
        "comparisons_in_history": len(rag_comparison_service.comparison_history)
    }
