"""
Service for extracting user information from state object.
This can be used across different tools to standardize user info extraction.
"""

def extract_user_info_from_state(state):
    """
    Extracts user information from the state object.
    
    Args:
        state (dict): The state object containing user information
        
    Returns:
        dict: A dictionary containing extracted user information
    """
    user_info = state.get("user_info", {})
    
    return {
        "uuid": user_info.get("uuid"),
        "user_name": user_info.get("user_name"),
        "client_id": user_info.get("client_id"),
        "session_id": user_info.get("session_id"),
        "request_id": user_info.get("request_id")
    }