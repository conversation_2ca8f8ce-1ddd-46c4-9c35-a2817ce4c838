import jwt
import os
import time

def get_token():
    jwt_token = os.getenv("JWT_TOKEN")
    if (jwt_token == None):
        return generate_new_token()
    jwt_token_issue_time = os.getenv("JWT_TOKEN_ISSUE_TIME")
    if (jwt_token_issue_time == None) or (int(time.time()) >= int(jwt_token_issue_time) + 3590):
        return generate_new_token()
    return jwt_token

def generate_new_token():
    jwt_key = os.getenv("JWT_KEY")
    jwt_secret = os.getenv("JWT_SECRET")

    issue_time = int(time.time())
    expiry_time = issue_time + 3600

    payload = {
        "iss": jwt_key,
        "exp": expiry_time
    }
    
    jwt_token = jwt.encode(payload, jwt_secret, algorithm="HS256")
    os.environ["JWT_TOKEN"] = jwt_token
    os.environ["JWT_TOKEN_ISSUE_TIME"] = str(issue_time)
    return jwt_token
