from pydantic import BaseModel

class ToGCOReportingAssistant(BaseModel):
    """Transfers work to a specialized assistant to handle queries related to "Group published reports" or "Download Recent Reports" for these types of queries, "Show the Reports for a group <GROUP_NAME>", "<GROUP_NAME>/<REPORT_TYPE>", "download or view reports for specific groups, report types or dates", "use tool show me new published reports", "use tool show me top 5 downloaded reports. instead of top 5 there can be any number", "use tool show me recent downloaded reports", "Please show the list of available groups", "show me <GROUP_NAME>" and similar queries. If user ask about submit ticket or submit feedback, then it will be handled by the GCO Reporting Assistant.
    This agent can **ONLY answer report related questions to these **REPORTS: 'Utilization Report', 'Employer Change Report', 'Facility RandC REPORTS FOR ERNST & YOUNG Summary', 'Facility RandC REPORTS FOR ERNST & YOUNG Detail', 'Member Detail Report Prior YR', 'Member Detail Report', 'Percent of Savings Bank', 'Medical Copay Coupon Summary', 'Naviguard Report Detail', 'Medical Copay Coupon Detail', 'Large Loss with Diagnosis (statistical)', 'Facility R&C Report Detail', 'Facility R&C Report Summary', 'Percent of Savings Billed', 'Shared Savings Report Detail', 'Naviguard Report Summary', 'Shared Savings Report Summary', 'Key Accounts Executive Affordability Scorecard', 'Show Published Report/Published Report', 'Download a Report/Download Report', 'Downloaded Report/Downloaded Reports'.
    (Do not include report names/types to the user in the follow-ups.)
    If user ask about submit ticket or submit feedback or feedback form or form or first time user or reporting guide or reporting user guide or reporting instructions or reporting support, then it will be handled by the GCO Reporting Assistant.
    Also if user queries have keywords related to CER like 
    ["PVC", "PVCI", "MBM","pvc vs claims", "pvc versus claims", "pvc and claims", "claims vs pvc", 
    "Claim Lag Study", "Claims Expenses by Size of Payment", "Cost and Utilization by Procedure",
    "Cost and Utilization Summary Report", "Detail Payment Report", "Distribution of Discounts", 
    "Distribution of Ineligible Charges", "Distribution of Other Savings", "Health Care Cost Management Summary", 
    "Inpatient Utilization And Cost by Admission Type", "Inpatient Utilization by Diagnosis", 
    "Key Generic Substitution Indicators by Month", "Large Loss Claim Payments (eCR Financial Report)", 
    "Managed Pharmacy Cost and Utilization by Month", "Managed Pharmacy Critical Indicators", 
    "Managed Pharmacy Utilization by Age Group", "Membership by Market", "Membership by Month", 
    "Membership with Demographic and Geographic Factor", "Network Utilization", "Outpatient Utilization by Diagnosis", 
    "Payments by Benefit Type", "Payments by Month", "Premium vs Claim - Summary", "Top Drug Utilization Ranked by Total Net Paid", 
    "Top Drug Utilization Ranked by Volume", "Top Hospitals Ranked-Total Net Paid", "Top Physicians Ranked-Total Net Paid",
    "Top Therapeutic Class Utilization Ranked by Total Net Paid", "Top Therapeutic Class Utilization Ranked by Volume",
    "Utilization And Cost by Provider Type", "Utilization by Age Group", "Utilization by Diagnosis"] then it will be handled by the GCO Reporting Assistant."""
