
from client_plugins.bneportal.service_tracker.tool import get_service_issues_dashboard

from graph.state import State
from graph.nodes import Assistant
from config.openAI import model
from langgraph.graph import END, StateGraph, START
from langgraph.prebuilt import tools_condition

from langchain_core.runnables import <PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>
from langgraph.prebuilt import Tool<PERSON>ode
from tools.common_tools import CompleteOrEscalate
from utils.helpers.constants import TOOL_CALL_ERROR_MESSAGE
from client_plugins.bneportal.service_tracker.prompts import service_tracker_system_prompt
# Import the other required modules


service_tracker_tools = [get_service_issues_dashboard,]  + [CompleteOrEscalate]
service_tracker_tool_node = ToolNode(service_tracker_tools, handle_tool_errors = TOOL_CALL_ERROR_MESSAGE)
service_tracker_runnable = service_tracker_system_prompt | model.bind_tools(service_tracker_tools)
