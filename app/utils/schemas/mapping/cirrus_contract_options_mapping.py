import datetime
from utils.helpers.mappings import PRODUCTS_MAPPING, REVENUE_TYPE_MAPPING

def is_date_in_range(start_date, end_date, date):
    if start_date and end_date:  
        start_date = datetime.datetime.strptime(start_date, "%Y-%m-%d").date()  
        end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d").date()  
        if (type(date) is str):
            date = datetime.datetime.strptime(date, "%Y-%m-%d").date()
        return start_date <= date <= end_date
    return False  
    
def get_nested_value(data, keys, default=None):  
    for key in keys:  
        if isinstance(data, dict):  
            data = data.get(key, default)  
        else:  
            return default  
    return data  

def map_contract_options_response(contract_options_data): 
    current_date = datetime.datetime.now().date()  
    
    product_plan_info_list = []  
    insuring_rule_info={}
    
    for contract in contract_options_data.get('memberGroup', {}).get('contractList', []):  
        contract_effective_date = get_nested_value(contract, ['effectiveDate'])  
        contract_expiration_date = get_nested_value(contract, ['expirationDate'])  
        
        if is_date_in_range(contract_effective_date, contract_expiration_date, current_date):  
            for contract_option in contract.get('contractOptionsList', []):  
                contract_option_effective_date = get_nested_value(contract_option, ['contractOptionEffectiveDate'])  
                contract_option_expiration_date = get_nested_value(contract_option, ['contractOptionExpirationDate'])  
                
                if is_date_in_range(contract_option_effective_date, contract_option_expiration_date, current_date):  
                    insuring_rule = contract_option.get('contractOptionInsuringRule', [])
                    if (len(insuring_rule)> 0):
                        rule_wait_period = insuring_rule[0].get('guardrailInsRuleWaitPeriod', [])
                        if (len(rule_wait_period)>0):
                            duration_type = get_nested_value(rule_wait_period[0], [ 'waitingPeriodDurationType'])
                            waiting_period_count = get_nested_value(rule_wait_period[0], [ 'waitingPeriodMonthCount'] if duration_type == 'Months' else ['waitingPeriodDaysCount']) 
                            insuring_rule_info = {  
                                "waitingPeriodDurationType": duration_type,
                                "waitingPeriodCount": waiting_period_count,
                                "waitingPeriodType": get_nested_value(rule_wait_period[0], [ 'waitingPeriodType'])
                            }                     
                    for coverage_option in contract_option.get('coverageOptionList', []):  
                        coverage_option_id = get_nested_value(coverage_option, ['coverageOptionID'], None)  
                        carrier_name = get_nested_value(coverage_option, ['carrierName'], None)  
                        
                        # Check for coverageOptionPlansList  
                        plans_list = coverage_option.get('coverageOptionPlansList', [])  
                        for plan in plans_list:  
                            plan_name = get_nested_value(plan, ['planName'], None)  
                            revenue_arrangement = get_nested_value(plan, ['revenueArrangement'], None)  
                            
                            product_plan_info = {  
                                "product": PRODUCTS_MAPPING.get(get_nested_value(contract_option, ['contractOptType']), None),
                                "planName": f"{coverage_option_id} - {plan_name}" if coverage_option_id and plan_name else None,
                                "coveragePeriod": f"{contract_option_effective_date} - {contract_option_expiration_date}" if contract_option_effective_date and contract_option_expiration_date else None,  
                                "carrierName": carrier_name,  
                                "revenueType": REVENUE_TYPE_MAPPING.get(revenue_arrangement, None),
                                "status": "Active" if get_nested_value(contract_option, ['contractOptStatus']) == "A" else get_nested_value(contract_option, ['contractOptStatus'])
                            }  
                            product_plan_info_list.append(product_plan_info)  
                        
                        # Check for coverageOptionFinProtectPlansList  
                        fin_protect_plans_list = coverage_option.get('coverageOptionFinProtectPlansList', [])  
                        for plan in fin_protect_plans_list:  
                            plan_name = get_nested_value(plan, ['finProtectionPlanName'], None)  
                            # revenue_arrangement = get_nested_value(plan, ['revenueArrangement'], None)  
                            
                            product_plan_info = {  
                                "product": PRODUCTS_MAPPING.get(get_nested_value(contract_option, ['contractOptType']), None),  
                                "planName": f"{coverage_option_id} - {plan_name}" if coverage_option_id and plan_name else None,
                                "coveragePeriod": f"{contract_option_effective_date} - {contract_option_expiration_date}" if contract_option_effective_date and contract_option_expiration_date else None,  
                                "carrierName": carrier_name,  
                                "revenueType": None,
                                "status": "Active" if get_nested_value(contract_option, ['contractOptStatus']) == "A" else get_nested_value(contract_option, ['contractOptStatus'])  
                            }  
                            product_plan_info_list.append(product_plan_info)   

    return {"productPlanInfoList": product_plan_info_list,"insuringRuleInfo": insuring_rule_info}

def map_plan_and_bill_group_lists(contract_options_data, effective_date):

    medical_plans_list = []
    vision_plans_list = []
    dental_plans_list = []
    member_life_plans_list = []
    dependent_life_plans_list = []
    short_term_disability_plans_list = []
    long_term_disability_plans_list = []
    bill_group_list = []

    for contract in contract_options_data.get('memberGroup', {}).get('contractList', []):  
        contract_effective_date = get_nested_value(contract, ['effectiveDate'])  
        contract_expiration_date = get_nested_value(contract, ['expirationDate'])  
        
        if is_date_in_range(contract_effective_date, contract_expiration_date, effective_date):  
            for contract_option in contract.get('contractOptionsList', []):
                contract_option_effective_date = get_nested_value(contract_option, ['contractOptionEffectiveDate'])  
                contract_option_expiration_date = get_nested_value(contract_option, ['contractOptionExpirationDate'])  

                if is_date_in_range(contract_option_effective_date, contract_option_expiration_date, effective_date):  
                    populations = (get_nested_value(contract_option, ['populationList']))
                    population_list = [get_nested_value(population, ['populationName']) for population in populations] if populations else []
                        
                    for coverage_option in contract_option.get('coverageOptionList', []):  
                        coverage_option_id = get_nested_value(coverage_option, ['coverageOptionID'], None)   
                            
                        # Check for coverageOptionPlansList  
                        plans_list = coverage_option.get('coverageOptionPlansList', [])  
                        for plan in plans_list:  
                            plan_name = get_nested_value(plan, ['planName'], None)

                            plan_info = {
                                "planName": plan_name,
                                "coverageOptionID": coverage_option_id,
                                "popList": population_list
                            }

                            if get_nested_value(contract_option, ['contractOptType']) == 'MD':
                                medical_plans_list.append(plan_info)
                            elif get_nested_value(contract_option, ['contractOptType']) == 'VS':
                                vision_plans_list.append(plan_info)
                            elif get_nested_value(contract_option, ['contractOptType']) == 'DN':
                                dental_plans_list.append(plan_info)

                        # Check for coverageOptionFinProtectPlansList  
                        fin_protect_plans_list = coverage_option.get('coverageOptionFinProtectPlansList', [])  
                        for plan in fin_protect_plans_list:  
                            plan_name = get_nested_value(plan, ['finProtectionPlanName'], None)  
                            
                            plan_info = {  
                                "planName": plan_name,
                                "coverageOptionID": coverage_option_id,
                                "popList": population_list
                            }

                            if get_nested_value(contract_option, ['contractOptType']) == 'LI':
                                if coverage_option_id[:2] == 'BL':
                                    member_life_plans_list.append(plan_info)
                                elif coverage_option_id[:4] == 'BDEP':
                                    dependent_life_plans_list.append(plan_info)
                            elif get_nested_value(contract_option, ['contractOptType']) == 'DS':
                                short_term_disability_plans_list.append(plan_info)
                            elif get_nested_value(contract_option, ['contractOptType']) == 'DL':
                                long_term_disability_plans_list.append(plan_info)

    bill_groups = (contract_options_data.get('memberGroup', {}).get('billGroupList', []))
    for bill_group in bill_groups:
        bill_group_info = {
            'billGroupReferenceID': get_nested_value(bill_group, ['billGroupReferenceID']),
            'billGroupDescription': get_nested_value(bill_group, ['billGroupDescription'])
        }
        bill_group_list.append(bill_group_info)

    return {"coverageDetails": {"medicalPlansList": medical_plans_list, "visionPlansList": vision_plans_list, "dentalPlansList": dental_plans_list, "memberLifePlansList": member_life_plans_list, "dependentLifePlansList": dependent_life_plans_list, "shortTermDisabilityPlansList": short_term_disability_plans_list, "longTermDisabilityPlansList": long_term_disability_plans_list, "billGroupList": bill_group_list}}
