import os
import importlib
from fastapi.responses import JSONResponse

async def trigger_agentic_offline_evaluation(query, session_id,  agent_name,user_name, application_name, additional_arg=None):
    # Dynamically construct the module path
    module_path = f"client_plugins.{application_name}.{agent_name}.test"

    # Verify the module exists by checking its corresponding file
    test_py_path = os.path.join(
        os.getcwd(), "client_plugins", application_name, agent_name, "test.py"
    )
    if not os.path.exists(test_py_path):
        return JSONResponse(
            content={
                "status": "error",
                "message": f"test.py file not found at {test_py_path} for agent_name={agent_name} and application_name={application_name}"
            },
            status_code=500
        )

    # Dynamically import the module and the run_agent function
    offline_evaluation_module = importlib.import_module(module_path)
    start_graph_func_name = f"start_{agent_name}_graph"
    start_graph_func = getattr(offline_evaluation_module, start_graph_func_name, None)
    run_agent = getattr(offline_evaluation_module, "run_agent", None)

    # Verify that the run_agent function exists
    if run_agent is None:
        return JSONResponse(
            content={
                "status": "error",
                "message": f"The 'run_agent()' function is not defined in {module_path}."
            },
            status_code=500
        )

    if start_graph_func is None:
        return JSONResponse(
            content={
                "status": "error",
                "message":f"The '{start_graph_func_name}()' function is not defined in {module_path}."
                },
            status_code=500
        )
    
    graph = await start_graph_func()
    
    # Execute the run_agent function
    raw_response = await run_agent(query, session_id, graph, user_name=user_name, additional_arg=additional_arg or {} )

    return raw_response