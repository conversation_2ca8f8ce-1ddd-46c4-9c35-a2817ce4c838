
from client_plugins.bneportal.nonauth_documents_search.tool import bne_training_document_search, bne_training_video_search, bne_training_tile_click, bne_get_sbc, bne_find_resources_tile_click, bne_get_employer_handbook, bne_get_member_handbook, bne_get_forms

from graph.state import State
from graph.nodes import Assistant
from config.openAI import model
from langgraph.graph import END, StateGraph, START
from langgraph.prebuilt import tools_condition

# from utils.tool_utils import handle_tool_error
from langchain_core.runnables import <PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>
from langgraph.prebuilt import ToolNode
from tools.common_tools import CompleteOrEscalate
from client_plugins.bneportal.nonauth_documents_search.prompts import nonauth_documents_search_system_prompt
from utils.helpers.constants import TOOL_CALL_ERROR_MESSAGE
# Import the other required modules
# def create_tool_node_with_fallback(tools: ToolNode) -> dict:
#     return tools.with_fallbacks(
#         [RunnableLambda(handle_tool_error)], exception_key="error"
#     )

nonauth_documents_search_tools = [bne_training_document_search, bne_training_video_search, bne_training_tile_click, bne_get_sbc, bne_find_resources_tile_click, bne_get_employer_handbook, bne_get_member_handbook, bne_get_forms]  + [CompleteOrEscalate]

nonauth_documents_search_tool_node = ToolNode(nonauth_documents_search_tools, handle_tool_errors = TOOL_CALL_ERROR_MESSAGE)
nonauth_documents_search_runnable = nonauth_documents_search_system_prompt | model.bind_tools(nonauth_documents_search_tools)
            