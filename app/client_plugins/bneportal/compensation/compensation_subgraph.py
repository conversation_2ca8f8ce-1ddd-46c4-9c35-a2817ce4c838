
from client_plugins.bneportal.compensation.main import compensation_tool_node
from client_plugins.bneportal.compensation.main import compensation_runnable
from client_plugins.bneportal.compensation.router import route_compensation_assistant
from graph.nodes import Assistant
from langgraph.pregel import RetryPolicy
from graph.nodes import pop_dialog_state, escalation_fallback, invalid_tool

from langgraph.graph import START
from graph.state import State, get_postgres_checkpointer
from utils.subgraph_builder import BaseSubgraphBuilder
from utils.subgraph_registry import SubgraphRegistry

compensation_compiled_graph = None

async def init_compensation_graph():
    """
    Initialize the compensation graph using the BaseSubgraphBuilder pattern
    and register it with SubgraphRegistry.
    """
    global compensation_compiled_graph
    if compensation_compiled_graph is None:
        # Create a new builder with fluent interface
        builder = BaseSubgraphBuilder(State)
        
        # Add nodes
        builder.add_node("compensation_assistant", Assistant(compensation_runnable), retry=RetryPolicy(max_attempts=2))
        builder.add_node("call_compensation_tool", compensation_tool_node)
        builder.add_node("leave_skill", pop_dialog_state)
        builder.add_node("escalation_fallback", escalation_fallback)
        builder.add_node("tool_disabled_handler", invalid_tool)
        
        # Add edges
        builder.add_edge("leave_skill", "compensation_assistant")
        builder.add_edge("escalation_fallback", "compensation_assistant")
        builder.add_edge(START, "compensation_assistant")
        
        # Add conditional edges
        builder.add_conditional_edges("compensation_assistant", route_compensation_assistant)
        
        # Compile with async checkpointer
        compensation_compiled_graph = builder.compile(checkpointer=await get_postgres_checkpointer())
        
        # Register with SubgraphRegistry for future access
        SubgraphRegistry.register("compensation", compensation_compiled_graph)
