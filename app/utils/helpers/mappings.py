PRODUCER_TYPE_MAPPING = {  
        "1": "Broker",  
        "2": "General_Agent",  
        "3": "Agent",  
        "4": "Sales_Rep"  
        }  

PRODUCTS_MAPPING= {
        "CS": "Contraceptive Services Only",
        "DN": "Dental",
        "LI": "Life",
        "MD": "Medical",
        "RX": "Pharmacy",
        "SP": "Spending Account",
        "VS": "Vision",
        "AD": "AD&D",
        "LD": "Life & AD&D",
        "DS": "Short Term Disability",
        "DL": "Long Term Disability",
        "SH": "Stop Loss",
        "SL": "Supplemental Life",
        "SA": "Supplemental AD"
    }

REVENUE_TYPE_MAPPING = {
        "01": "Fully Insured",
        "02": "MP",
        "03": "MMP",
        "04": "ASO",
        "05": "Level Funded",
        "06": "ASO With Stoploss",
        "07": "Retro Rated"
    }

MARKET_SEGMENT_MAPPING = {
    "AE": "ACEC Expanded 51-99", 
    "AK": "ACEC Key 100+",
    "AS": "ACEC Small 2-50",
    "ML": "Expanded Small",
    "IN": "Individual",
    "IC": "Individual Conversion",
    "IE": "Individual Exchange", 
    "KA": "Key Accounts", 
    "MS": "Med Supp Plans", 
    "NA": "National", 
    "PE": "PEO",
    "FE": "Public - Federal Employees", 
    "LT": "Public - Labor and Trust",
    "SE": "Public - State", 
    "PS": "Public Sector", 
    "SH": "SHOP Exchange", 
    "SM": "Small", 
    "PP" : "PPO Foreign Resident"
}

GENDER_MAPPING = {
        'male': {'m', 'male'},
        'female': {'f', 'female'},
        'non-binary': {'nb', 'non-binary'}
    }

CIRRUS_PRODUCT_MAPPING = {
    "medical": "MD",
    "dental": "DN",
    "vision": "VS",
    "std": "DS",
    "ltd": "DL",
    "life": "LI",
    "deplife": "LI",
    "suplife": "SL"
}

NO_SSN_REASON_MAPPING  = {
        'Expatriate': '001',
        'Newborn': '002',
        'Refusal to Provide': '003',
        'Unknown': '004',
        'Personal ID Number': '005',
        'Satisfied': 'SAT'
    }

QUALIFYING_EVENT_MAPPING = {
        "New Group Plan": "28",
        "New Hire": "56",
        "Special Enrollment": "45",
        "Re-enrollment": "41",
        "Rehire": "44",
        "Annual Open Enrolment": "45",
        "Late Enrollee": "46",
    }