import httpx, os
from config import jwt
# from install_utility.payload_construction import construct_payload
# from install_utility.payload_formatter import format_payload
from services.cirrus.install_member_service import log_heavy_weight_data, get_case_id_from_counter
from cirrus.cirrus_install_plugin.plugin_utility.payload_constructor import construct_payload
from cirrus.cirrus_install_plugin.plugin_utility.review_json_formatter import format_payload

def _payload_HW(json_recieved):  # to-remove this function when re-factoring
    json_recieved = format_payload(json_recieved)  # to-refactor when joining with agent
    
    mem_body = construct_payload(json_recieved) 
    
    return mem_body

# 1. we need to define try-except in tools as well
# 2. at the time of connection recheck the UI json for plan List handling, 
# 3. check the comments mentioned while connecting the code
async def hwCall(payload: dict, uuid: str, client_id: str):
    hw_resp = {}
    try:
        payload = _payload_HW(payload)
        case_id = await get_case_id_from_counter(client_id)
        payload["processID"] = f"LingoOEC-{case_id}"

        lingo_api_base_url = os.getenv("LINGO_OEC_BASE_URL")
        post_back_url = lingo_api_base_url + '/memberCallback/' + str(case_id) + "/" + uuid + "/" + client_id
        member_hw_url = os.getenv("CIRRUS_URL_BASE") + '/benefits/v6.0'
        token = jwt.get_token()
        headers = {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json',
            'POST_BACK_URL': post_back_url
        }

        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
            response = await client.post(member_hw_url, json=payload, headers=headers)
            status_code = response.status_code
            hw_resp = response.json()

            if status_code >= 400:
                error_message = hw_resp.get('errors', 'Unknown error occurred')
                raise Exception(error_message)

        if 'errors' in hw_resp:
            error_msg = str(hw_resp['errors'])
            raise Exception(error_msg)

        await log_heavy_weight_data(uuid, client_id, case_id,
            payload["memberList"][0]["memberAffiliation"]["memGroupID"] if payload["memberList"][0]["memberAffiliation"]["memGroupID"] else None,
            payload, hw_resp
        )

        return "Case has been successfully submitted to cirrus for processing."

    except httpx.HTTPStatusError as e:
        error_message = e.response.json()
        error_msg = error_message.get('errors', 'Unknown error occurred')
        raise Exception(error_msg)

    except Exception as e:
        raise Exception(f"An error occurred: {str(e)}")
