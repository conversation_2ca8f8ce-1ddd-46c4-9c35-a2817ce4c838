#!/usr/bin/env python3
"""
RAG Performance Testing Script

This script provides comprehensive testing and monitoring capabilities for comparing
PostgreSQL pgvector and Azure AI Search RAG performance.
"""

import asyncio
import sys
import os
import json
import time
import statistics
from datetime import datetime
from typing import List, Dict, Any
import argparse

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'app'))

from services.rag_comparison_service import (
    rag_comparison_service,
    run_batch_comparison,
    ComparisonResult
)

class RAGPerformanceTester:
    """Comprehensive RAG performance testing and monitoring"""
    
    def __init__(self):
        self.test_results = []
        self.test_queries = [
            "What are the benefits of Surest health plans?",
            "How does Surest compare to traditional health insurance?",
            "What is the cost structure of Surest plans?",
            "How do I enroll in a Surest plan?",
            "What providers are covered under Surest?",
            "What are the deductibles for Surest plans?",
            "How does Surest handle prescription drug coverage?",
            "What preventive care is covered by Surest?",
            "How do I file a claim with Surest?",
            "What is the difference between Surest and HSA plans?",
            "What are the out-of-pocket maximums for Surest?",
            "How does Surest handle emergency care?",
            "What mental health benefits does Surest offer?",
            "How do I find in-network providers for Surest?",
            "What is the appeals process for Surest claims?"
        ]
    
    async def run_performance_test(
        self,
        num_iterations: int = 3,
        top_k: int = 5,
        queries: List[str] = None
    ) -> Dict[str, Any]:
        """
        Run comprehensive performance test
        
        Args:
            num_iterations: Number of times to run each query
            top_k: Number of results to return
            queries: Custom queries to test (uses default if None)
            
        Returns:
            Detailed performance analysis
        """
        test_queries = queries or self.test_queries
        print(f"Starting performance test with {len(test_queries)} queries, {num_iterations} iterations each")
        
        all_results = []
        
        for iteration in range(num_iterations):
            print(f"\nIteration {iteration + 1}/{num_iterations}")
            
            iteration_start = time.time()
            results = await run_batch_comparison(test_queries, top_k)
            iteration_time = time.time() - iteration_start
            
            print(f"Iteration {iteration + 1} completed in {iteration_time:.2f} seconds")
            all_results.extend(results)
        
        # Analyze results
        analysis = self._analyze_performance_results(all_results, num_iterations)
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"rag_performance_test_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(analysis, f, indent=2, default=str)
        
        print(f"\nResults saved to: {filename}")
        return analysis
    
    def _analyze_performance_results(
        self,
        results: List[ComparisonResult],
        num_iterations: int
    ) -> Dict[str, Any]:
        """Analyze performance test results"""
        
        # Separate metrics by provider
        pg_times = []
        azure_times = []
        pg_errors = 0
        azure_errors = 0
        
        quality_scores = []
        
        for result in results:
            # Performance metrics
            if result.postgresql_metrics.error:
                pg_errors += 1
            else:
                pg_times.append(result.postgresql_metrics.total_time_ms)
            
            if result.azure_metrics.error:
                azure_errors += 1
            else:
                azure_times.append(result.azure_metrics.total_time_ms)
            
            # Quality metrics
            if result.quality_metrics:
                overlap = result.quality_metrics.get("result_overlap", {})
                quality_scores.append(overlap.get("jaccard_similarity", 0))
        
        # Calculate statistics
        def calc_stats(times: List[float]) -> Dict[str, float]:
            if not times:
                return {"count": 0, "avg": 0, "min": 0, "max": 0, "median": 0, "std_dev": 0}
            
            return {
                "count": len(times),
                "avg": statistics.mean(times),
                "min": min(times),
                "max": max(times),
                "median": statistics.median(times),
                "std_dev": statistics.stdev(times) if len(times) > 1 else 0
            }
        
        analysis = {
            "test_summary": {
                "total_queries": len(results),
                "unique_queries": len(results) // num_iterations,
                "iterations": num_iterations,
                "test_timestamp": datetime.now().isoformat()
            },
            "postgresql_performance": {
                **calc_stats(pg_times),
                "error_count": pg_errors,
                "success_rate": (len(pg_times) / len(results)) * 100 if results else 0
            },
            "azure_ai_search_performance": {
                **calc_stats(azure_times),
                "error_count": azure_errors,
                "success_rate": (len(azure_times) / len(results)) * 100 if results else 0
            },
            "quality_analysis": {
                "avg_overlap_score": statistics.mean(quality_scores) if quality_scores else 0,
                "min_overlap_score": min(quality_scores) if quality_scores else 0,
                "max_overlap_score": max(quality_scores) if quality_scores else 0
            },
            "comparative_analysis": self._generate_comparative_analysis(pg_times, azure_times),
            "detailed_results": [
                {
                    "query": result.query,
                    "postgresql_time": result.postgresql_metrics.total_time_ms,
                    "azure_time": result.azure_metrics.total_time_ms,
                    "postgresql_results": len(result.postgresql_results),
                    "azure_results": len(result.azure_results),
                    "overlap_score": result.quality_metrics.get("result_overlap", {}).get("jaccard_similarity", 0) if result.quality_metrics else 0,
                    "postgresql_error": result.postgresql_metrics.error,
                    "azure_error": result.azure_metrics.error
                }
                for result in results
            ]
        }
        
        return analysis
    
    def _generate_comparative_analysis(
        self,
        pg_times: List[float],
        azure_times: List[float]
    ) -> Dict[str, Any]:
        """Generate comparative analysis between providers"""
        
        if not pg_times or not azure_times:
            return {"error": "Insufficient data for comparison"}
        
        pg_avg = statistics.mean(pg_times)
        azure_avg = statistics.mean(azure_times)
        
        faster_provider = "PostgreSQL" if pg_avg < azure_avg else "Azure AI Search"
        speed_difference = abs(pg_avg - azure_avg)
        speed_improvement = (speed_difference / max(pg_avg, azure_avg)) * 100
        
        return {
            "faster_provider": faster_provider,
            "speed_difference_ms": speed_difference,
            "speed_improvement_percent": speed_improvement,
            "postgresql_avg_ms": pg_avg,
            "azure_avg_ms": azure_avg,
            "recommendation": self._generate_recommendation(pg_avg, azure_avg, speed_improvement)
        }
    
    def _generate_recommendation(
        self,
        pg_avg: float,
        azure_avg: float,
        improvement: float
    ) -> str:
        """Generate performance-based recommendation"""
        
        if improvement < 5:
            return "Performance is similar between providers. Consider other factors like cost and features."
        elif pg_avg < azure_avg:
            if improvement > 20:
                return "PostgreSQL shows significant performance advantage. Consider staying with current setup."
            else:
                return "PostgreSQL is faster but difference is moderate. Evaluate other migration benefits."
        else:
            if improvement > 20:
                return "Azure AI Search shows significant performance advantage. Strong candidate for migration."
            else:
                return "Azure AI Search is faster but difference is moderate. Consider additional features like semantic search."
    
    async def run_load_test(
        self,
        concurrent_requests: int = 5,
        duration_seconds: int = 60
    ) -> Dict[str, Any]:
        """Run load test to evaluate performance under concurrent load"""
        
        print(f"Starting load test: {concurrent_requests} concurrent requests for {duration_seconds} seconds")
        
        start_time = time.time()
        end_time = start_time + duration_seconds
        
        results = []
        tasks = []
        
        async def worker():
            """Worker function for concurrent requests"""
            while time.time() < end_time:
                query = self.test_queries[int(time.time()) % len(self.test_queries)]
                try:
                    result = await rag_comparison_service.compare_search_providers(query, 3)
                    results.append(result)
                except Exception as e:
                    print(f"Worker error: {e}")
                
                # Small delay to prevent overwhelming
                await asyncio.sleep(0.1)
        
        # Start concurrent workers
        for _ in range(concurrent_requests):
            tasks.append(asyncio.create_task(worker()))
        
        # Wait for completion
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # Analyze load test results
        load_analysis = self._analyze_performance_results(results, 1)
        load_analysis["load_test_config"] = {
            "concurrent_requests": concurrent_requests,
            "duration_seconds": duration_seconds,
            "actual_duration": time.time() - start_time,
            "total_requests": len(results),
            "requests_per_second": len(results) / (time.time() - start_time)
        }
        
        return load_analysis
    
    def print_summary(self, analysis: Dict[str, Any]):
        """Print formatted summary of test results"""
        
        print("\n" + "="*60)
        print("RAG PERFORMANCE TEST SUMMARY")
        print("="*60)
        
        summary = analysis["test_summary"]
        print(f"Total Queries: {summary['total_queries']}")
        print(f"Unique Queries: {summary['unique_queries']}")
        print(f"Iterations: {summary['iterations']}")
        
        print("\nPostgreSQL Performance:")
        pg = analysis["postgresql_performance"]
        print(f"  Average Response Time: {pg['avg']:.2f}ms")
        print(f"  Min/Max: {pg['min']:.2f}ms / {pg['max']:.2f}ms")
        print(f"  Success Rate: {pg['success_rate']:.1f}%")
        
        print("\nAzure AI Search Performance:")
        azure = analysis["azure_ai_search_performance"]
        print(f"  Average Response Time: {azure['avg']:.2f}ms")
        print(f"  Min/Max: {azure['min']:.2f}ms / {azure['max']:.2f}ms")
        print(f"  Success Rate: {azure['success_rate']:.1f}%")
        
        print("\nComparative Analysis:")
        comp = analysis["comparative_analysis"]
        if "error" not in comp:
            print(f"  Faster Provider: {comp['faster_provider']}")
            print(f"  Speed Improvement: {comp['speed_improvement_percent']:.1f}%")
            print(f"  Recommendation: {comp['recommendation']}")
        
        print("\nQuality Analysis:")
        quality = analysis["quality_analysis"]
        print(f"  Average Result Overlap: {quality['avg_overlap_score']:.2f}")
        
        print("="*60)

async def main():
    """Main function for running performance tests"""
    parser = argparse.ArgumentParser(description='RAG Performance Testing Tool')
    parser.add_argument('--test-type', choices=['performance', 'load'], default='performance',
                       help='Type of test to run')
    parser.add_argument('--iterations', type=int, default=3,
                       help='Number of iterations for performance test')
    parser.add_argument('--concurrent', type=int, default=5,
                       help='Number of concurrent requests for load test')
    parser.add_argument('--duration', type=int, default=60,
                       help='Duration in seconds for load test')
    parser.add_argument('--top-k', type=int, default=5,
                       help='Number of results to return per query')
    
    args = parser.parse_args()
    
    tester = RAGPerformanceTester()
    
    if args.test_type == 'performance':
        print("Running performance test...")
        analysis = await tester.run_performance_test(
            num_iterations=args.iterations,
            top_k=args.top_k
        )
        tester.print_summary(analysis)
    
    elif args.test_type == 'load':
        print("Running load test...")
        analysis = await tester.run_load_test(
            concurrent_requests=args.concurrent,
            duration_seconds=args.duration
        )
        tester.print_summary(analysis)
        
        load_config = analysis["load_test_config"]
        print(f"\nLoad Test Results:")
        print(f"  Requests per second: {load_config['requests_per_second']:.2f}")
        print(f"  Total requests: {load_config['total_requests']}")

if __name__ == "__main__":
    asyncio.run(main())
