from client_plugins.bneportal.compensation.utils import check_pcis_id_authorization, validate_cust_crid, get_followup_questions, get_customer_validation_results, get_logger, post_with_cert
import requests
from typing import Annotated
from langgraph.prebuilt.tool_node import InjectedState
import json
import os
from config.openAI import json_model
from langchain_core.tools import tool
from utils.tool_utils import log_tool_error
from datetime import datetime 
import traceback

TRY_ANOTHER_PROMPTS = "View Compensation Prompts"
ALLIANCE_PARTNERS = ["UHG","Medica","Harvard Pilgrim","Mid-Atlantic Health","Pacificare"]
logger = get_logger(__name__)

current_year = datetime.now().year

@tool
async def commission_statements(state: Annotated[dict, InjectedState], pcis_input: str = None, business_segment: str = "All", year: int = current_year, payPeriodEndDate: str = None) -> dict:  
    """
    1. Always fetch fresh data everytime with this tool. Even if the answer to the question/inputs was provided earlier, ignore any earlier result provided with this tool.
    2. This tool will give a list of commission (also known as broker) statements.
    3. Never prompt the user to provide a PCIS ID. Only if the user has explicitly provided/mentioned a PCIS ID/pcis id in the input without being asked, assign that value to the pcis_input variable. Otherwise, don't do anything.
    4. If the user explicitly provides a Business Segment, assign that value to the business_segment variable. If they don't provide, then use "All" as the default.
    5. If the user explicitly enters or specifies a year in the input, assign that value to the year variable. If they don't provide an year, then use default option provided to parameter.
    """

    user_type, authorized, pcis_id = check_pcis_id_authorization(state, pcis_input)

    logger.info(f"pcis_id: {pcis_id}, authorized: {authorized}, user_type: {user_type}")

    if not authorized and pcis_id == "no_pcis":
        no_pcis_message = "You have not provided a PCIS ID. Please provide a valid PCIS ID to view Commission Statements."
        return {
            "answer": no_pcis_message,
            "follow_up_questions": [TRY_ANOTHER_PROMPTS]
        }
    elif not authorized:
        no_pcis_auth_message = "You do not have authorization to view Commission Statements for provided PCIS ID. "
        return {
            "answer": no_pcis_auth_message,
            "follow_up_questions": [TRY_ANOTHER_PROMPTS]
        }
    else:
        if business_segment != "All":  
            # Only process business_segment if it was provided
            business_segment = business_segment.strip().lower()  
            segments_mapping = {  
                "uhc": "UHC",  
                "hpjv": "HPJV",  
                "medica": "Medica",
                "comcalc": "COMCALC",
                "nhp": "NHP",
                "prms": "PRMS",
                "uhcofca": "UHCOFCA",
                "usb": "USB",
                "all": "All"
            }
            business_segment = segments_mapping.get(business_segment, "All")
            if business_segment not in ["UHC", "HPJV", "Medica", "COMCALC", "NHP", "PRMS", "UHCOFCA", "USB", "All"]:
                business_segment = "All"
        
        api_url = os.getenv("COMMISSION_STATEMENTS_ENDPOINT")

        headers = {  
            "Content-Type": "application/json"  
        }  

        payload = {
            "payeeId": pcis_id,
            "businessSegment": business_segment,
            "year": year,
            "payPeriod": str(payPeriodEndDate) if payPeriodEndDate else "All",
        } 
        logger.info(f"Payload for Commission Statements API: {payload}")

        try: 
            response = await post_with_cert(api_url, payload)  
            if response.status_code == 200:  
                stmts_data = response.json()  
                stmts_list = stmts_data.get('payload', {}).get('data', {})

                if stmts_list:
                    commission_stmts_data ={
                        "pcis": payload.get("payeeId"),
                        "stmtsList": stmts_list
                    }

                    questions = [
                        "View Commission Statements for the year: xxxx",
                        "Apply multiple filters for Commission Statements",
                        "View Customer Details for Customer Number: xxxxx",
                        TRY_ANOTHER_PROMPTS
                    ]

                    user_info = {
                        "type": user_type,
                        "pcis": pcis_id,
                    }
                    logger.info(f"Commission Statements Data: {commission_stmts_data}")

                    return {
                        "answer": {
                            "client": "bne_client",
                            "functionality": "compensation", 
                            "response_type": "commissionStatements", 
                            "userInfo": user_info,
                            "commissionStmtsData": commission_stmts_data
                        },
                        "follow_up_questions": questions,
                    }
                else: 
                    no_data_message = "No Commission Statements found for given inputs. Please check the Business Segment, or Year provided."
                    return {
                        "answer": no_data_message,
                        "follow_up_questions": [TRY_ANOTHER_PROMPTS]
                    }
            else:  
                resp_error_message = "I was unable to retrieve commission statements. Please try again or at a later time."
                return {
                    "answer": resp_error_message,
                    "follow_up_questions": [TRY_ANOTHER_PROMPTS]
                } 
        except Exception as e:
            await log_tool_error(state, traceback.format_exc(), "commission_statements")
            exception_message = "I am unable to retrieve commission statements at this time. Please try at a later time."
            return {
                "answer": exception_message,
                "follow_up_questions": [TRY_ANOTHER_PROMPTS]
            }   
    
   
@tool
async def commission_rates(state: Annotated[dict, InjectedState], pcis_input: str = None, customer_id: str = None, coverage_type: str = None) -> dict:
    """
    1. This tool will give the commission rates. 
    2. Never prompt the user to provide a PCIS ID. Only if the user has explicitly provided/mentioned a PCIS ID/pcis id in the input without being asked, assign that value to the pcis_input variable. Otherwise, don't do anything.
    3. Prompt for Customer ID and Coverage Type together.
    4. Do NOT use this tool for commission statements, compensation details transactions, or compensation details summaries.
    5. coverage_type: The expected values for this field are "Life, Medical, Dental, Vision". Any other coverage type given by the user will be invalid, and the user will be asked again.
    """ 

    user_type, authorized, pcis_id = check_pcis_id_authorization(state, pcis_input)

    logger.info(f"pcis_id: {pcis_id}, authorized: {authorized}, user_type: {user_type}")

    if not authorized and pcis_id == "no_pcis":
        no_pcis_message = "You have not provided a PCIS ID. Please provide a valid PCIS ID to view Commission Rates."
        return {
            "answer": no_pcis_message,
            "follow_up_questions": [TRY_ANOTHER_PROMPTS]
        }
    elif not authorized:
        no_pcis_auth_message = "You do not have authorization to view Commission Rates for provided PCIS ID. "
        return {
            "answer": no_pcis_auth_message,
            "follow_up_questions": [TRY_ANOTHER_PROMPTS]
        }
    else:
        validation_results = await get_customer_validation_results(state, customer_id, pcis_id)

        logger.info(f"***After making validation function: {validation_results}***")

        if "answer" in validation_results:
            return validation_results
        elif validation_results.get("valid_association"):
    
            api_verify_pcis_id = os.getenv("COMMISSION_RATES_PRODUCER_ENDPOINT") 
            api_verify_customer_id = os.getenv("COMPENSATION_CUSTOMER_INFO_ENDPOINT")
            coverage_code = coverage_type.strip().upper()[0]

            headers = {
                "Content-Type": "application/json"
            }
            payload_verify_pcis_id = {
                "custNbr": customer_id,
                "sysCode": "AC",
                "covTyp": coverage_code
            }
            payload_verify_customer_id = {
                "custNm": None,
                "srcSysCustNbr": customer_id,
                "type": "customer",
                "request": "id"
            }
            logger.info(f"Payload for Verify PCIS ID API: {payload_verify_pcis_id}")

            try:
                response_verify_customer = await post_with_cert(api_verify_customer_id, payload_verify_customer_id)
                response_verify_pcis_id = await post_with_cert(api_verify_pcis_id, payload_verify_pcis_id)

                logger.info(f"Response from Verify Customer API: {response_verify_customer}")

                if response_verify_customer.status_code == 200:
                    json_response = response_verify_customer.json()
                    cust_data_list = json_response.get("payload", {}).get("data", {})
                    if cust_data_list:
                        cust_details = cust_data_list[0]
                    else:
                        no_details_message = "Unable able to fetch some necessary details for the customer. Please try again or at a later time."
                        return {
                            "answer": no_details_message,
                            "follow_up_questions": [TRY_ANOTHER_PROMPTS]
                        }
                    
                if response_verify_pcis_id.status_code == 200:
                    json_response = response_verify_pcis_id.json()
                    payee_details_list = json_response.get("payload", {}).get("data", [])
                    
                    if not payee_details_list:
                        return {
                            "answer": "No Commission Rates found for given inputs. Please check the Customer ID and Coverage Type provided.",
                            "follow_up_questions": ["Please use this Customer ID: (enter id here)  & Coverage Type: (enter type here)",TRY_ANOTHER_PROMPTS]
                        }
                    
                payee_ids = []
                for item in payee_details_list:
                    payee_details = item.get("payeeDetails", {})
                    payee_id = payee_details.get("payeeId")
                    if payee_id:
                        payee_ids.append(payee_id)
                
                if pcis_id in payee_ids:
                    rates_info_api = os.getenv("COMMISSION_RATES_INFO_ENDPOINT")
                    payload = {
                        "alliancePartners": ALLIANCE_PARTNERS,
                        "customerId": customer_id,
                        "type": "customer",
                    }

                    summary_api = os.getenv("COMMISSION_RATES_SUMMARY_ENDPOINT") 
                    payload_2 = {
                        "customerId": customer_id,  
                        "sourceSystem": "AC",  
                        "contractId": "All",  
                        "coverageType": coverage_code,
                        "issueState": "All",  
                        "chargeLine": "All",  
                        "chargeLineCodes": None,  
                        "type": "All"  
                    }
                    try:
                        #response_rates_info = requests.post(rates_info_api, headers=headers, data=json.dumps(payload), verify=False)
                        #response_commission_rates = requests.post(summary_api, headers=headers, data=json.dumps(payload_2), verify=False)  
                        response_rates_info = await post_with_cert(rates_info_api, payload)
                        response_commission_rates = await post_with_cert(summary_api, payload_2)

                        if response_commission_rates.status_code == 200 and response_rates_info.status_code == 200:
                            data = response_commission_rates.json().get("payload", {}).get("data", {}) 
                            customer_data = response_rates_info.json().get("payload", {}).get("data", {}).get("customerDetailsResponse", [])

                            rates_list = data.get('baseList', []) 
                            questions = [
                                "View Customer Details for Customer Number: xxxxx", 
                                "View Compensation Details"
                            ]

                            if customer_data and len(customer_data) > 0:
                                logger.info(f"Found customer data with {len(customer_data)} records. Returning data.")
                                questions = get_followup_questions(questions, customer_data, "commission_rates")

                            commission_rates_data = {
                                "ratesList": rates_list,
                                "customerId": customer_id,
                                "pcisId": pcis_id,
                            }                          
                            user_info = {
                                "type": user_type,
                                "pcis": pcis_id,
                            }

                            return {
                                "answer": {
                                    "client": "bne_client",
                                    "functionality": "compensation",  
                                    "response_type": "commissionRates",  
                                    "commissionRatesData": commission_rates_data,
                                    "userInfo": user_info,
                                    "custValidationResult" : validation_results,
                                },
                                "follow_up_questions": questions
                            }
                        else:
                            no_rates_message = "Unable able to retrieve commission rates. Please try again or at a later time."
                            return {
                                "answer": no_rates_message,
                                "follow_up_questions": [TRY_ANOTHER_PROMPTS]
                            }
                    except Exception as e:
                        exception_message = "I am unable to retrieve commission rates at this time. Please try at a later time."
                        return {
                            "answer": exception_message,
                            "follow_up_questions": [TRY_ANOTHER_PROMPTS]
                        }
                else:
                    no_pcis_message = f"Your PCIS ID {pcis_id} is not authorized to view Commission Rates for your selection. Please check the PCIS ID, Customer ID, or Coverage Type provided."
                    return {
                        "answer": no_pcis_message,
                        "follow_up_questions": ["Please use this Customer ID: (enter id here) & Coverage Type: (enter type here)", "Please use this PCIS ID: (enter id here) to retrieve commission rates.",TRY_ANOTHER_PROMPTS]
                    }   
            except Exception as e:
                await log_tool_error(state, traceback.format_exc(), "commission_rates")
                exception_message = "I am unable to retrieve commission rates at this time. Please try at a later time."
                return {
                    "answer": exception_message,
                    "follow_up_questions": [TRY_ANOTHER_PROMPTS]
                } 
        else:
            status_message = "Can't find valid association of PCIS ID and the Customer ID provided. Please try with different Customer ID"
            return {
                "answer": status_message,
                "follow_up_questions": ["Try with another Customer ID: ",TRY_ANOTHER_PROMPTS]
            }
    
    
@tool
def filter_commission_statements() :
    """ 
    This tool renders the micro frontend for filter selection. This will be called ONLY when the user asks to filter commission statements.
    After the user interacts with this filter interface for commission statements, the system should:
    1. Capture all filter parameters selected by the user: (pcis_id (PCIS ID), business_segment (Business Segment), year (Period Year), payPeriodEndDate (Period End Date))
    2. Call the commission_statements tool using those parameters
    """
    return {
        "answer": {
            "client": "bne_client",
            "functionality": "compensation",
            "response_type": "filterCommissionStatements",
        },
        "follow_up_questions": [TRY_ANOTHER_PROMPTS],
    }