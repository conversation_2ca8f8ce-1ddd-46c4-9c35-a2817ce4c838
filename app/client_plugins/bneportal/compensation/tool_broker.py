from client_plugins.bneportal.compensation.utils import check_pcis_id_authorization, validate_cust_crid, get_followup_questions, get_customer_validation_results, get_logger,post_with_cert
import requests
from typing import Annotated
from langgraph.prebuilt.tool_node import InjectedState
import json
import os
from config.openAI import json_model
from langchain_core.tools import tool
from utils.tool_utils import log_tool_error
from datetime import datetime 
import traceback

logger = get_logger(__name__)

TRY_ANOTHER_PROMPTS = "View Compensation Prompts"

@tool
async def producer_details(state: Annotated[dict, InjectedState], pcis_input: str = None) -> dict:
    """
    1. This tool will retrieve producer details based on an ID. Only call tool if the user asks for producer details.
    2. Never prompt the user to provide a PCIS ID. Only if the user has explicitly provided/mentioned a PCIS ID/pcis id in the input without being asked, assign that value to the pcis_input variable. Otherwise, don't do anything.
    """

    user_type, authorized, pcis_id = check_pcis_id_authorization(state, pcis_input)

    logger.info(f"pcis_id111: {pcis_id}, authorized111: {authorized}, user_type111: {user_type}")

    if not authorized and pcis_id == "no_pcis":
        no_pcis_message = "You have not provided a PCIS ID. Please provide a valid PCIS ID to view Producer Information."
        return {
            "answer": no_pcis_message,
            "follow_up_questions": [TRY_ANOTHER_PROMPTS]
        }
    elif not authorized:
        no_pcis_auth_message = "You do not have authorization to view Producer Information for provided PCIS ID. "
        return {
            "answer": no_pcis_auth_message,
            "follow_up_questions": [TRY_ANOTHER_PROMPTS]
        }
    else:
        api_url = os.getenv("PRODUCER_RELATIONSHIP_ENDPOINT")
        headers = {
            "Content-Type": "application/json"
        }
        
        payload = {
            "request": {
                "findProducerRequest": {
                    "producerID": pcis_id,
                    "producerSSN": None,
                    "producerTIN": None,
                    "producerFirstName": None,
                    "producerMiddleInitial": None,
                    "producerLastName": None,
                    "producerCompanyName": None,
                    "producerState": None,
                    "producerStatus": None,
                    "legacyProducerSystem": None,
                    "legacyProducerID": None,
                    "customerNumber": "",
                    "adminSystem": "",
                    "customerName": None
                },
                "serviceAttributes": {
                    "applicationIdentifier": "compagent",
                    "requestIdentifier": "",
                    "requestedDate": None,
                    "producerGroupAssociation": "present",
                    "producerAndGroupFlag": "N",
                    "pageNumber": 1,
                    "recordsPerPage": 50,
                    "totalNumberOfRecords": None,
                    "responseCode": 0,
                    "responseDescription": ""
                }
            }
        }

        logger.info(f"API URL for producer data: {api_url}, Payload: {json.dumps(payload)}")

        try:
            response = await post_with_cert(api_url, payload)
            if response.status_code == 200:
                producer_data = response.json()

                # Extract only the required data from the response
                response_data = producer_data.get('response', {})
                
                if response_data:                  
                    questions = [
                        "Download Customer List",
                        "View Customer Details",
                        "View Commission Statements",
                        "Search Customer by Name",
                        TRY_ANOTHER_PROMPTS
                    ]
                    
                    user_info = {
                        "type": user_type,
                        "pcis": pcis_id,
                    }

                    return {
                        "answer": {
                            "client": "bne_client",
                            "functionality": "compensation",
                            "response_type": "producerData",
                            "userInfo": user_info,
                            "producerData": response_data
                        },
                        "follow_up_questions": questions
                    }
            else:
                resp_error_message = "Unable to retrieve producer data. Please try again later."
                return {
                    "answer": resp_error_message,
                    "follow_up_questions": ["Try with a different PCIS ID: ",TRY_ANOTHER_PROMPTS]
                }
        except Exception as e:
            await log_tool_error(state, traceback.format_exc(), "producer_details")
            exception_message = "I am unable to retrieve producer data at this time. Please try again later."
            return {
                "answer": exception_message,
                "follow_up_questions": ["Try with a different PCIS ID: ",TRY_ANOTHER_PROMPTS]
            }

@tool
async def customer_search_by_name(state: Annotated[dict, InjectedState], customer_name: str, pcis_input : str = None ) -> dict:
    """
    This tool searches for customers by name for a specific producer.
    1. If the user has explicitly provided/mentioned a PCIS ID/pcis id in the input without being asked, assign that value to the pcis_input variable. If not, no need to do anything.
    2. Customer Name is a required parameter. Only if the user has provided a customer name to search for in previous message(s), use the most recent name. If not, then ask the user to provide a customer name.
    3. The tool will search for customers with names matching the provided search string.
    4. The search is case-insensitive and supports partial matches (add * after the name for wildcard search).
    """

    user_type, authorized, pcis_id = check_pcis_id_authorization(state, pcis_input)

    logger.info(f"pcis_id: {pcis_id}, authorized: {authorized}, user_type: {user_type}, customer_name: {customer_name}")

    if not authorized and pcis_id == "no_pcis":
        no_pcis_message = "You have not provided a PCIS ID. Please provide a valid PCIS ID to run Customer Search."
        return {
            "answer": no_pcis_message,
            "follow_up_questions": [TRY_ANOTHER_PROMPTS]
        }
    elif not authorized:
        no_pcis_auth_message = "You do not have authorization to run Customer Search for provided PCIS ID. "
        return {
            "answer": no_pcis_auth_message,
            "follow_up_questions": [TRY_ANOTHER_PROMPTS]
        }
    else:
    # Check if customer name is at least 3 characters long
        if customer_name and len(customer_name.strip()) < 3:
            return {
                "answer": f"The customer name '{customer_name}' is too short. Please enter either the full name or at least the first 3 characters of the name.",
                "follow_up_questions": [TRY_ANOTHER_PROMPTS]
            }
            
        api_url = os.getenv("PRODUCER_RELATIONSHIP_ENDPOINT")
        
        # Add wildcard if not already present
        if not customer_name.endswith('*'):
            customer_name = f"{customer_name}*"
        
        headers = {
            "Content-Type": "application/json"
        }
        
        payload = {
            "request": {
                "findProducerRequest": {
                    "producerID": pcis_id,
                    "producerSSN": None,
                    "producerTIN": None,
                    "producerFirstName": None,
                    "producerMiddleInitial": None,
                    "producerLastName": None,
                    "producerCompanyName": None,
                    "producerState": None,
                    "producerStatus": None,
                    "legacyProducerSystem": None,
                    "legacyProducerID": None,
                    "customerNumber": "",
                    "adminSystem": "",
                    "customerName": customer_name
                },
                "serviceAttributes": {
                    "applicationIdentifier": "COMPAGENT",
                    "requestIdentifier": "",
                    "requestedDate": None,
                    "producerGroupAssociation": "present",
                    "producerAndGroupFlag": "Y",
                    "pageNumber": 1,
                    "recordsPerPage": 50,
                    "totalNumberOfRecords": None,
                    "responseCode": 0,
                    "responseDescription": ""
                }
            }
        }
        
        logger.info(f"Payload for customer search API: {json.dumps(payload)}")
        
        try:
            response = await post_with_cert(api_url, payload)

            if response.status_code == 200:
                customer_data = response.json().get('response', {}).get('findProducerResponse', [])
                
                # Extract only customerDetails from each item
                extracted_customer_details = []
                for item in customer_data:
                    if 'customerDetails' in item:
                        extracted_customer_details.extend(item.get('customerDetails', []))
                
                # Remove duplicate customers by creating a unique identifier from customerNumber and adminSystem
                unique_customer_details = []
                seen_identifiers = set()
                
                for customer in extracted_customer_details:
                    # Create compound key for uniqueness check
                    customer_key = f"{customer.get('customerNumber')}_{customer.get('adminSystem')}"
                    
                    if customer_key not in seen_identifiers:
                        seen_identifiers.add(customer_key)
                        unique_customer_details.append(customer) 
                
                if unique_customer_details:
                    
                    questions= [
                        "Search Another Customer with Customer Name: xxxx",
                        "View Customer Details with Customer Number: xxxxx",
                        TRY_ANOTHER_PROMPTS
                    ]
                    
                    return {
                        "answer": {
                            "client": "bne_client",
                            "functionality": "compensation",
                            "response_type": "customerSearchData",
                            "customerListData": unique_customer_details,
                        },
                        "follow_up_questions": questions
                    }
                else:
                    no_results_message = f"No customers found matching '{customer_name.replace('*', '')}'. Please try with different name."
                    return {
                        "answer": no_results_message,
                        "follow_up_questions": [TRY_ANOTHER_PROMPTS]
                    }
            else:
                error_message = "Unable to search customers. Please try again later."
                return {
                    "answer": error_message,
                    "follow_up_questions": [TRY_ANOTHER_PROMPTS]
                }
        except Exception as e:
            await log_tool_error(state, traceback.format_exc(), "customer_search_by_name")
            exception_message = "I am unable to search for customers at this time. Please try again later."
            return {
                "answer": exception_message,
                "follow_up_questions": [TRY_ANOTHER_PROMPTS]
            }