from typing import Annotated, Callable
from typing_extensions import TypedDict
from langgraph.graph.message import AnyMessage, add_messages
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import Runnable
from langgraph.checkpoint.sqlite import SqliteSaver
from langgraph.graph import END, StateGraph
from langgraph.prebuilt import tools_condition
import uuid
from langchain_core.messages import HumanMessage
from mongodb_agent.utils import create_tool_node_with_fallback, _print_event
from mongodb_agent.tools import mongo_query, fetch_vector_details
from config.openAI import model
import os
from motor.motor_asyncio import AsyncIOMotorClient

dbString = os.environ.get("MONGO_DB_URL")
dbName = os.environ.get("DB")
dbCollection = os.environ.get("COLLECTION")
 
class State(TypedDict):
    messages: Annotated[list[AnyMessage], add_messages]
 
class Assistant:
    def __init__(self, runnable: Runnable):
        self.runnable = runnable
 
    def __call__(self, state: State):
        while True:
            state = {**state, "user_info": ""}
            result = self.runnable.invoke(state)
            if not result.tool_calls and (
                not result.content
                or isinstance(result.content, list)
                and not result.content[0].get("text")
            ):
                messages = state["messages"] + [("user", "Respond with a real output.")]
                state = {**state, "messages": messages}
            else:
                break
        return {"messages": result}
    
async def create_entry_node(state: State,
                    host = dbString,
                    database = dbName,
                    collection = dbCollection) -> Callable:
    client = AsyncIOMotorClient(host)

    db = client[database]
    coll = db[collection]
    # document = await coll.find().sort('_id', -1).limit(1).to_list(length=1) # for latest entry
    document = await coll.find_one({'caseId': 200}) # test entry
    if document:
        structure = document
    else:
        structure = {}
    return {
            "messages": HumanMessage(content = "Ignore this message in the normal conversation,"
                                               f"This is the example structure of the documents in database, {structure}"
                                               "Remember you will never mention that you have been provided with structure again and not even mention it, but use it quietly."
                                               "Now Respond to the previous query",),
            "dialog_state": "structure in memory",
        }

primary_assistant_prompt = ChatPromptTemplate.from_messages(
    [
        (
            "system",
            "You are a helpful user support assistant for DB inquiry related tasks. "
            " Use the provided tools to create and perform mongo or vector database related queries to assist the user's requirement. "
            " If the request is for mongoDB create a query_request in JSON format which contains the query_type [find_one, find, update_one, update_many, insert_one, insert_many, delete_one, delete_many], and other relevant details."
            " If the request is for vectorDB or vector search, convert the user query into the query which is effective for vector search and proceed directly"
            "Strictly tell the user that you can't process the query as you don't have the capabilities other than monogoDB/mongoSearch or vectorDB/vector search, related transactions."
            " When searching, be persistent. Expand your query bounds if the first search returns no results. "
            " If a search comes up empty, expand your search before giving up."
            " If the tool call is not required and the query is to map the json then dont call the toll and perform parsing the json on your own "
        ),
        ("placeholder", "{messages}"),
    ]
).partial()

primary_assistant_tools = [mongo_query, fetch_vector_details]
primary_assistant_runnable = primary_assistant_prompt | model.bind_tools(primary_assistant_tools)

builder = StateGraph(State)
 
# Define nodes
builder.add_node("assistant", Assistant(primary_assistant_runnable))
builder.add_node("DB_structure", create_entry_node)
builder.add_edge("DB_structure","assistant")
builder.add_node("tools", create_tool_node_with_fallback(primary_assistant_tools))
 
builder.set_entry_point("DB_structure")
builder.add_conditional_edges("assistant",tools_condition)
 
memory = SqliteSaver.from_conn_string(":memory:")

primary_assistant_graph = builder.compile(checkpointer=memory)
 
thread_id = str(uuid.uuid4())
 
config = {
    "configurable": {
        # Checkpoints are accessed by thread_id
        "thread_id": thread_id,
    }
}
_printed = set()
 
def run_assistant_db(user_input: str):
    events = primary_assistant_graph.stream(
        {"messages": ("user", user_input)}, config, stream_mode="values"
    )
    for event in events:
       resp = _print_event(event, _printed)
    return resp