apiVersion: apps/v1
kind: Deployment
metadata:
  name: magnus-oec-api
  labels:
    app: magnus-oec-api
    env: dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: magnus-oec-api
  strategy: {}
  template:
    metadata:
      labels:
        app: magnus-oec-api
    spec:
      containers:
        - name: magnus-oec-api
          env:
            - name: VERIFY_SSL
              value: "False"
            - name: CIRRUS_URL_BASE
              value: https://gateway-stage-dmz.optum.com/api/master/cel/cirrus
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: jwt-credentials
                  key: jwt-secret
            - name: JWT_KEY
              valueFrom:
                secretKeyRef:
                  name: jwt-credentials
                  key: jwt-key
            - name: AZURE_OPENAI_ENDPOINT_TOKEN
              value: "https://api.uhg.com/api/cloud/api-management/ai-gateway/1.0"
            - name: AZURE_OPENAI_ENDPOINT_KEY
              valueFrom:
                secretKeyRef:
                  name: azure-openai-credentials
                  key: endpoint-key
            - name: OPENAI_API_VERSION_TOKEN
              value: "2025-01-01-preview"
            - name: OPENAI_API_VERSION_KEY
              value: "2023-07-01-preview"
            - name: AZURE_OPENAI_API_KEY
              valueFrom:
                secretKeyRef:
                  name: azure-openai-credentials
                  key: api-key
            - name: AZURE_OPENAI_CHAT_DEPLOYMENT_NAME_TOKEN
              value: "gpt-4o_2024-11-20"
            - name: AZURE_OPENAI_CHAT_DEPLOYMENT_NAME_KEY
              value: "gpt_4o"
            - name: AZURE_OPENAI_CHAT_MODEL_NAME_KEY
              value: "gpt-4o"
            - name: AZURE_OPENAI_CHAT_MODEL_NAME_TOKEN
              value: "gpt-4o"
            - name: OPENAI_PROJECT_ID
              value: "33de7400-191f-4e76-9e11-82a12254eced"
            - name: OPENAI_AUTH_URL
              value: "https://api.uhg.com/oauth2/token"
            - name: OPENAI_CLIENT_ID
              value: "4ced791b-d036-4783-9dd0-bba7fe759c10"
            - name: OPENAI_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: openai-credentials
                  key: client-secret
            - name: OPENAI_SCOPE
              value: "https://api.uhg.com/.default"
            - name: LINGO_OEC_BASE_URL
              value: "https://magnus-oec-api-dev.hcck8s-ctc-np101.optum.com/"
            - name: DB_START_STRING
              value: "Lingo"
            - name: DB_ONBOARDING_START_STRING
              value: "Lingo_Onboarding"
            - name: LingoAI_Kafka_BASE_URL
              value: "https://lingo-kafka-api-dev.hcck8s-ctc-np101.optum.com"
            - name: ENV
              value: "Dev"
            - name: KAFKA_Logger
              value: "True"
            - name: MONGO_DB_URL
              valueFrom:
                secretKeyRef:
                  name: mongodb-credentials
                  key: mongodb-url
            - name: OIDC_MSAD_GROUPS
              value: "MagnusOEC_Users"
            - name: OIDC_AUTOMATIC_SILENT_RENEW
              value: "true"
            - name: OIDC_RESPONSE_TYPE
              value: "code"
            - name: OIDC_CLIENT_SECRET
              value: "WRYm6PGD7GbQD@P2k#o#N2eK4SJF$2xPEJzkda2Uv<om"
            - name: OIDC_SCOPE
              value: "openid profile address email phone"
            - name: OIDC_ACR_VALUE
              value: "R1_AAL1_MS-AD-Kerberos"
            - name: OIDC_REDIRECT_URI
              value: "https://magnus-oec-ui-dev.hcck8s-ctc-np101.optum.com/"
            - name: OHID_REDIRECT_URI
              value: "https://magnus-oec-ui-dev.hcck8s-ctc-np101.optum.com/"
            - name: OIDC_AUTHORITY
              value: "https://authgateway1-dev.entiam.uhg.com"
            - name: OIDC_CLIENT_ID
              value: "Reg1Dev_magnusOEC"
            - name: SAMX_SERVICE_LAYER_URL
              value: "https://samxfi-uat-xport.hcck8s-ctc.optum.com/xport/api/ben/quick-quotes-sl/v1"
            - name: XTRACTOR_EMPLOYEES_API_URL
              value: "https://data-external-dev2-xtractor.hcck8s-ctc-np1.optum.com/app/gpt/get_employees"
            - name: XTRACTOR_GROUP_INFO_API_URL
              value: "https://data-external-dev2-xtractor.hcck8s-ctc-np1.optum.com/app/gpt/get_company_details"
            - name: XTRACTOR_RENEWAL_PLAN_API_URL
              value: "https://data-external-dev2-xtractor.hcck8s-ctc-np1.optum.com/app/gpt/get_plans"
            - name: XTRACTOR_SHOPPING_PLAN_API_URL
              value: "https://data-external-dev2-xtractor.hcck8s-ctc-np1.optum.com/app/gpt/get_allinfo"
            - name: MESSAGES_TO_DELETE_COUNT
              value: "20"
            - name: SUMMARY_TRIGGER_THRESHOLD
              value: "30"
            - name: NIMBUS_URL_BASE
              value: "https://gateway-stage-dmz.optum.com/api/master/cust/cirrus"
            - name: TOKEN_SERVER_URL
              value: "https://authgateway1-dev.entiam.uhg.com/as/token.oauth2"
            - name: OPENAI_RESOURCE_FLAG
              value: "TOKEN"
            - name: AZURE_OPENAI_EMBEDDINGS_TOKEN 
              value: "text-embedding-3-small_1"
            - name: AZURE_OPENAI_EMBEDDINGS_KEY
              value: "text-embedding-3-small"
            - name: DB_CORE_START_STRING
              value: "Lingo_Core"
            - name : DB_MEMBER_ENROLL
              value : "Lingo_Member_Enroll"
            - name : XTRACTOR_ENDPOINT
              value : "https://data-external-dev-xtractor.hcck8s-ctc-np1.optum.com/app/gpt/get_image_data"
            - name : XTRACTOR_SBC_ENDPOINT
              value : "https://data-external-dev3-xtractor.hcck8s-ctc-np1.optum.com/app/sbc/get_sbc_data"              
            - name : EMAIL_AUTOMATION_ENDPOINT
              value : "https://magnus-va-api-dev-pilot.hcck8s-ctc-np101.optum.com/api/secure/documentExtraction/upload"
            - name : AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: aws-credentials
                  key: secret-access-key
            - name : AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: aws-credentials
                  key: access-key-id
            - name : AWS_FOLDER
              value : "dev"
            - name : AWS_BUCKET_NAME
              value : "bne-portal-form"
            - name : AWS_ENDPOINT
              value : "https://s3api-core.optum.com"
            - name : DB_XTRACTOR_DOCS
              value : "SBC_Extractor"
            - name : XTRACTOR_MONGO_DB_URL
              valueFrom:
                secretKeyRef:
                  name: xtractor-mongo-credentials
                  key: mongo-db-url
            - name : OHID_CLIENT_ID
              value : "MGNS0084532N"
            - name : OHID_SECRET   
              valueFrom:
                secretKeyRef:
                  name: ohid-credentials
                  key: secret
            - name : OHID_JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: ohid-credentials
                  key: jwt-secret
            - name : OHID_GRANT_TYPE
              value : "authorization_code"
            - name : OPTUM_OHID_SSO_URL
              value : "https://identity.nonprod.onehealthcareid.com/oidc/token"     
            - name : OPTUM_OHID_SSO_AUTHORITY
              value : "https://identity.nonprod.onehealthcareid.com/oidc/authorize?"   
            - name : LANGFUSE_PUBLIC_KEY
              value : "pk-lf-8c48b9c4-0aca-47a4-8237-6d6aa9140eb5"
            - name : LANGFUSE_HOST
              value : "https://langfuse.hcck8s-ctc-np101.optum.com"                                          
            - name: POSTGRES_HOST
              value: "rn000192364.uhc.com"
            - name: POSTGRES_USER
              value: "lingo_user"
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgres-credentials
                  key: postgres-password
            - name: POSTGRES_DATABASE_NAME
              value: "lingoai_dev"
            - name: SAMX_COMM_CENTER_STARGATE_BASE_URL
              value: https://gateway-stage-core.optum.com/api/gcpdev/ben/commcenter/v1
            - name: SAMX_COMM_CENTER_NB_URL
              value: "https://gateway-stage-dmz.optum.com/api/stage/ben/quick.quotes/v1/getnbcases" 
            - name: SAMX_ONE_DB_URL
              valueFrom:
                secretKeyRef:
                  name: samx-one-db-credentials
                  key: db-url
            - name: SAMX_ONE_DB_NAME
              value: "SAMxOneDev"  
            - name: DISABLE_AUTHORIZATION
              value: "false"
            - name: GCO_PARSER_URL
              value: "https://python-parser-stage.optum.com/"
            - name: GCO_REDIS_URL
              value: "https://bneportal-stg-polaris-redis.prod.internal-gcp.optum.com/api/secure/cache/samx/v1/"   
            - name: GCO_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: gco-secret-key
                  key: secret
            - name: SAMX_ONE_COMMON_SERVICE_URL            
              value: "https://samxlf-dev-samx-one-common.prod.internal-gcp.optum.com/samx-one-api/common-services/v1"  
            - name: PERMISSIONS_CACHE_TTL
              value: "3600" # 1 hour in seconds      
            - name: "CER_LINK"
              value: "https://identity.nonprod.onehealthcareid.com/oidc/authorize?client_id=SSR027589N&response_type=code&scope=openid+profile+email&redirect_uri=https://stage2-reporting.paussr.optum.com/MicroStrategy/servlet/mstrWeb%3Fevt%3D3010%26src%3DmstrWeb.3010%26loginReq%3Dtrue%26Server%3DENV-267945LAIO1EASTUS2%26ServerAlias%3DENV-267945LAIO1EASTUS2%252FENV-267945LAIO2EASTUS2%26Project%3DClaims%252BExperience%252BReporting%26Port%3D34952"
            - name: COMPENSATION_DETAILS_ENDPOINT
              value: "https://bas-operations-stage.uhc.com/operations/compensation/details"
            - name: COMMISSION_STATEMENTS_ENDPOINT
              value: "https://bas-operations-stage.uhc.com/operations/documents/v1/search"
            - name: COMMISSION_RATES_PRODUCER_ENDPOINT
              value: "https://bas-commissionrates-stage.uhc.com/commissionrates/rates/producer/details"
            - name: COMPENSATION_CUSTOMER_INFO_ENDPOINT
              value: "https://bas-operations-stage.uhc.com/operations/compensation/customer-info"
            - name: COMMISSION_RATES_SUMMARY_ENDPOINT
              value: "https://bas-commissionrates-stage.uhc.com/commissionrates/rates/summary"
            - name: CUSTOMER_DETAILS_ENDPOINT
              value: "https://bas-management-stage.uhc.com/management/vcc/read-info"
            - name: CUSTOMER_OVERRIDE_ENDPOINT
              value: "https://bas-producerrels-stage.uhc.com/producerrels/override/read-info"
            - name: MANUAL_HOLDS_ENDPOINT
              value: "https://bas-producer-management-stage.uhc.com/producer-management/holds/summary/customer"
            - name: SERVICE_FEE_AGREEMENT_ENDPOINT
              value: "https://bas-producerrels-stage.uhc.com/producerrels/sfa/read-info"
            - name: SFA_SUMMARY_ENDPOINT
              value: "https://bas-producerrels-stage.uhc.com/producerrels/sfa/summary"
            - name: CRID_DETAILS_ENDPOINT
              value: "https://bas-produceronline-stage.uhc.com/produceronline/producers-customers-relations/v1/search"
            - name: PRODUCER_RELATIONSHIP_ENDPOINT
              value: "https://bas-produceronline-stage.uhc.com/produceronline/producers-customers-relations/v1/search"
            - name: COMMISSION_RATES_INFO_ENDPOINT
              value: "https://bas-commissionrates-stage.uhc.com/commissionrates/rates/read-info"
            - name: BAS_PEM_PATH
              value: "./email/broker-bonus/bascert.pem"  
            - name: DOC360_FIND_URL
              value: "https://gateway-stage-core.optum.com/api/int/ecs/doc360/document-results/v1/search"  
            - name: DOC360_GET_URL
              value: "https://gateway-stage-core.optum.com/api/int/ecs/doc360/document-contents/v1"  
            - name: BNE_GRAPHQL_API_ENDPOINT
              value: "https://api-stg.uhg.com/api/prodr/be3-platform/1.0.0/graphql"
            - name: BNE_OAUTH2_TOKEN_URL
              value: "https://api-stg.uhg.com/oauth2/token"
            - name: BNE_CLIENT_ID
              value: "3e639613-050e-41fa-8c7d-0c18fcd74d86"  
            - name: BNE_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: bne-graphql-credentials
                  key: client-secret
            - name: BNE_SCOPE
              value: "https://api.uhg.com/.default"
            - name: SAMX_ONE_QUOTE_SERVICE_URL_DEV
              value: "https://samxlf-dev-samx-one-quotes.prod.internal-gcp.optum.com/samx-one-quotes/v1"
            - name: SAMX_ONE_QUOTE_SERVICE_URL_TEST
              value: "https://samxlf-test-samx-one-quotes.prod.internal-gcp.optum.com/samx-one-quotes/v1"
            - name: SAMX_ONE_QUOTE_SERVICE_URL_UAT
              value: "https://samxlf-uat-samx-one-quotes.prod.internal-gcp.optum.com/samx-one-quotes/v1"
            - name: SAMX_ONE_QUOTE_SERVICE_URL_STAGE
              value: "https://samxlf-stg-samx-one-quotes.prod.internal-gcp.optum.com/samx-one-quotes/v1"
            - name: SAMX_ONE_QUOTE_SERVICE_URL
              value: "https://samxlf-dev-samx-one-quotes.prod.internal-gcp.optum.com/samx-one-quotes/v1" #TODO update this to production URL once ready
            - name: LINGO_OPENAI_PROJECT_ID
              value: "33de7400-191f-4e76-9e11-82a12254eced"
            - name: LINGO_OPENAI_API_URL_TOKEN
              value: "https://api.uhg.com/api/cloud/api-management/ai-gateway/1.0"
            - name: LINGO_API_4o_VERSION_TOKEN
              value: "2025-01-01-preview"
            - name: LINGO_EMBEDDING_VERSION_TOKEN
              value: "2025-01-01-preview"
            - name: GPT_4o_DEPLOYMENT_TOKEN
              value: "gpt-4o_2024-11-20"
            - name: LINGO_EMBEDDING_DEPLOYMENT_TOKEN
              value: "text-embedding-3-small_1"
            - name: LINGO_OPENAI_API_KEY
              valueFrom:
                secretKeyRef:
                  name: azure-openai-credentials
                  key: api-key
            - name: LINGO_OPENAI_API_URL_KEY
              valueFrom:
                secretKeyRef:
                  name: azure-openai-credentials
                  key: endpoint-key
            - name: LINGO_API_35_VERSION_KEY
              value: "2024-08-01-preview"
            - name: LINGO_API_4o_VERSION_KEY
              value: "2023-07-01-preview"
            - name: GPT_35_DEPLOYMENT_KEY
              value: "2024-06-01"
            - name: GPT_4o_DEPLOYMENT_KEY
              value: "gpt_4o"
            - name: LINGO_EMBEDDING_VERSION_KEY
              value: "2023-07-01-preview"
            - name: LINGO_EMBEDDING_DEPLOYMENT_KEY
              value: "text-embedding-3-small"
            - name: LINGO_WHISPER_VERSION_KEY
              value: "2024-06-01"
            - name: LINGO_WHISPER_DEPLOYMENT_KEY
              value: "magnus-whisper"
          image: docker.repo1.uhc.com
          securityContext:
            runAsUser: 1001
            runAsGroup: 3000
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: false
            capabilities:
              drop:
                - KILL
                - MKNOD
                - SYS_CHROOT
          resources:
            requests:
              memory: 8G
              cpu: 1920m
            limits:
              memory: 8G
              cpu: 1920m
          readinessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 5
            periodSeconds: 10
          livenessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 15
            periodSeconds: 20
          volumeMounts:
            - mountPath: /app/email
              readOnly: false
              name: email
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 180
      volumes:
        - name: email
          persistentVolumeClaim:
            claimName: email
        - name: tls
          secret:
            secretName: magnus-oec-api-cert