import os
from pymongo import MongoClient
from motor.motor_asyncio import AsyncIOMotorClient
import asyncio

db_url = os.getenv("MONGO_DB_URL")
db_name_start = os.getenv("DB_START_STRING")
db_name_member_enroll = os.getenv("DB_MEMBER_ENROLL")
db_onboarding_start_string = os.getenv("DB_ONBOARDING_START_STRING")
db_core_start_string = os.getenv("DB_CORE_START_STRING")

ENV = os.getenv("ENV").lower()
if ENV not in ["prod", "dev", "stage"]:
    raise ValueError("Unknown environment, ENV must be one of 'prod', 'dev', or 'stage'")

onboarding_db = db_onboarding_start_string + "_" + ENV.capitalize() if ENV != "prod" else db_onboarding_start_string

async_mongo_client = AsyncIOMotorClient(db_url)
sync_mongo_client = MongoClient(db_url)

if not db_core_start_string:
    raise ValueError("DB_CORE_START_STRING environment variable is not set")
db_core_name = db_core_start_string + "_" + ENV.capitalize() if ENV != "prod" else db_core_start_string

db_core = async_mongo_client[db_core_name]
sync_db_core = sync_mongo_client[db_core_name]
db_member_enroll = async_mongo_client[db_name_member_enroll]
db_onboarding = async_mongo_client[onboarding_db]
sync_db_onboarding = sync_mongo_client[onboarding_db]

samx_one_db_url = os.getenv("SAMX_ONE_DB_URL")
samx_one_db_name = os.getenv("SAMX_ONE_DB_NAME")
samx_one_client = AsyncIOMotorClient(samx_one_db_url)
samx_one_db = samx_one_client[samx_one_db_name]

_db_cache = {}
_db_cache_lock = asyncio.Lock() 

async def get_client_db_string(client_id: str):
    return db_name_start + "_" + client_id + "_" + ENV.capitalize() if ENV != "prod" else db_name_start + "_" + client_id

async def get_client_database_connection(client_id: str):  
    async with _db_cache_lock:  
        if client_id in _db_cache:  
            return _db_cache[client_id]  
          
        db_name = await get_client_db_string(client_id)  
        db_conn = async_mongo_client[db_name]  
        _db_cache[client_id] = db_conn  
          
        return db_conn 
