from client_plugins.samx_one.samx_one_utils.utils import extract_census, update_samx_one_quote
from typing import Optional
from fastapi import APIRouter, Form, UploadFile, File
from fastapi.responses import JSONResponse
import traceback
from services import log_error_to_db_service
from utils.helpers.constants import OEC_SAMXONE_CLIENT_ID

router = APIRouter()

@router.post("/samxone/upload/census")
async def upload_census_to_quote(quoteId: Optional[str] = Form(...), file: Optional[UploadFile] = File(None), env: Optional[str] = Form("")):
    try:
        # Check if file is provided
        if not file:
            return JSONResponse(
            content={"status": "success", "message": "Census file is not provided. Skipping update."},
            status_code=200
        )
            
        extracted_census = await extract_census(file)
        census_update = {"employees": extracted_census}
        await update_samx_one_quote(quoteId, census_update,env)
        return JSONResponse(
            content={"status": "success", "message": "Census updated successfully"},
            status_code=200
        )
    except Exception as e:
        traceback.print_exc()
        await log_error_to_db_service.log_error_to_db(traceback.format_exc(), OEC_SAMXONE_CLIENT_ID, "N/A", "FileUploadError")
        return JSONResponse(
            content={"status": "error", "message": "An error occurred during census file upload."},
            status_code=500
        )