import traceback
from config.db import get_client_database_connection
from utils.general_utils import getTime
import pymongo

async def get_case_id_from_counter(client_id):
    try: 
        client_db = await get_client_database_connection(client_id) # to-pass-clientid
        current_time = getTime()
        collectionName = "UspCaseIdCounter"
        collection = client_db[collectionName]
        query = {"caseId": {"$lt": 999999999}}
        update = {"$inc": {"caseId": 1}}
        options = pymongo.ReturnDocument.AFTER
        res = await collection.find_one_and_update(query, update, return_document=options)

        # Reset the case-id 
        if res is None:   
            await collection.insert_one({"caseId": 1})   
            case_id = 1  
        else:  
            case_id = res['caseId']

        case_id = res['caseId']
        return case_id 
    except Exception as e:
        print(e)
        raise Exception("Facing issues to generate CASE-ID. Please try later.")

async def install_memberData(uuid, client_id, file_type, file_data, response):
    try:
        client_db = await get_client_database_connection(client_id) 
        current_time = getTime()
        collectionName = "EnrollmentForm"
        collection = client_db[collectionName] 
        file_data = file_data
        data = {
                    "create_timestamp": current_time,
                    "update_timestamp": current_time,
                    "uuid": uuid,
                    "client_id": client_id,
                    "file_type": file_type,
                    "file_buffer": file_data,
                    "json_response": response
                }
        await collection.insert_one(data)
        errorMsg = ""
    except Exception as e:
        errorMsg = str(traceback.format_exc())
        print(errorMsg)

async def log_heavy_weight_data(uuid, client_id, caseId, grpNumber, requestHW, responseHW):
    try:
        client_db = await get_client_database_connection(client_id) # to-pass-clientid
        current_time = getTime()
        collectionName = "UspTransactions"
        collection = client_db[collectionName] 
        data = {
                    "create_timestamp": current_time,
                    "update_timestamp": current_time,
                    "case_id": caseId,
                    "uuid": uuid,
                    "client_id": client_id,
                    "member_group_id": grpNumber,
                    "member_hw": {
                        "request": requestHW,
                        "response": responseHW
                    }
                }
        await collection.insert_one(data)
    except Exception as e:
        errorMsg = str(traceback.format_exc())
        print(errorMsg)
