from datetime import date
import os, time
import json
import httpx
from config import jwt
from utils.schemas.mapping.cirrus_member_individual_mapping import map_member_individual_json
from services.cirrus.cirrus_api_definitions import member_search_api
from services.log_cirrus_response_service import log_cirrus_response
from utils.schemas.cirrus_classes import *
from services.event_logger_service import logger
from utils.systemMetrics import SystemMetrics

async def member_search(input: CirrusMemberSearch):
    if not input.memGroupID or input.memGroupID.isspace():
        return "Member group not provided"

    response = await member_search_api(input)
    return response


async def individual_member_search(input: CirrusMemberSearch, memberId):
    url = os.getenv("CIRRUS_URL_BASE") + "/get-member-individual/v6.0"

    if not input.memGroupID or input.memGroupID.isspace():
        return "Member group not provided"

    request_body = {
        "memGroupID": input.memGroupID,
        **({"memberID": memberId} if memberId != "" else {}),
        **({"socialSecurityNumber": input.SSN} if input.SSN != "" else {}),
        "addressType": "HOME",
        "inquiryDate": str(date.today())
    }
    
    if  input.firstNameStartsWith and not input.firstNameStartsWith.isspace():
        request_body["firstNameStartsWith"] = input.firstNameStartsWith
        
    if input.lastNameStartsWith and not input.lastNameStartsWith.isspace():
        request_body["lastNameStartsWith"] = input.lastNameStartsWith

    data = json.dumps(request_body)
    jwt_token = jwt.get_token()
    headers = {"Authorization": "Bearer " + jwt_token, "Content-Type": "application/json"}
    metrics = SystemMetrics()
    event_name = f"cirrus_search_{input.uuid}_{time.time()}"
    async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
        with metrics.track_event(event_name) as event_data:
            response = await client.post(url, data=data, headers=headers)
            status_code = response.status_code
            response_data = response.json()

            if (isinstance(response_data.get('responseData'), dict)) and isinstance(response_data.get('responseData').get('members'), dict):
                final_response = map_member_individual_json(response_data)
            else:
                meta_info = response_data.get('metaInformation')
                if isinstance(meta_info.get('responseMessageList'), list):
                    error_data = meta_info.get('responseMessageList')
                    if len(error_data) >= 1 and error_data[0].get('type') == 'ERROR':
                        final_response = error_data[0].get('message') + f" for the provided data - Member Group ID: {input.memGroupID}, First Name: {input.firstNameStartsWith or None}, Last Name: {input.lastNameStartsWith or None}, SSN: {input.SSN or None}."
                else:
                    final_response = 'NA'

        system_metrics = metrics.get_event_metrics(event_name)
        event_details = {
            "method": "POST",
            "url": url,
            "Status Code": status_code,
            "system_metrics": system_metrics
        }

        if str(status_code).startswith('2'):
            await logger.info(input.uuid, input.user_name, input.session_id, input.request_id, input.clientId, "api", url, {"body": request_body, "header": headers}, response_data, status_code, None, event_details=event_details)
        else:
            await logger.error(input.uuid, input.user_name, input.session_id, input.request_id, input.clientId, "cirrus_individual_member_search", url, {"body": request_body, "header": headers}, None, status_code, response_data)

        await log_cirrus_response(input.clientId, "Individual member search", url, status_code, response_data, input.uuid, request_body)

        return final_response
