import json
from fastapi import HTTPException

def parse_additional_args(additional_arg: str, field_name: str = "additional_args") -> dict:
    """
    Parse the additional arguments from a string to a dictionary.
    
    Args:
        additional_arg (str): The string representation of additional arguments.
        field_name (str): The name of the field for error reporting.
        
    Returns:
        dict: Parsed additional arguments as a dictionary.
        
    Raises:
        HTTPException: If the parsing fails or if the input is invalid.
    """
    if not additional_arg:
        return {}
    try:
        return json.loads(additional_arg)
    except json.JSONDecodeError:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid JSON format for {field_name}. Please provide a valid JSON string."
        )