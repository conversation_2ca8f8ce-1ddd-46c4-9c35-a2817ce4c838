from config.db import get_client_database_connection
from utils.general_utils import getTime

async def update_theme_db(user_name, theme, client_id):
    client_db = await get_client_database_connection(client_id)
    theme_collection = client_db["UserPreference"]
    
    # Append The New Theme & Save It To The Database
    current_time = getTime()
    result = await theme_collection.find_one_and_update(
        {
            "user_name": user_name,
        },
        {
            "$set": {
                # "user_name": user_name,
                "theme": theme,
                "update_timestamp": current_time
            }
        }
    )
    # If The user_name Is Not Found, Insert A New Document
    if result is None:
            new_document = { 
                "user_name": user_name,
                "theme": theme,
                "create_timestamp": current_time, 
                "update_timestamp": current_time  
            }
            insert_result = await theme_collection.insert_one(new_document)
            if not insert_result.acknowledged:
                print("Failed To Insert New Theme")
            
async def get_preference(user_name, client_id):
    client_db = await get_client_database_connection(client_id)
    collection = client_db['UserPreference']
    query = {'user_name': user_name}  
    query_result = await collection.find_one(query)  
    current_time = getTime()
    if not query_result:  
        # If user_name is not found, insert a new document with 'theme': 'uhc'  
        new_document = {'theme': 'uhc', 'user_name': user_name, 'create_timestamp': current_time, 'update_timestamp': current_time}  
        await collection.insert_one(new_document)  
        preference = {'theme': 'uhc'}  
    else:  
        preference = {'theme': query_result.get('theme')}  
  
    return preference
