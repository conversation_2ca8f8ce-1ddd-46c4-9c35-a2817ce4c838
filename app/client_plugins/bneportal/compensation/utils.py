import httpx
from typing import Annotated
from langgraph.prebuilt.tool_node import InjectedState
import json
import os

TRY_ANOTHER_PROMPTS = "View Compensation Prompts"


# def get_logger(name=__name__):
#     import logging
#     logging.basicConfig(
#         level=logging.INFO,
#         format="[**line %(lineno)d] %(message)s"
#     )
#     return logging.getLogger(name)

def get_logger(name=__name__):
    import logging
    import sys
    
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)
    
    # Prevent duplicate handlers if logger already configured
    if not logger.handlers:
        # Create console handler
        handler = logging.StreamHandler(sys.stdout)
        handler.setLevel(logging.INFO)
        
        formatter = logging.Formatter(
            '[%(funcName)s-line %(lineno)d] %(message)s'
        )
        handler.setFormatter(formatter)
        
        # Add handler to logger & Prevent propagation to root logger to avoid duplicate messages
        logger.addHandler(handler)
        logger.propagate = False
    
    return logger

logger = get_logger(__name__)

def check_pcis_id_authorization(state: Annotated[dict, InjectedState], pcis_input: str) -> bool:
    pcis_state = state.get("additional_arg", {}).get("bne_user_pcis")
    user_type = state.get("additional_arg", {}).get("bne_user_type")
    logger.info(f"check_pcis_id_auth-- user: {user_type}, pcis_input: {pcis_input}, pcis_state: {pcis_state}")

    if user_type == "Broker":
        if pcis_input == None and pcis_state != "":
            return user_type, True, pcis_state
        elif pcis_input == None and pcis_state == "":
            return user_type, False, "no_pcis"
        elif pcis_input == pcis_state:
            return user_type, True, pcis_state
        else:
            return user_type, False, ""
    elif user_type == "Internal":
        if pcis_input == None and pcis_state == "":
            return user_type, False, "no_pcis"
        # if user doesnt enter a pcis id, then use the current pcis id from state
        elif pcis_input == None and pcis_state:
            return user_type, True, pcis_state
        # if user enters a pcis id, just use provided pcis id
        elif pcis_input:
            return user_type, True, pcis_input
        else:
            return user_type, False, ""
    else:
        return "", False, ""


async def validate_cust_crid(pcis_id: str, customer_id:str) -> dict:
    logger.info(f"Inside get_crid_details tool... pcis_id: {pcis_id}, customer_id: {customer_id}")

    api_url = os.getenv("CRID_DETAILS_ENDPOINT")
    headers = {
        "Content-Type": "application/json"
    }
    payload = {
        "request": {
            "findProducerRequest": {
                "producerID": pcis_id,
                "producerSSN": None,
                "producerTIN": None,
                "producerFirstName": None,
                "producerMiddleInitial": None,
                "producerLastName": None,
                "producerCompanyName": None,
                "producerState": None,
                "producerStatus": None,
                "legacyProducerSystem": None,
                "legacyProducerID": None,
                "customerNumber": customer_id,
                "adminSystem": "",
                "customerName": None
            },
            "serviceAttributes": {
                "applicationIdentifier": "compagent",
                "requestIdentifier": "",
                "requestedDate": None,
                "producerGroupAssociation": "present",
                "producerAndGroupFlag": "Y",
                "pageNumber": 1,
                "recordsPerPage": 50,
                "totalNumberOfRecords": None,
                "responseCode": 0,
                "responseDescription": ""
            }
        }
    }
    try:
        response = await post_with_cert(api_url, payload)
        if response.status_code == 200:
            response_json = response.json()
 
            if response_json.get('errorMsg'):
                message = f"No CRID details found for PCIS ID {pcis_id}. Please verify the PCIS ID and try again."
                return {
                    "answer": message,
                    "follow_up_questions": ["Try with another PCIS ID",TRY_ANOTHER_PROMPTS]
                }
 
            cridDetailsData = response_json.get("response", {}).get("findProducerResponse", [])
            broker_crid_list = [item.get("producerCRID") for item in cridDetailsData if item.get("producerCRID") ]
 
            # Explicitly check if data exists (not empty array or None)
            if broker_crid_list and len(broker_crid_list) > 0:
                valid_association = True
                valid_customer = customer_id

                return {
                   "valid_association": valid_association,
                   "valid_customer": valid_customer,
                   "crid_list": broker_crid_list
                }
            else:
                logger.info("Can't find valid association of PCIS ID and Customer Number. Please try with different Customer number")
                return {
                    "valid_association": False,
                    "valid_customer": "",
                }
        else:
            status_message = f"Unable to retrieve CRID details. Status code: {response.status_code}. Please try again later."
            return {
                "answer": status_message,
                "follow_up_questions": ["Try with another PCIS ID",TRY_ANOTHER_PROMPTS]
            }
    except Exception as e:
        logger.error(f"Error in validate_cust_crid: {e}")
        status_message = "I am unable to retrieve CRID details at this time. Please try at a later time."
        return {
            "answer": status_message,
            "follow_up_questions": [TRY_ANOTHER_PROMPTS]
        }
    
def get_followup_questions(questions: list, data: list, tool: str) -> list:
    
    # Set flags based on customer data
    has_comm_rates = data[0].get("commissionRateInd" , False) is True
    has_sfa_ind = data[0].get("serviceFeeAgrInd" , False) is True
    has_override = data[0].get("override", False) is True
    has_manual_holds = data[0].get("hold", False) is True

    logger.info(f"has_comm_rates: {has_comm_rates}, has_sfa_ind: {has_sfa_ind}, has_override: {has_override}, has_manual_holds: {has_manual_holds}")
    
    if has_comm_rates and tool != "commission_rates":
        questions.append("View Commission Rates")
    if has_sfa_ind and tool != "service_fee_agreement":
        questions.append("View Service Fee Agreement")
    if has_override and tool != "customer_override":
        questions.append("View Customer Override")
    if has_manual_holds and tool != "manual_holds":
        questions.append("View Manual Holds")
    
    questions.append(TRY_ANOTHER_PROMPTS)
    return questions


async def get_customer_validation_results(state: Annotated[dict, InjectedState], customer_id: str, pcis_id: str) -> dict:
    logger.info("---------------------------------------------------------------")
    valid_state = state.get("additional_arg", {}).get("bne_pcis_cust_validation")
    logger.info(f"Valid State*****: {valid_state}, Customer ID: {customer_id}")
    logger.info("---------------------------------------------------------------")

    if valid_state and valid_state.get("valid_association") and customer_id == state.get("additional_arg", {}).get("bne_pcis_cust_validation", {}).get("valid_customer"):
        logger.info(f"Using valid_state from state: {valid_state}")
        validation_results = valid_state
    elif valid_state and valid_state.get("valid_association") and customer_id != state.get("additional_arg", {}).get("bne_pcis_cust_validation", {}).get("valid_customer"):
        logger.info("Since diff customer so calling validation fn again****")
        validation_results = await validate_cust_crid(pcis_id, customer_id)
    elif not valid_state or not valid_state.get("valid_association"):
        logger.info("No valid_state found or invalid association, so using validation fn****")
        validation_results = await validate_cust_crid(pcis_id, customer_id)

    logger.info(f"validation_results updated ******** {validation_results}")
    logger.info("---------------------------------------------------------------")
    logger.info(f"customer_id: {customer_id}")
    logger.info(f"cust id from additional_arg: {state.get('additional_arg', {}).get('bne_pcis_cust_validation', {}).get('valid_customer')}")
    
    return validation_results 

async def post_with_cert(api_url: str, payload: dict, headers: dict = None) -> httpx.Response:
    """
    Send an async POST request with a PEM file as client certificate.
    Reads PEM file path from environment variable CLIENT_PEM_PATH.
    """
    
    if headers is None:
        headers = {"Content-Type": "application/json"}

    try:
        pem_path = os.getenv("BAS_PEM_PATH")
        logger.info(f"Using PEM path: {pem_path}")
        async with httpx.AsyncClient(verify=pem_path) as client:
            response = await client.post(
                api_url,
                headers=headers,
                json=payload
            )
        return response
    except httpx.RequestError as e:
        logger.error(f"Error calling API: {e}")


def filterResultsByCRID(data: any, cridList: list) -> list:
    if not data:
        return data
    
    if isinstance(cridList, dict) and 'crid_list' in cridList:
        crid_list = cridList.get('crid_list', [])
    else:
        crid_list = cridList
    
    if not crid_list:
        return data
    
    crid_set = {str(crid) for crid in crid_list}

    print("CRID set for filtering:", crid_set)
    print("Data before filtering:", data)
    
    try:
        if isinstance(data, dict):
            for  value in data.items():
                if isinstance(value, list):
                    indices_to_remove = []
                    for i, item in enumerate(value):
                        if isinstance(item, dict):
                            item_crid = str(item.get('crid', '')) if 'crid' in item else None
                            item_comp_rel_id = str(item.get('compRelId', '')) if 'compRelId' in item else None
                            
                            if (item_crid is not None or item_comp_rel_id is not None) and \
                               (item_crid not in crid_set and item_comp_rel_id not in crid_set):
                                indices_to_remove.append(i)

                    for index in reversed(indices_to_remove):
                        value.pop(index)
        
        elif isinstance(data, list):
            indices_to_remove = []
            for i, item in enumerate(data):
                if isinstance(item, dict):
                    item_crid = str(item.get('crid', '')) if 'crid' in item else None
                    item_comp_rel_id = str(item.get('compRelId', '')) if 'compRelId' in item else None
                    
                    if (item_crid is not None or item_comp_rel_id is not None) and \
                       (item_crid not in crid_set and item_comp_rel_id not in crid_set):
                        indices_to_remove.append(i)

            for index in reversed(indices_to_remove):
                data.pop(index)
                
        return data
            
    except Exception as e:
        print(f"Error in filterResultsByCRID: {e}")
        return data

