from config.db import get_client_database_connection

async def remove_convo(session_id, uuid, client_id):
    client_db = await get_client_database_connection(client_id)
    chat_collection = client_db['ChatConversation']
    titles_collection = client_db["ChatTitles"]
    if await chat_collection.count_documents({"session_id": session_id, 'uuid': uuid}, limit=1):
        delete_query = {"session_id": session_id}
        await chat_collection.delete_one(delete_query)  
    if await titles_collection.count_documents({'uuid': uuid}, limit=1):
        update_query = {"uuid": uuid}
        update_data = {"$pull": {"titles": {'session_id': session_id}}}
        await titles_collection.update_one(update_query, update_data)  
 
   
