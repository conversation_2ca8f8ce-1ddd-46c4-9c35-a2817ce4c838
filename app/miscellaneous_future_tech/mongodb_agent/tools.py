from langchain_core.tools import tool
from typing import Any, Dict, Optional
from pydantic import Field
import os
from motor.motor_asyncio import AsyncIOMotorClient

dbString = os.environ.get("MONGO_DB_URL")
dbName = os.environ.get("DB")
dbCollection = os.environ.get("COLLECTION")

@tool
async def mongo_query(
    query_request: Dict[str, Any] = Field(
        description="A JSON object containing relevant details such as filter, query_type, document, etc."  
    ),
    host: Optional[str] = dbString,
    database: Optional[str] = dbName,
    collection: Optional[str] = dbCollection
     ):
    """
    Executes a MongoDB query based on the query request from the LLM agent.

    Args:
        query_request: A JSON object containing relevant details such as filter, query_type, document, etc.

    Returns:
        The result of the MongoDB query.
    """

    client = AsyncIOMotorClient(host)

    db = client[database]
    coll = db[collection]

    if query_request["query_type"] == "find_one":
        result = await coll.find_one(query_request["filter"])
    elif query_request["query_type"] == "find":
        cursor = coll.find(query_request["filter"])
        result = await cursor.to_list(length=None)
    elif query_request["query_type"] == "update_one":
        result = await coll.update_one(query_request["filter"], query_request["update"])
    elif query_request["query_type"] == "update_many":
        result = await coll.update_many(query_request["filter"], query_request["update"])
    elif query_request["query_type"] == "insert_one": # only problem with insert query
        obj = await coll.insert_one({**query_request["document"]})
        result = "Document Inserted"
    elif query_request["query_type"] == "insert_many":
        await coll.insert_many({**query_request["document"]})
        result = "Documents Inserted"
    elif query_request["query_type"] == "delete_one":
        await coll.delete_one(query_request["filter"])
        result = "Document Removed"
    elif query_request["query_type"] == "delete_many":
        await coll.delete_many(query_request["filter"])
        result = "Documents Removed"
    else:
        raise Exception("Invalid query type")

    # Close the MongoDB client
    await client.close()

    return result

@tool
def fetch_vector_details(vectorQuery: str):
    """Searches for a member in cirrus.
    
    Args:
        vectorQuery: A tailored query to be used for vector search
    
    Returns:
        The result of the vector DB query.
    """
    ##
    # ... Add your vector DB related logic here 
    ##
    return f"The search was complete for the {vectorQuery}"
