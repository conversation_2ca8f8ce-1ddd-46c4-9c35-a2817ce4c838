OEC_DEFAULT_CLIENT_ID = "Internal"
OEC_SUREST_CLIENT_ID = "SAMx_LevelFunded"
OEC_SAMXONE_CLIENT_ID = "samx_one"
OEC_BNEPORTAL_CLIENT_ID = "bne_client"
OEC_BNEMOBILE_CLIENT_ID = "bne_mobile"
OEC_OPTUMAI_CLIENT_ID = "optum_ai"
OEC_GCOREPORTING_CLIENT_ID = "gco_reporting"

def get_json():
    DEFAULT_JSON = {
            "EffectiveDate": None,
            "QualifyingEffectiveDate": None,
            "ReasonForApplication": {
                "Reason": None
            },
            "GroupName": None,
            "GroupNumber": None,
            "EmployeeInfo": {
                "FirstName": None,
                "LastName": None,
                "MiddleName": None,
                "EmployeeType": None,
                "SSN": None,
                "NoSSNReason":None,
                "DOB": None,
                "Gender": None,
                "DateOfHire": None,
                "Address": None,
                "City": None,
                "State":None,
                "PostalCode": None,
                "Country": None,
                "HomePhone": None,
                "CellPhone": None,
                "WorkPhone": None,
                "EmailAddress": None,
                "PhysicianID": None,
                "ExistingPatientMedical": None,
                "PlanAndPopulationDetails": [],
                "billGroupList": None,
                "SelectedBillGroup":None,
            },
            "SpouseInfo": {
                "FirstName": None,
                "LastName": None,
                "MiddleName": None,
                "SSN": None,
                "NoSSNReason":None,
                "DOB": None,
                "Gender": None,
                "Address": None,
                "City": None,
                "State":None,
                "PostalCode": None,
                "Country": None,
                "PhysicianID": None,
                "ExistingPatientMedical": None,
                "PlanAndPopulationDetails": [],
                "billGroupList": [],
                "SelectedBillGroup":"",
            },
            "Dependent1Info": {
                "FirstName": None,
                "LastName": None,
                "MiddleName": None,
                "SSN": None,
                "NoSSNReason":None,
                "DOB": None,
                "Gender": None,
                "Address": None,
                "City": None,
                "State":None,
                "PostalCode": None,
                "Country": None,
                "PhysicianID": None,
                "ExistingPatientMedical": None,
                "PlanAndPopulationDetails": [],
                "billGroupList": [],
                "SelectedBillGroup":"",
            },
            "Dependent2Info": {
                "FirstName": None,
                "LastName": None,
                "MiddleName": None,
                "SSN": None,
                "NoSSNReason":None,
                "DOB": None,
                "Gender": None,
                "Address": None,
                "City": None,
                "State":None,
                "PostalCode": None,
                "Country": None,
                "PhysicianID": None,
                "ExistingPatientMedical": None,
                "PlanAndPopulationDetails": [],
                "billGroupList": [],
                "SelectedBillGroup":"",
            },
            "Dependent3Info": {
                "FirstName": None,
                "LastName": None,
                "MiddleName": None,
                "SSN": None,
                "NoSSNReason":None,
                "DOB": None,
                "Gender": None,
                "Address": None,
                "City": None,
                "State":None,
                "PostalCode": None,
                "Country": None,
                "PhysicianID": None,
                "ExistingPatientMedical": None,
                "PlanAndPopulationDetails": [],
                "billGroupList": [],
                "SelectedBillGroup":"",
            },
            "Dependent4Info": {
                "FirstName": None,
                "LastName": None,
                "MiddleName": None,
                "SSN": None,
                "NoSSNReason":None,
                "DOB": None,
                "Gender": None,
                "Address": None,
                "City": None,
                "State":None,
                "PostalCode": None,
                "Country": None,
                "PhysicianID": None,
                "ExistingPatientMedical": None,
                "PlanAndPopulationDetails": [],
                "billGroupList": [],
                "SelectedBillGroup":"",
            },
            "OtherMedCovInfo": {
                "OtherCVG": None,
                "NameOfOtherCarrier": None,
                "MedicarePartA": None,
                "MedicarePartB": None,
                "MedicarePartD": None,
                "ReasonforMedEligibility": None,
                "MedicareS/DFirstName": None,
                "MedicareS/DLastName": None,
                "MedicareS/DPartA": None,
                "MedicareS/DPartB": None,
                "MedicareS/DPartD": None,
                "ReasonforS/DMedEligibility": None
            },
            "ProductSelection": {
                "MedicalPlanName": None
            }
        }
    return DEFAULT_JSON

TOOL_CALL_ERROR_MESSAGE = "There has been a technical error. Please try again."