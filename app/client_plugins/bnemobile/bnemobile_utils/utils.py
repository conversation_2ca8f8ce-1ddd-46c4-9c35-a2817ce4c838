
# utils.py
# Add your utility functions here.
from config.jwt import get_token

def create_header():
    jwt_token = get_token()
    headers = {  
        "Authorization": "Bearer " + jwt_token, 
        "Content-Type": "application/json"  
    } 

    return headers
    
def createFilters(userInput:dict):
    ret = []
    for key in userInput.keys():
        if key == "companyName" and userInput[key]!="":
            ret.append(
                {
                    "id": key,
                    "value":{
                        "condition":"contains",
                        "filterValue":userInput[key],
                    },
                    "operator":'AND',
                }
            )
        elif userInput[key]!="":
            ret.append(
                {
                    "id": key,
                    "value":{
                        "condition":"equals",
                        "filterValue":userInput[key],
                    },
                    "operator":'AND',
                }
            )
    return ret
