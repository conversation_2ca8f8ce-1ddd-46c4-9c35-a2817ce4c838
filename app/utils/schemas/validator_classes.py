from typing import List, Optional
from pydantic.v1 import validator
from pydantic import Field, BaseModel
import re

class PlanAndPopulationDetailValidator(BaseModel):
    planList: List[str]
    productType: str = Field(..., pattern='^(medical|dental|vision|std|ltd|life|deplife)$')
    selectedCoverageOptionId: str
    selectedPopulation: Optional[str] = ""
    
    @validator('planList')  
    def check_plan_list(cls, value):  
        if len(value) != 1:  
            raise ValueError("planList must contain exactly one element.")  
        plan = re.sub(r'\s+', '', value[0])
        if plan.lower() == "waive":
            return [plan]   
        plan_pattern = re.compile(r'^.+-.+$')  
        if not plan_pattern.match(plan):  
            raise ValueError(f"planList element must contain a '-' separating two parts but got - {plan}")   
        return [plan]
    
    @validator('selectedCoverageOptionId')  
    def validate_selected_coverage_option_id(cls, value, values):  
        if 'planList' in values and values['planList']:  
            plan = values['planList'][0].strip()
            if plan.lower() == "waive":
                if value.strip().lower() != "waive":
                    raise ValueError("selectedCoverageOptionId must be 'Waive' when planList contains 'Waive'")
                return value.strip() 
            
            numeric_part = values['planList'][0].split('-')[0]  
            if not re.fullmatch(f'^{numeric_part.strip()}$', value.strip()):  
                raise ValueError(f'selectedCoverageOptionId - {value.strip()} must match the numeric part - {numeric_part.strip()} of the first planList item')  
        return value.strip()
    
class DependentsListValidator(BaseModel):
    FirstName: str
    LastName: str
    SSN: str
    DOB: str
    Gender: str
    Type: str = Field(..., pattern='(?i)^(spouse|child)$')
