import os
from langchain_openai import AzureChatOpenAI, AzureOpenAIEmbeddings
from auth.openAI_auth import get_openai_token

def create_resource():
    if os.environ["OPENAI_RESOURCE_FLAG"] == "TOKEN":
        return AzureChatOpenAI(
            model=os.environ["AZURE_OPENAI_CHAT_MODEL_NAME_TOKEN"],
            api_version=os.environ["OPENAI_API_VERSION_TOKEN"],
            azure_deployment=os.environ["AZURE_OPENAI_CHAT_DEPLOYMENT_NAME_TOKEN"],
            azure_endpoint=os.environ["AZURE_OPENAI_ENDPOINT_TOKEN"],
            azure_ad_token_provider=get_openai_token,
            default_headers={ 
                "projectId": os.environ["OPENAI_PROJECT_ID"]
            },
            temperature = 0
        )
    elif os.environ["OPENAI_RESOURCE_FLAG"] == "KEY":
        return AzureChatOpenAI(
            model=os.environ["AZURE_OPENAI_CHAT_MODEL_NAME_KEY"],
            api_version=os.environ["OPENAI_API_VERSION_KEY"],
            azure_deployment=os.environ["AZURE_OPENAI_CHAT_DEPLOYMENT_NAME_KEY"],
            azure_endpoint=os.environ["AZURE_OPENAI_ENDPOINT_KEY"],
            temperature = 0
        )
    
def create_embeddings():
    if os.environ["OPENAI_RESOURCE_FLAG"] == "TOKEN":
        return AzureOpenAIEmbeddings(
            model=os.environ["AZURE_OPENAI_EMBEDDINGS_TOKEN"],
            azure_ad_token_provider=get_openai_token,
            azure_endpoint=os.environ["AZURE_OPENAI_ENDPOINT_TOKEN"],
            default_headers={ 
                "projectId": os.environ["OPENAI_PROJECT_ID"]
            }
        )
    elif os.environ["OPENAI_RESOURCE_FLAG"] == "KEY":
        return AzureOpenAIEmbeddings(
            model=os.environ["AZURE_OPENAI_EMBEDDINGS_KEY"],
            azure_endpoint=os.environ["AZURE_OPENAI_ENDPOINT_KEY"]
        )

model = create_resource()

embeddings = create_embeddings()

json_model = model.bind(response_format={"type": "json_object"})
