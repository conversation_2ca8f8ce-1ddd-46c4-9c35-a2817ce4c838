---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: lingo-api-allow-all-egress
spec:
  podSelector:
    matchLabels:
      app: lingo-api
  egress:
    - ports:
        - protocol: TCP
          port: 1521
        - protocol: TCP
          port: 2200
        - protocol: TCP
          port: 22
        - protocol: TCP
          port: 8200
        - protocol: TCP
          port: 443
        - protocol: TCP
          port: 27017
        - protocol: TCP
          port: 3306
        - protocol: TCP
          port: 80
        - protocol: TCP
          port: 389
        - protocol: TCP
          port: 25
        - protocol: TCP
          port: 19530
        - protocol: TCP
          port: 5432
  policyTypes:
    - Egress
