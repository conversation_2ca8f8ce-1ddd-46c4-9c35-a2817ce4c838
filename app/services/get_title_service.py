from config.db import get_client_database_connection

async def get_title(uuid, client_id):
    client_db = await get_client_database_connection(client_id)
    collection = client_db['ChatTitles']
    title_list = []
    query = {'uuid': uuid}

    async for object in collection.find(query):
        for item in object['titles']:
            title_list.append(item)
    return {"status": "success", "titles": title_list}
