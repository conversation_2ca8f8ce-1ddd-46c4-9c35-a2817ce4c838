from utils.helpers.mappings import MARKET_SEGMENT_MAPPING, PRODUCER_TYPE_MAPPING

def get_index_value(lst, search_key, search_val, search_key2=None, search_val2=None):
    index_val = None
    if (isinstance(lst, list)):
        for check_index in range(len(lst)):
            if (lst[check_index].get(search_key)==search_val and lst[check_index].get(search_key2)==search_val2):
                    index_val= check_index               
    return index_val

def get_nested(data, keys, default=None, index=0):  
    for key in keys:  
        if isinstance(data, list):  
            data = data[index] if data and index!=None else default  
        if isinstance(data, dict):  
            data = data.get(key, default)  
        else:  
            return default  
    return data  
def mapping(input_json):
    output_json = {
        "groupInformation": {},
        "groupAddress": {},
        'benefitAdministratorContactInformation': {},
        "brokerInformation": {},  
    }
  
    # Group Information
    output_json["groupInformation"]["groupName"] = get_nested(input_json, ["memGroupName"]) 
    output_json["groupInformation"]["groupNumber"] = get_nested(input_json, ["memGroupID"]) 
    output_json["groupInformation"]["platform"] = "Cirrus" 
    output_json["groupInformation"]["smallGroupIndicator"] = "Yes" if get_nested(input_json, ["smallGroupInd"])==True else "No" if get_nested(input_json, ["smallGroupInd"])==False else None
    output_json["groupInformation"]["holdEligibility"] = "Yes" if get_nested(input_json, ["holdElig"])==True else "No" if get_nested(input_json, ["holdElig"])==False else None
    output_json["groupInformation"]["employerGroupReportingFlag"] = "Yes" if get_nested(input_json, ["employerGroupReportingFlag"])==True else "No" if get_nested(input_json, ["employerGroupReportingFlag"])==False else None 
    output_json["groupInformation"]["situsState"] = get_nested(input_json, ["addressList", "state"])
    output_json["groupInformation"][get_nested(input_json, ["sourceSystemIDList", "originalSourceSystemType" ], default='pulse').lower() + "ID"] = get_nested(input_json, ["sourceSystemIDList", "originalSourceSystemID" ])
    output_json["groupInformation"]["sic"] = get_nested(input_json, ["industryClassCode"]) if get_nested(input_json, ["industryClassCodeType"]) =="SIC" else None
    output_json["groupInformation"]["taxID"] = get_nested(input_json, ['memGroupTinList', 'tin'])
    output_json["groupInformation"]["marketSegment"] = MARKET_SEGMENT_MAPPING.get(get_nested(input_json, ['memGroupReportingCodeList', 'reportingCodeType'], None))
    #GroupAddress
    address_list= input_json.get('addressList',None)
    address_index = get_index_value(address_list, 'addressType','Physical_Address')  
    output_json["groupAddress"]["addressType"] = get_nested(input_json, ["addressList", "addressType"], index=address_index) 
    output_json["groupAddress"]["addressLine1"] = get_nested(input_json, ["addressList", "address1"], index=address_index) 
    output_json["groupAddress"]["addressLine2"] = get_nested(input_json, ["addressList", "address2"], index=address_index) 
    output_json["groupAddress"]["city"] = get_nested(input_json, ["addressList", "city"], index=address_index) 
    output_json["groupAddress"]["state"] = get_nested(input_json, ["addressList", "state"], index=address_index) 
    output_json["groupAddress"]["zip"] = get_nested(input_json, ["addressList", "postalCode"], index=address_index) 

    #benefitAdministratorContactInformation
    contact_list= input_json.get('contactList',None)
    contact_index = None
    if (isinstance(contact_list, list)):
        for contact in range(len(contact_list)):
            contact_role_list = contact_list[contact].get('contactRoleList')
            contact_role_index = get_index_value(contact_role_list, 'roleType', 'Benefit_Administrator','preferredInd', True)
            if (contact_role_index !=None):
                contact_index = contact
                break
    if contact_index!=None:
        contact_address_list= contact_list[contact_index].get('addressList',None)
        contact_address_index = get_index_value(contact_address_list, 'addressType','Physical_Address') 
        ecomm_list = contact_list[contact_index].get('ecommList',None)
        ecomm_index = get_index_value(ecomm_list, 'communicationType','EML') 
        contact_json = input_json['contactList'][contact_index]
    else: 
        contact_address_index = None
        ecomm_index = None
        contact_json = None    
    output_json["benefitAdministratorContactInformation"]["firstName"] = get_nested(contact_json, [ "nameFirst"], index=contact_index) 
    output_json["benefitAdministratorContactInformation"]["lastName"] = get_nested(contact_json, ["nameLast"], index=contact_index) 
    output_json["benefitAdministratorContactInformation"]["addressType"] = get_nested(contact_json, ["addressList", "addressType"], index=contact_address_index) 
    output_json["benefitAdministratorContactInformation"]["addressLine1"] = get_nested(contact_json, ["addressList", "address1"], index=contact_address_index) 
    output_json["benefitAdministratorContactInformation"]["addressLine2"] = get_nested(contact_json, ["addressList", "address2"], index=contact_address_index) 
    output_json["benefitAdministratorContactInformation"]["city"] = get_nested(contact_json, ["addressList", "city"], index=contact_address_index) 
    output_json["benefitAdministratorContactInformation"]["state"] = get_nested(contact_json, ["addressList", "state"], index=contact_address_index) 
    output_json["benefitAdministratorContactInformation"]["zip"] = get_nested(contact_json, ["addressList", "postalCode"], index=contact_address_index) 
    output_json["benefitAdministratorContactInformation"]["email"] = get_nested(contact_json, ['ecommList','communicationText'], index=ecomm_index)     
    # Adding Broker Information  
    broker_list = input_json.get('contractList', [{}])[0].get('producerList', [])  
    if broker_list:  
        broker_info_list = []
        pcis_id_list =[]
        for broker in broker_list:
            name = (broker.get('nameFirst', "") + " " + broker.get('nameLast', "")).strip()  
            if not name:  
                name = broker.get('organizationName', None)  
          
            pcis_id = None  
            source_system_list = broker.get('sourceSystemList', [])  
            for source in source_system_list:  
                if source.get('originalSourceSystemType') == "PCIS":  
                    pcis_id = source.get('originalSourceSystemID', None)  
                    break  
            if pcis_id not in pcis_id_list: 
                broker_info = {  
                    'name': name,  
                    'pcisId': pcis_id,  
                    'cirrusId': broker.get('producerID', None),  
                    'producerType': PRODUCER_TYPE_MAPPING.get(broker.get('producerType', ""), None)  
                }
                broker_info_list.append(broker_info)
                pcis_id_list.append(pcis_id)
          
        output_json['brokerInformation'] = broker_info_list  
    else:  
        output_json['brokerInformation'] = [{  
            'name': None,  
            'pcisId': None,  
            'cirrusId': None,  
            'producerType': None  
        }]   
        
    return output_json  
