 
from langchain_core.prompts import ChatPromptTemplate
from services.get_prompts_service import master_prompt_document, get_prompt_by_assistant_name

sbc_retrieval_agent_assistant_prompt_document = get_prompt_by_assistant_name("sbc_retrieval_agent")

if not sbc_retrieval_agent_assistant_prompt_document:
    raise ValueError("sbc_retrieval_agent Assistant Prompt Not Found")

master_prompt = master_prompt_document.get("masterPrompt")
assistant_prompt = sbc_retrieval_agent_assistant_prompt_document.get("assistantPrompt")
system_prompt = ("system", master_prompt + assistant_prompt)
sbc_retrieval_agent_system_prompt = ChatPromptTemplate.from_messages([system_prompt, ("placeholder", "{messages}")]).partial() 
