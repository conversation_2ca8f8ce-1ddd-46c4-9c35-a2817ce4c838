"""
This file contains the payload formatter utility functions used in the Lingo_OEC_APIGateway application.

These functions are responsible for formatting the payload by applying a series of transformations.
"""

from cirrus.cirrus_install_plugin.plugin_utility.review_json_formatter_utility import *
from utils.helpers.constants import get_json

def format_payload(base_payload):
    """
    Formats the given payload by applying a series of transformations.

    Args:
        base_payload (dict): The base payload to be formatted.

    Returns:
        dict: The formatted payload.

    """
    # may not be required in current flow
    fixedJson = get_json() 

    finalJson = recheck_json_payload(base_payload, fixedJson)
    # may not be required in current flow 
    
    adjustedResponse = booleanize_checkboxes(finalJson) 
    
    adjustedResponse= field_adjustments(adjustedResponse) 
    
    adjustedResponse = gender_to_letter(adjustedResponse)
    
    adjustedResponse= remove_hyphens_in_physicianId(adjustedResponse)
    
    adjustedResponse = reason_reMapping(adjustedResponse)
    
    adjustedResponse = stringToNone(adjustedResponse)
    
    adjustedResponse = format_phone_and_ssn(adjustedResponse)

    return adjustedResponse