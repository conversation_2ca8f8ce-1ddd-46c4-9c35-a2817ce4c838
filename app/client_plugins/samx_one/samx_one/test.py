import sys
import os
import asyncio
sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..', '..', '..')))
from dotenv import load_dotenv
load_dotenv()
from utils.tool_utils import _print_event
import uuid
# from client_plugins.samx_one.samx_one.main import samx_one_builder
from client_plugins.samx_one.samx_one.samxone_subgraph import init_samx_one_graph
from config.postgres import init_auto_commit_pool, init_connection_pool, close_connection_pools
from graph.state import get_postgres_checkpointer
from utils.subgraph_registry import SubgraphRegistry

async def start_samx_one_graph():
    if SubgraphRegistry.get("samx_one") is None:
        await init_connection_pool()
        await init_auto_commit_pool()
        await get_postgres_checkpointer()
        await init_samx_one_graph()
    samx_one_graph = SubgraphRegistry.get("samx_one")
    return samx_one_graph

_printed = set()
async def run_agent(queryString, session_id, graph, user_name=None, uuid=None, additional_arg=None):
    additional_arg = additional_arg or {}
    thread_id = session_id
    config = {
        "configurable": {
            "thread_id": thread_id,
        }
    }
    user_info = {"client_id": "Internal", "uuid": uuid, "session_id": session_id, "user_name": user_name}

    events = graph.astream(
        {
            "messages": ("user", queryString),
            "user_info": user_info,
            "is_multiagent": False,
            "is_auth_performed": False,
            "additional_arg": additional_arg,
            "run_mode": "test"
        },
        config,
        stream_mode="values"
    )
    async for event in events:
        response = _print_event(event, _printed)
    return response

async def main():
    # Set up the graph once at the beginning
    graph = await start_samx_one_graph()
    thread_id = str(uuid.uuid4())
    try:
        while True:
            input_text = input("Enter your question or press 'q' to exit: ")
            if input_text.lower() in ["quit", "exit", "q"]:
                print("Goodbye! Have a nice day!")
                print("Exiting...")
                break
            response = await run_agent(input_text, thread_id, graph)
    finally:
        await close_connection_pools()

if __name__ == "__main__":
    asyncio.run(main())