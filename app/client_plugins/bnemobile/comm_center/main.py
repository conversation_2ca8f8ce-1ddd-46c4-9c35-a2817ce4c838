
from client_plugins.bnemobile.comm_center.tool import retrieve_pend_reasons,resolve_pend_reason,retrieve_pend_notes,create_pend_note,retrieve_quoteId

from graph.state import State
from graph.nodes import Assistant
from config.openAI import model
from langgraph.graph import END, StateGraph, START
from langgraph.prebuilt import tools_condition

from langgraph.prebuilt import ToolNode
from tools.common_tools import CompleteOrEscalate
from utils.helpers.constants import TOOL_CALL_ERROR_MESSAGE
from client_plugins.bnemobile.comm_center.prompts import comm_center_system_prompt
# Import the other required modules


comm_center_tools = [retrieve_pend_reasons,resolve_pend_reason,retrieve_pend_notes,create_pend_note,retrieve_quoteId] + [CompleteOrEscalate]
comm_center_tool_node = ToolNode(comm_center_tools, handle_tool_errors = TOOL_CALL_ERROR_MESSAGE)
comm_center_runnable = comm_center_system_prompt | model.bind_tools(comm_center_tools)