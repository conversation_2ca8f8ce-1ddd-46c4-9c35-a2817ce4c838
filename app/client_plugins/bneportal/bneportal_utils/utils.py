# utils.py
# Add your utility functions here.
import io
from config.openAI import embeddings
from config.postgres import get_pg_connection, return_pg_connection
import pdfplumber
from langchain.text_splitter import RecursiveCharacterTextSplitter
from client_plugins.bneportal.bneportal_consts import *
from datetime import datetime
from langchain_core.messages import ToolMessage, AIMessage
from graph.state import State
from client_plugins.bneportal.bneportal_utils.endpoints import *
import httpx
import os
from json import JSONDecodeError
    
def embed_metadata_to_chunk(chunk, file_name):
    metadata_obj = [
        f"File Name: {file_name}"
    ]
    metadata_string = "\n".join(metadata_obj)

    chunk_with_metadata = f"{metadata_string}\n\n{chunk}"
    return chunk_with_metadata

async def create_or_update_bne_training_material(file, file_name, file_url, file_category, user_type,  broker_training, employer_training, internal_training):
    # Get a connection from the pool
    pg_connection = await get_pg_connection()
    cursor = pg_connection.cursor()

    table_name = get_table_name_based_on_user_type(user_type, 'Training Materials')

    # Create table if it doesn't exist 
    create_table_query = f"""
    CREATE TABLE IF NOT EXISTS {table_name} (
        id SERIAL PRIMARY KEY,
        file_name TEXT,
        file_url TEXT,
        file_category TEXT,
        broker_training TEXT DEFAULT 'false',
        employer_training TEXT DEFAULT 'false',
        internal_training TEXT DEFAULT 'false',
        page_content TEXT,
        embedding vector(1536), 
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    """
    await cursor.execute(create_table_query)
    await pg_connection.commit()

    # Read the PDF file
    pdf_buffer = io.BytesIO(await file.read())
    pdf_text = ""
    with pdfplumber.open(pdf_buffer) as pdf:
        for page in pdf.pages:
            pdf_text += page.extract_text()

    # Split the docs into chunks
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=1000,
        chunk_overlap=200,
        separators=["\n\n","\n", "", ""]
    )
    # Assuming text_splitter is an instance of a text splitting class
    chunks = text_splitter.split_text(pdf_text)
    
    # delete the existing docs for file_name
    await delete_bne_training_material(table_name=table_name, file_name=file_name)

    # Prepare the data for batch insertion
    data_to_insert = []
    for chunk in chunks:
        chunk = embed_metadata_to_chunk(chunk, file_name)
        embedding = await embeddings.aembed_query(chunk)
        data_to_insert.append((file_name, file_url, file_category,  broker_training, employer_training, internal_training, chunk, embedding))

    # Batch insert the data
    insert_query = f"""
    INSERT INTO {table_name} (file_name, file_url, file_category, broker_training, employer_training, internal_training, page_content, embedding, created_at)
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
    """
    await cursor.executemany(insert_query, data_to_insert)
    await pg_connection.commit()
    await cursor.close()
    await return_pg_connection(pg_connection)

async def create_or_update_bne_training_material_for_batch(file, file_name, file_url, file_category, user_type, broker_training, employer_training, internal_training):
    pg_connection = await get_pg_connection()
    cursor = pg_connection.cursor()

    table_name = get_table_name_based_on_user_type(user_type, 'Training Materials')

    # Create table if it doesn't exist
    create_table_query = f"""
    CREATE TABLE IF NOT EXISTS {table_name} (
        id SERIAL PRIMARY KEY,
        file_name TEXT,
        file_url TEXT,
        file_category TEXT,
        broker_training TEXT DEFAULT 'false',
        employer_training TEXT DEFAULT 'false',
        internal_training TEXT DEFAULT 'false',
        page_content TEXT,
        embedding vector(1536), 
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    """
    await cursor.execute(create_table_query)
    await pg_connection.commit()

    # Read the PDF file
    pdf_buffer = io.BytesIO(await file.read())
    pdf_text = ""
    with pdfplumber.open(pdf_buffer) as pdf:
        for page in pdf.pages:
            pdf_text += page.extract_text()

    # Split the docs into chunks
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=1000,
        chunk_overlap=200,
        separators=["\n\n", "\n", "", ""]
    )
    chunks = text_splitter.split_text(pdf_text)

    await delete_bne_training_material(table_name=table_name, file_name=file_name)

    data_to_insert = []
    for chunk in chunks:
        chunk = embed_metadata_to_chunk(chunk, file_name)
        embedding = await embeddings.aembed_query(chunk)
        data_to_insert.append((file_name, file_url, file_category, broker_training, employer_training, internal_training, chunk, embedding))

    insert_query = f"""
    INSERT INTO {table_name} (file_name, file_url, file_category, broker_training, employer_training, internal_training, page_content, embedding, created_at)
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP) 
    """
    await cursor.executemany(insert_query, data_to_insert)
    await pg_connection.commit()
    await cursor.close()
    await return_pg_connection(pg_connection)

async def delete_bne_training_material(table_name, file_name):
    pg_connection = await get_pg_connection()
    cursor = pg_connection.cursor()
    # Pass file_name as a tuple
    fetch_query = """SELECT id FROM {} WHERE file_name = %s""".format(table_name)
    delete_query = """DELETE FROM {} WHERE id = ANY(%s)""".format(table_name)

    await cursor.execute(fetch_query, (file_name,))
    await pg_connection.commit()
    existing_records = await cursor.fetchall()
    
    if existing_records:
        # Collect IDs of the documents to delete
        ids_to_delete = [record[0] for record in existing_records]

        # Delete the existing documents
        await cursor.execute(delete_query, (ids_to_delete,))
        await pg_connection.commit()

    await cursor.close()
    await return_pg_connection(pg_connection)

async def get_all_bne_training_materials(table_name):
    pg_connection = await get_pg_connection()
    cursor = pg_connection.cursor()

    fetch_query = """ SELECT file_name, file_url, MIN(created_at) as created_at FROM {} GROUP BY file_name, file_url """.format(table_name)

    await cursor.execute(fetch_query)
    await pg_connection.commit()
    records = await cursor.fetchall()
    
    # Convert datetime objects to strings
    formatted_records = []
    for record in records:
        file_name, file_url, created_at = record
        # created_at = record[2]
        if isinstance(created_at, datetime):
            created_at = created_at.strftime("%Y-%m-%d %H:%M:%S")  # Convert datetime to string

        formatted_record = {
            "file_name": file_name,
            "file_url": file_url,
            "created_at": created_at # Convert datetime to string
        }
        formatted_records.append(formatted_record)

    await cursor.close()
    await return_pg_connection(pg_connection)
    return formatted_records

def get_table_name_based_on_user_type(user_type, functionality):
    table_name = ''
    if functionality == 'Training Materials':
        if user_type == BNE_USER_TYPE_BROKER:
            table_name = BNE_BROKER_TRAINING_MATERIAL_TABLE_NAME
        elif user_type == BNE_USER_TYPE_EMPLOYER:
            table_name = BNE_EMPLOYER_TRAINING_MATERIAL_TABLE_NAME
        elif user_type == BNE_USER_TYPE_INTERNAL:
            table_name = BNE_INTERNAL_TRAINING_MATERIAL_TABLE_NAME
    elif functionality == 'Training Videos':
        if user_type == BNE_USER_TYPE_BROKER:
            table_name = BNE_BROKER_TRAINING_VIDEO_TABLE_NAME
        elif user_type == BNE_USER_TYPE_EMPLOYER:
            table_name = BNE_EMPLOYER_TRAINING_VIDEO_TABLE_NAME
        elif user_type == BNE_USER_TYPE_INTERNAL:
            table_name = BNE_INTERNAL_TRAINING_VIDEO_TABLE_NAME
    
    return table_name

def find_column_by_tile_option(query):
    query_lower = query.lower()
    if "broker" in query_lower:
        return 'broker_training'
    elif "employer" in query_lower:
        return 'employer_training'
    elif "internal" in query_lower:
        return 'internal_training'
    raise Exception("Query must include 'broker', 'employer', or 'internal'. No valid match found.")

async def get_sbc(group_id, plan_id, eff_date):
    url = get_bne_base_url() + BNE_DOCUMENT_URL
    
    payload = {
        "benefitBundleOptionId": plan_id,
        "coverageEffDate": eff_date,
        "documentClass": "u_cirr_sbc_doc",
        "memberGroupId": group_id,
        "templateId": "",
        "stateCode": "",
        "divType": "",
        "platform": "cir"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {await get_internal_api_token()}"
    }
    
    try:
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
            response = await client.post(url, json=payload, headers=headers)
            
            response.raise_for_status()  # Raise exception for 4XX/5XX responses
            
            try:
                return response.json()
            except JSONDecodeError:
                return {
                    "error": "Failed to parse JSON response",
                    "response_text": response.text
                }
    except httpx.HTTPStatusError as e:
        return {
            "error": f"HTTP error occurred: {e.response.status_code}",
            "details": e.response.text
        }
    except Exception as e:
        return {
            "error": f"An error occurred: {str(e)}"
        }
    
def get_current_date():
    """Returns current date in YYYY-MM-DD format."""
    return datetime.now().strftime("%Y-%m-%d")

def is_exp_date_older_that_1_year_ago(exp_date: str) -> bool:
    """
    Returns True if exp_date is less than 1 year from today.
    exp_date should be in 'YYYY-MM-DD' format.
    """
    today = datetime.strptime(get_current_date(), "%Y-%m-%d")
    one_year_before = today.replace(year=today.year - 1)
    date_to_compare = datetime.strptime(exp_date, "%Y-%m-%d")
    return date_to_compare < one_year_before
    
async def get_plan_id_data(group_id):
    gco_data = await get_contract_options_for_user(group_id)
    plan_id_data = dict()

    currentDate = get_current_date()
    member_group = gco_data.get('memberGroup', {})
    contractList = member_group.get('contractList', [])

    for contract in contractList:
        eff_date = contract.get('effectiveDate', '')
        exp_date = contract.get('expirationDate', '')
        isContractActive = (
            eff_date and exp_date and
            eff_date <= currentDate and exp_date >= currentDate
        )
        if isContractActive:
            contractOptionsList = contract.get('contractOptionsList', [])
            for contractOption in contractOptionsList:
                expDate = contractOption.get('contractOptionExpirationDate', '')
                contractOptType = contractOption.get('contractOptType', '')
                coverageOptionList = contractOption.get('coverageOptionList', [])

                if not is_exp_date_older_that_1_year_ago(expDate):
                    for coverageOption in coverageOptionList:
                        planId = coverageOption.get('coverageOptionID', '')
                        planName = coverageOption.get('covrgOptDescWithExtMarktName', '')
                        effDate = contractOption.get('contractOptionEffectiveDate', '')
                        expDate = contractOption.get('contractOptionExpirationDate', '')

                        if not planId:
                            continue

                        if contractOptType not in plan_id_data:
                            plan_id_data[contractOptType] = {}

                        if planId not in plan_id_data[contractOptType]:
                            plan_id_data[contractOptType][planId] = {
                                "planName": planName,
                                "effDates": []
                            }

                        plan_id_data[contractOptType][planId]["effDates"].append({
                            "effDate": effDate,
                            "expDate": expDate
                        })

    return plan_id_data

def validate_plan_id(plan_id_data, plan_id) -> bool:
    for contractOptType in plan_id_data:
        if plan_id in plan_id_data[contractOptType]:
            return True
    return False

def check_for_multiple_eff_dates(plan_id_data, plan_id) -> bool:
    """
    Check if there are multiple effective dates for the given plan_id.
    Returns True if multiple effective dates exist, otherwise False.
    """
    for contractOptType in plan_id_data:
        if plan_id in plan_id_data[contractOptType]:
            eff_dates = plan_id_data[contractOptType][plan_id]["effDates"]
            return len(eff_dates) > 1
    return False

def find_single_effective_date(plan_id_data, plan_id) -> str:
    """
    Find the single effective date for the given plan_id.
    Returns the effective date string if found, otherwise an empty string.
    """
    for contractOptType in plan_id_data:
        if plan_id in plan_id_data[contractOptType]:
            eff_dates = plan_id_data[contractOptType][plan_id]["effDates"]
            if len(eff_dates) == 1:
                return eff_dates[0]["effDate"]
    return ""

async def get_contract_options_for_user(group_id):
    url = get_bne_base_url() + BNE_CONTRACT_OPTIONS_URL
    
    payload = {
        "groupId": group_id,
        "inquiryDate": "0001-01-01",
        "isSitusStValidationRequired": False
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {await get_internal_api_token()}"
    }
    
    try:
        # Using verify parameter consistent with other httpx calls in the codebase
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
            response = await client.post(url, json=payload, headers=headers)
            
            response.raise_for_status()  # Raise exception for 4XX/5XX responses
            
            try:
                return response.json()
            except JSONDecodeError:
                return {
                    "error": "Failed to parse JSON response",
                    "response_text": response.text
                }
    except httpx.HTTPStatusError as e:
        return {
            "error": f"HTTP error occurred: {e.response.status_code}",
            "details": e.response.text
        }
    except Exception as e:
        return {
            "error": f"An error occurred: {str(e)}"
        }
    
async def get_tile_options_for_user(query, user_type):
    docs_table_name = get_table_name_based_on_user_type(user_type, 'Training Materials')
    videos_table_name = get_table_name_based_on_user_type(user_type, 'Training Videos')

    pg_connection = await get_pg_connection()
    cursor = pg_connection.cursor()


    fetch_query = f""" 
    SELECT 
        file_category,
        COALESCE(ARRAY_AGG(DISTINCT file_name) FILTER (WHERE file_type = 'file'), ARRAY[]::text[]) AS files,
        COALESCE(ARRAY_AGG(DISTINCT file_name) FILTER (WHERE file_type = 'video'), ARRAY[]::text[]) AS videos
    FROM (
        SELECT 
            file_category,
            file_name,
            'file' AS file_type
        FROM 
            {docs_table_name}
        WHERE
            {find_column_by_tile_option(query)} = 'true'
        UNION
        SELECT 
            file_category,
            file_name,
            'video' AS file_type
        FROM 
            {videos_table_name}
        WHERE
            {find_column_by_tile_option(query)} = 'true'
    ) AS combined
    GROUP BY 
        file_category;
    """

    await cursor.execute(fetch_query)
    await pg_connection.commit()
    records = await cursor.fetchall()
    
    # Convert datetime objects to strings
    formatted_records = []
    for record in records:

        formatted_record = {
            "file_category": record[0],
            "files": record[1],
            "videos": record[2],
        }
        formatted_records.append(formatted_record)

    await cursor.close()
    await return_pg_connection(pg_connection)

    return formatted_records

async def create_or_update_bne_training_video(file_name, file_url, file_category, user_type,  broker_training, employer_training, internal_training):

    table_name = get_table_name_based_on_user_type(user_type, 'Training Videos')

    pg_connection = await get_pg_connection()
    cursor = pg_connection.cursor()
    
    # Create table if it doesn't exist
    create_table_query = f"""
    CREATE TABLE IF NOT EXISTS {table_name} (
        id SERIAL PRIMARY KEY,
        file_name TEXT,
        file_url TEXT,
        file_category TEXT,
        broker_training TEXT DEFAULT 'false',
        employer_training TEXT DEFAULT 'false',
        internal_training TEXT DEFAULT 'false',
        page_content TEXT,
        embedding vector(1536), 
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    """
    await cursor.execute(create_table_query)
    await pg_connection.commit()
    
    # Generate embedding for the document content
    embedding = await embeddings.aembed_query(file_name)

    # Check if video document already exists    
    await delete_bne_training_material(table_name=table_name, file_name=file_name)
    
    # Insert new video document
    insert_query = f"""
    INSERT INTO {table_name} (file_name, file_url, file_category,  broker_training, employer_training, internal_training, page_content, embedding, created_at)
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
    """
    # cursor.execute(insert_query, (video_document.file_name, video_document.file_url, video_document.page_content, embedding))
    await cursor.execute(insert_query, (file_name, file_url, file_category,  broker_training, employer_training, internal_training, file_name, embedding))

    await pg_connection.commit()
    await cursor.close()
    await return_pg_connection(pg_connection)

#temp fix, will be removed in later release

async def invalid_user_type_fallback(state) -> dict:
    tool_calls = state["messages"][-1].tool_calls
    
    return {
        "messages": [
            ToolMessage(
                content=f"The capability is not available for current user. Please contact support for assistance.",
                tool_call_id=tc["id"],
            ) for tc in tool_calls
        ]
    }

def should_direct_to_human(state:State) -> bool:
    sorry_threshold = THRESHOLD_FOR_HUMAN_ASSISTANCE
    count = COUNT_OF_MSG_TO_MONITOR
    messages = state.get("messages", [])
    ai_messages = [m for m in messages if isinstance(m, AIMessage)]
    tool_messages = [m for m in messages if isinstance(m, ToolMessage)]
    if len(ai_messages) > count or len(tool_messages) > count:
        latest_ai_msgs = ai_messages[-count:]
        latest_tool_msgs = tool_messages[-count:]
        sorry_count = 0
        for msg in latest_ai_msgs + latest_tool_msgs:
            if "sorry" in (msg.content or "").lower():
                sorry_count += 1
        if sorry_count >= sorry_threshold:
            return True
    return False

def direct_to_human(state:State) -> dict:
    """
    Direct user to human representative.
    """
    return {
        "messages": [
            AIMessage(
                content="You can reach a human representative at 866-908-5940."
            )
        ]
    }

async def get_internal_api_token():
    """
    Fetches an internal API token from the specified endpoint.
    Returns the token string if successful, or raises an exception with details.
    """
    url = BNE_GET_TOKEN_URL
    headers = {
        "Content-Type": "application/json"
    }
    try:
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
            response = await client.get(url, headers=headers)
            response.raise_for_status()
            token = response.text.strip()
            if not token:
                raise Exception("Token response is empty.")
            return token
    except httpx.HTTPStatusError as e:
        raise Exception(f"HTTP error occurred: {e.response.status_code}, details: {e.response.text}")
    except Exception as e:
        raise Exception(f"An error occurred while fetching internal API token: {str(e)}")

async def get_employer_handbook(group_id, plan_id, eff_date, contractOptType):
    """
    Fetches the employer handbook based on the provided parameters.

    Args:
        group_id (str): The group ID.
        plan_id (str): The plan ID.
        eff_date (str): The effective date in 'YYYY-MM-DD' format.
        contractOptType (str): The coverage option type (MD, DN, VS, LI, AD).

    Returns:
        dict: The response JSON if successful.
    """
    # Map contractOptType to templateId
    template_id_mapping = {
        "MD": "GROUP_HANDBOOK",
        "DN": "DENTAL_EMPLOYER_HANDBOOK",
        "VS": "VISION_EMPLOYER_HANDBOOK",
        "LI": "BASIC_LIFE_EMPLOYER_HANDBOOK",
        "AD": "BASIC_LIFE_EMPLOYER_HANDBOOK"
    }
    if not contractOptType:
        raise Exception("contractOptType cannot be None.")
    
    template_id = template_id_mapping.get(contractOptType)
    if not template_id:
        raise Exception(f"Invalid contractOptType: {contractOptType}. Must be one of {list(template_id_mapping.keys())}.")

    url = get_bne_base_url() + BNE_DOCUMENT_URL

    payload = {
        "benefitBundleOptionId": plan_id,
        "coverageEffDate": eff_date,
        "documentClass": "u_oxf_hndbk",
        "memberGroupId": group_id,
        "templateId": template_id,
        "stateCode": "",
        "divType": "",
        "platform": "cir"
    }

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {await get_internal_api_token()}"
    }

    try:
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
            response = await client.post(url, json=payload, headers=headers)
            response.raise_for_status()  # Raise exception for 4XX/5XX responses
            
            try:
                return response.json()
            except JSONDecodeError as e:
                raise Exception(f"Failed to parse JSON response: {str(e)}")
    except httpx.HTTPStatusError as e:
        raise Exception(f"HTTP error occurred: {e.response.status_code}, details: {e.response.text}")
    except Exception as e:
        raise Exception(f"An error occurred while fetching the employer handbook: {str(e)}")

async def get_member_handbook(group_id, plan_id, eff_date, contractOptType):
    """
    Fetches the member handbook based on the provided parameters.

    Args:
        group_id (str): The group ID.
        plan_id (str): The plan ID.
        eff_date (str): The effective date in 'YYYY-MM-DD' format.
        contractOptType (str): The coverage option type (MD, DN, VS, LI, AD).

    Returns:
        dict: The response JSON if successful.
    """
    # Map contractOptType to templateId
    template_id_mapping = {
        "MD": "MEMBER_HANDBOOK",
        "DN": "DENTAL_MEMBER_HANDBOOK",
        "VS": "VISION_MEMBER_HANDBOOK",
        "LI": "BASIC_LIFE_MEMBER_HANDBOOK",
        "AD": "BASIC_LIFE_MEMBER_HANDBOOK"
    }
    if not contractOptType:
        raise Exception("contractOptType cannot be None.")
    
    template_id = template_id_mapping.get(contractOptType)
    if not template_id:
        raise Exception(f"Invalid contractOptType: {contractOptType}. Must be one of {list(template_id_mapping.keys())}.")

    url = get_bne_base_url() + BNE_DOCUMENT_URL

    payload = {
        "benefitBundleOptionId": plan_id,
        "coverageEffDate": eff_date,
        "documentClass": "u_oxf_hndbk",
        "memberGroupId": group_id,
        "templateId": template_id,
        "stateCode": "",
        "divType": "",
        "platform": "cir"
    }

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {await get_internal_api_token()}"
    }

    try:
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
            response = await client.post(url, json=payload, headers=headers)
            response.raise_for_status()  # Raise exception for 4XX/5XX responses
            
            try:
                return response.json()
            except JSONDecodeError as e:
                raise Exception(f"Failed to parse JSON response: {str(e)}")
    except httpx.HTTPStatusError as e:
        raise Exception(f"HTTP error occurred: {e.response.status_code}, details: {e.response.text}")
    except Exception as e:
        raise Exception(f"An error occurred while fetching the member handbook: {str(e)}")

def find_contract_opt_type(plan_id_data, plan_id):
    """
    Finds the contractOptType that contains the given plan_id.

    Args:
        plan_id_data (dict): The plan_id_data structure.
        plan_id (str): The plan ID to search for.

    Returns:
        str: The contractOptType containing the plan_id, or None if not found.
    """
    for contractOptType, plans in plan_id_data.items():
        if plan_id in plans:
            return contractOptType
    return None

async def get_forms(state_code: str, group_size: str, language: str):
    """
    Fetches forms document based on state, group size, and language.

    Args:
        state_code (str): The state code (e.g., "AL").
        group_size (str): The group size (e.g., "2-50").
        language (str): The language (e.g., "ENGLISH").

    Returns:
        dict: The response JSON if successful.
    """
    url = BNE_GET_FORMS_URL
    payload = {
        "documentClass": "u_ei_forms",
        "stateCode": state_code,
        "grpSize": group_size,
        "language": language
    }
    headers = {
        "Content-Type": "application/json"
    }

    try:
        async with httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True') as client:
            response = await client.post(url, json=payload, headers=headers)
            response.raise_for_status()
            try:
                return response.json()
            except JSONDecodeError as e:
                raise Exception(f"Failed to parse JSON response: {str(e)}")
    except httpx.HTTPStatusError as e:
        raise Exception(f"HTTP error occurred: {e.response.status_code}, details: {e.response.text}")
    except Exception as e:
        raise Exception(f"An error occurred while fetching forms: {str(e)}")