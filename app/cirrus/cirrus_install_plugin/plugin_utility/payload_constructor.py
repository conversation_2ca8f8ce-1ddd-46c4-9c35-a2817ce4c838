from utils.helpers.mappings import CIRRUS_PRODUCT_MAPPING, NO_SSN_REASON_MAPPING, QUALIFYING_EVENT_MAPPING
from enum import Enum
"""
This module contains functions for constructing payload data used in an API.

Functions:
- removeHyphens: Removes hyphens from a given string.
- gender_word_to_letter: Converts a gender word to its corresponding letter representation.
- memberDemographics: Constructs the member demographics JSON object.
- memberAddressList: Constructs the member address list JSON object.
- memberEcommList: Constructs the member electronic communication list JSON object.
- memberPhoneList: Constructs the member phone list JSON object.
- billGroupListInfo: Constructs the bill group list information JSON object.
- populationList: Constructs the population list JSON object.
- empStatus: Retrieves the employment status code for a given status.
- is_auto_assigned: Checks if a provider ID is auto-assigned.
- is_digit_provider_id: Checks if a provider ID is a digit.
- providerinfo: Constructs the provider information JSON object.
- eligibilityReason: Retrieves the eligibility reason code for a given coverage information.
- memberAffiliation: Constructs the member affiliation JSON object.
- incomelist: Constructs the income list JSON object.
- populate_mem_info: Populates the member information JSON object.
- construct_payload: Constructs the final payload JSON object.

"""

class RelationshipCode(Enum):
    EMPLOYEE = '18'
    SPOUSE = '01'
    DEPENDENT = '19'

DEPENDENTS_LIST = ["SpouseInfo", "Dependent1Info","Dependent2Info", "Dependent3Info", "Dependent4Info"]

# Rest of the code...

def removeHyphens(ssn_payload):
    if ssn_payload:
        ssn = ssn_payload.replace('-','')
        return ssn
    else:
        return None


def gender_word_to_letter(gender):   
    if (str(gender).lower() == 'male'):  
        return 'M' 
    elif (str(gender).lower() == 'female'):  
        return 'F'
    elif 'non-binary' in (str(gender).lower() if str(gender) else "1"):  
        return 'U'  
    elif str(gender) and str(gender)=="M" or str(gender)=="F": 
        return str(gender)
    else :
        return None


def memberDemographics(payload_response):
    json_obj = {
        "nameFirst": payload_response["FirstName"],
        "nameLast": payload_response["LastName"],
        "nameMiddle": payload_response["MiddleName"] if payload_response["MiddleName"] else None,
        "nameSuffix": "",
        "socialSecurityNumber": removeHyphens(payload_response["SSN"]),
        "ssnNotProvidedReasonType": NO_SSN_REASON_MAPPING.get(payload_response['NoSSNReason'], None) if not payload_response["SSN"] else None,
        "birthDate": payload_response["DOB"],
        "gender": gender_word_to_letter(payload_response["Gender"])
    }
    return json_obj


def memberAddressList(payload_response):
    if "Address" in payload_response and payload_response['Address']:
        json_obj = [{
            "effectiveDate": eff_date,
            "addressType": "HOM",  # to-check-hardcoded-as-per-previous-logic
            "address1": payload_response["Address"],
            "address2": "",
            "city": payload_response["City"],
            "state": payload_response["State"],
            "postalCode": payload_response["PostalCode"],
            "country": payload_response["Country"],

        }]
    else:
        json_obj = None
    return json_obj


def memberEcommList(payload_response, isEmployee):
    if isEmployee and payload_response["EmailAddress"]:
        json_obj = [{
            "communicationType": "EML",
            "primaryInd": True,
            "communicationText": payload_response["EmailAddress"]
        }]
        return json_obj
    else:
        return None
    

def memberPhoneList(payload_response, isEmployee):
    phone_mapping = {'HomePhone': 'H', 'CellPhone': 'M', 'WorkPhone': 'W'}
    if isEmployee:
        json_obj = []
        for phone_type, phone_key in phone_mapping.items():
            if payload_response[phone_type]:
                phone_entry = {
                    "phoneType": phone_key,
                    "phoneNumber": payload_response[phone_type],
                    "contactPeriod": "4",
                    "primaryInd": True
                }
                json_obj.append(phone_entry)

        return json_obj if json_obj else None
    else:
        return None


def billGroupListInfo(payload_response):
    if payload_response.get("SelectedBillGroup"):
        bill_grp_id = payload_response["SelectedBillGroup"].split('-')[0].strip()
        bill_grp_list =  [
                {
                    "billGroupReferenceID":bill_grp_id ,
                    "coverageOptionType": CIRRUS_PRODUCT_MAPPING.get(productInfo.get("productType"), ""),
                    "effectiveDate": eff_date,
                    "expirationDate": ""
                } for productInfo in payload_response["PlanAndPopulationDetails"] if productInfo.get("selectedCoverageOptionId") != "Waive"
            ]
        return bill_grp_list


def populationList(payload_response):
    pop_list = [
        {
            "populationID": productInfo.get("selectedPopulation"),
            "coverageOptionType": CIRRUS_PRODUCT_MAPPING.get(productInfo.get("productType"), ""),
            "effectiveDate": eff_date,
            "expirationDate": ""
        } for productInfo in payload_response["PlanAndPopulationDetails"] if productInfo.get("selectedPopulation")
    ]
    return pop_list


def empStatus(Status):
    if 'active' in (Status.lower() if Status else "1"):
        return 'A'
    elif 'cobra' in (Status.lower() if Status else "1"):
        return 'T'
    elif 'retired' in (Status.lower() if Status else "1"):
        return 'R'
    else:
        return 'A'
    

def is_auto_assigned(provider_id):  
    auto_assigned_words = ['auto',"assign"]  
    for word in auto_assigned_words:  
        if word not in str(provider_id).lower():  
            return False  
    return True  


def is_digit_provider_id(provider_id):  
    if provider_id: 
        if provider_id.isdigit(): ##
            return True  
    return False 


def providerinfo(payload_response):
    provider_id = payload_response["PhysicianID"]
    ispatient = payload_response["ExistingPatientMedical"]
    if 'y' in (ispatient.lower() if ispatient else "0"):
        ispatient = True
    else:
        ispatient = False
 
    if is_digit_provider_id(provider_id) or is_auto_assigned(provider_id):
        json_obj = [
            {
                "assignmentType": "PCP",
                "providerID": provider_id if is_digit_provider_id(provider_id) else '',
                "effectiveDate": eff_date,
                "existingPatientInd": ispatient,
                **({"RandomizePCP": True} if is_auto_assigned(provider_id) else {})
            },
        ]
        return json_obj
    else:
        return None


def eligibilityReason(covinfo, isEmployee):
    if isEmployee and covinfo["ReasonforMedEligibility"] != None:
        el_reason = covinfo["ReasonforMedEligibility"].lower(
        ) if covinfo["ReasonforMedEligibility"] else "1"
    elif covinfo["ReasonforS/DMedEligibility"] != None:
        el_reason = covinfo["ReasonforS/DMedEligibility"].lower(
        ) if covinfo["ReasonforS/DMedEligibility"] else "1"
    else:
        el_reason = "1"
    if el_reason == "over 65":
        reason = '0'
        return reason
    elif "disable" in el_reason:
        reason = '1'
        return reason
    elif "kidney" in el_reason:
        reason = '2'
        return reason
    else:
        return None


def memberAffiliation(payload_response, rel_code, isEmployee):
    json_obj = {
        "relationshipCode": rel_code,
        "memGroupID": grp_id,
        **({"employBeginDate": payload_response["DateOfHire"],
            "employEndDate": "",  # only for Cobra
            "employStatusCode": empStatus(payload_response["EmployeeType"]),
            "effectiveDate": eff_date} if isEmployee else {}),
        "populationList": populationList(payload_response),
        "billGroupList": billGroupListInfo(payload_response),
        "incomeList": incomelist(payload_response) if isEmployee else None
    }
    return json_obj


def incomelist(payload_response):
    if "Salary" in payload_response:
        json_obj = {
            "effectiveDate": eff_date,
            "expirationDate": "9999-12-31",
            "incomeAmount": payload_response['Salary'],
            "incomeFrequencyCode": "6"
        }
    else:
        json_obj = None
    return json_obj


def coblistinfo(payload_response, covinfo, isMemEnrolled, isEmployee):
    Medicare_Status = covinfo["OtherCVG"]
    if isMemEnrolled and Medicare_Status != None and Medicare_Status:
        cob_obj = {
            "benefitType": "MD",  # leave it for now
            "entityName": covinfo["NameOfOtherCarrier"],
            "subscriberFirstName": payload_response["FirstName"],
            "subscriberLastName": payload_response["LastName"],
            "cobCoverageType": 1,
            "PayerResponsibilitySeqCode": "S",
            "effectiveDate": eff_date,
            "medicareEligibilityReasonCode": eligibilityReason(covinfo, isEmployee),
        }
        json_obj = getCobList(covinfo, cob_obj, isEmployee)
        return json_obj
    else:
        return None


def getCobList(covinfo, json_obj, isEmployee):
    if isEmployee:
        return getCobListEmp(covinfo, json_obj)
    else:
        return getCobListDep(covinfo, json_obj)


def getCobListEmp(covinfo, json_obj):
    if (covinfo["MedicarePartA"] and covinfo["MedicarePartB"] and covinfo["MedicarePartD"]):
        bentype = [{
            **json_obj,
            "govtBenefitType": "MedicareAB",
        },
            {
            **json_obj,
            "govtBenefitType": "MedicareD",
        }
        ]
        return bentype
    elif (covinfo["MedicarePartA"] and covinfo["MedicarePartB"]):
        bentype = [{
            **json_obj,
            "govtBenefitType": "MedicareAB",
        },]
        return bentype
    elif (covinfo["MedicarePartB"] and covinfo["MedicarePartD"]):
        bentype = [{
            **json_obj,
            "govtBenefitType": "MedicareB",
        },
            {
            **json_obj,
            "govtBenefitType": "MedicareD",
        }
        ]
        return bentype
    elif (covinfo["MedicarePartA"] and covinfo["MedicarePartD"]):
        bentype = [{
            **json_obj,
            "govtBenefitType": "MedicareA",
        },
            {
            **json_obj,
            "govtBenefitType": "MedicareD",
        }
        ]
        return bentype
    elif (covinfo["MedicarePartA"] or covinfo["MedicarePartB"] or covinfo["MedicarePartD"]):
        govBenefitTypeVal = ''
        if (covinfo["MedicarePartA"]):
            govBenefitTypeVal = 'MedicareA'
        if (covinfo["MedicarePartB"]):
            govBenefitTypeVal = 'MedicareB'
        if (covinfo["MedicarePartD"]):
            govBenefitTypeVal = 'MedicareD'
        bentype = [
            {
                **json_obj,
                "govBenefitType": govBenefitTypeVal
            },
        ]
        return bentype
    else:
        bentype = [
            {
                **json_obj,
                "govBenefitType": None
            },
        ]
        return bentype


def getCobListDep(covinfo, json_obj):
    if (covinfo["MedicareS/DPartA"] and covinfo["MedicareS/DPartB"] and covinfo["MedicareS/DPartD"]):
        bentype = [{
            **json_obj,
            "govtBenefitType": "MedicareAB",
        },
            {
            **json_obj,
            "govtBenefitType": "MedicareD",
        }
        ]
        return bentype
    elif (covinfo["MedicareS/DPartA"] and covinfo["MedicareS/DPartB"]):
        bentype = [{
            **json_obj,
            "govtBenefitType": "MedicareAB",
        },]
        return bentype
    elif (covinfo["MedicareS/DPartB"] and covinfo["MedicareS/DPartD"]):
        bentype = [{
            **json_obj,
            "govtBenefitType": "MedicareB",
        },
            {
            **json_obj,
            "govtBenefitType": "MedicareD",
        }
        ]
        return bentype
    elif (covinfo["MedicareS/DPartA"] and covinfo["MedicareS/DPartD"]):
        bentype = [{
            **json_obj,
            "govtBenefitType": "MedicareA",
        },
            {
            **json_obj,
            "govtBenefitType": "MedicareD",
        }
        ]
        return bentype
    elif (covinfo["MedicareS/DPartA"] or covinfo["MedicareS/DPartB"] or covinfo["MedicareS/DPartD"]):
        govBenefitTypeVal = ''
        if (covinfo["MedicareS/DPartA"]):
            govBenefitTypeVal = 'MedicareA'
        if (covinfo["MedicareS/DPartB"]):
            govBenefitTypeVal = 'MedicareB'
        if (covinfo["MedicareS/DPartD"]):
            govBenefitTypeVal = 'MedicareD'
        bentype = [
            {
                **json_obj,
                "govBenefitType": govBenefitTypeVal
            },
        ]
        return bentype
    else:
        bentype = [
            {
                **json_obj,
                "govBenefitType": None
            },
        ]
        return bentype


# payload construction

def populate_mem_info(rel_code, payload_response, correlationID, covinfo, isMemEnrolled, isEmployee):
    mem_info = {
        "correlationID": f"{correlationID}",
        "memberAffiliation": memberAffiliation(payload_response, rel_code, isEmployee),
        "memberCoverageList": [  # json required initially (leave it for now)
            {
                "benPlanEffDate": eff_date,
                "coverageOptionType": CIRRUS_PRODUCT_MAPPING.get(productInfo.get("productType"), ""),
                "benefitStatusCode": "A",  
                "coverageOptionID": productInfo.get("selectedCoverageOptionId"),
            } for productInfo in payload_response["PlanAndPopulationDetails"] if productInfo.get("selectedCoverageOptionId") != "Waive"
        ],
        "providerList": providerinfo(payload_response),
        
        # "cobList": coblistinfo(payload_response, covinfo, isMemEnrolled, isEmployee), # coblist-commented-for-now

        "demographics": memberDemographics(payload_response),
        "addressList": memberAddressList(payload_response),
        "phoneList": memberPhoneList(payload_response, isEmployee),
        "ecommList": memberEcommList(payload_response, isEmployee)
    }
    return mem_info

def construct_payload(payload_response):
    global eff_date, grp_id
    
    eff_date = payload_response["EffectiveDate"]
    grp_id = payload_response["GroupNumber"]
    mem_list = []
    mem_count = 0
    
    def add_member(rel_code, member_info, mem_count, other_info, is_enrolled, is_employee):
        mem_info = populate_mem_info(rel_code, member_info, mem_count, other_info, is_enrolled, is_employee)
        mem_list.append(mem_info)
        return mem_count + 1

    # Employee
    mem_count = add_member(RelationshipCode.EMPLOYEE.value, payload_response["EmployeeInfo"], mem_count, payload_response["OtherMedCovInfo"], 1, 1)
    
    # Dependents
    for dependent in DEPENDENTS_LIST:
        if (payload_response[dependent]["FirstName"] and not all(item['selectedCoverageOptionId'] == 'Waive' for item in payload_response[dependent]["PlanAndPopulationDetails"])):
            rel_code = RelationshipCode.SPOUSE.value if dependent == "SpouseInfo" else RelationshipCode.DEPENDENT.value
            is_enrolled = 1 if (payload_response[dependent]["FirstName"].lower() if payload_response[dependent]["FirstName"] else "No First Name") == (payload_response["OtherMedCovInfo"]["MedicareS/DFirstName"].lower() if payload_response["OtherMedCovInfo"]["MedicareS/DFirstName"] else "No Medicare Name") else 0  # isMemMedPrimary
            mem_count = add_member(rel_code, payload_response[dependent], mem_count, payload_response["OtherMedCovInfo"], is_enrolled, 0)


    print("member count - ", mem_count)
    
    memBody = {
        "qualifyingEvent": QUALIFYING_EVENT_MAPPING.get(payload_response["ReasonForApplication"]["Reason"], None),
        "effectiveDate": eff_date,
        "memberCount": mem_count,
        "qualifyingEventDate": eff_date,
        "processID": ""  
    }
    temp_data = {"memberList": mem_list}
    temp_list = temp_data.get("memberList", [])
    memBody["memberList"] = temp_list + memBody.get("memberList", [])
    
    return memBody
