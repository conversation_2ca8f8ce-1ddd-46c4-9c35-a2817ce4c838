from config.db import get_client_database_connection
from dateutil import tz  
from utils.general_utils import getTime


est = tz.gettz('US/Eastern')  
  
async def log_error_to_db(error: str, client_id: str, session_id: str, user_name: str, error_type: str, uuid: str = None, request_id: str = None, collection: str = "ErrorLogs"):  
    try:  
        client_db = await get_client_database_connection(client_id)
        error_collection = client_db[collection]  
        current_time = getTime()
        error_log = {  
            "create_timestamp": current_time,
            "update_timestamp": current_time,  
            "uuid": uuid,  
            "session_id": session_id,
            "request_id": request_id,
            "user_name": user_name,
            "client_id": client_id,
            "type": error_type,  
            "error": error, 
        }  
          
        await error_collection.insert_one(error_log)  
      
    except Exception as e:   
        print(f"An error occurred while logging the error in DB: {str(e)}")
