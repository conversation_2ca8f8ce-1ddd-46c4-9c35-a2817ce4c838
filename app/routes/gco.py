import json
from fastapi import APIRouter, Request, Response
import traceback
import os
import httpx
from graph.state import State
from services import log_error_to_db_service
from client_plugins.gco_reporting.gco_reporting_utils.payload_utils import PayloadUtils
import math
from utils.helpers.constants import OEC_GCOREPORTING_CLIENT_ID
from urllib.parse import urlparse

from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
import hashlib
import base64
import os

router = APIRouter()

async def fetch_gco_reports_data(uuid: str, user_type: str = '', current_page: int = 0, page_size: int = 10):
    try:
        if not user_type:
            return {"status": "error", "response": "User type is required"}
        
        # Validate current_page and page_size to prevent SSRF
        try:
            current_page = int(current_page)
            page_size = int(page_size)
            if current_page < 0 or page_size <= 0:
                raise ValueError
        except (ValueError, TypeError):
            return {"status": "error", "response": "Invalid pagination parameters"}

        initial_url = os.environ.get('GCO_PARSER_URL')
        user_type = user_type.upper()
        endpoint =  initial_url + "getreporttypescount?skip=" + str(current_page) + "&page_size=" + str(page_size)
        headers = {"Content-Type": "application/json"}
        client = httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True')
        
        if user_type == 'INTERNAL':
            body = {"user_type": "INTERNAL", "uuid": uuid}
        else:
            body = {"user_type": "EXTERNAL", "uuid": uuid}
        
        parsed_url = urlparse(endpoint)
        if not parsed_url.scheme or not parsed_url.netloc:
            raise ValueError("Invalid URL format for endpoint")
        print(f"Fetching GCO reports data from: {endpoint} with body: {body} and headers: {headers}")
        response = await client.post(endpoint, json=body, headers=headers)

        if response.status_code == 200:
            response = response.json()
            if not response:
                return {"status": "success", "response": "No data found for the provided parameters"}
            
            total_records = response.get('total_records_count', 0)
            response = response.get('internal_user_records', []) if user_type == 'INTERNAL' else response.get('external_user_records', [])
            ####### redis dump ########
            # await dump_to_redis(uuid, response, headers)
            if page_size <= 0:
                return {"status": "error", "response": "Page size must be greater than 0"}
            total_pages = math.ceil((total_records / page_size))
            return {"status": "success", "response": { "group_data": response, "total_records": total_records, "current_page": current_page, "page_size": page_size,"total_pages":total_pages , "user_type": user_type }}
        else:
            return {"status": "error", "response": f"Failed to fetch data: {response.text}"}
    except Exception as e:
        await log_error_to_db_service.log_error_to_db(
            traceback.format_exc(), OEC_GCOREPORTING_CLIENT_ID, "N/A", "N/A", "ValueError"
        )
        traceback.print_exc()
        return {"status": "error", "response": "An unexpected error occurred"}

async def store_user_tracking_info(user_action_payload: dict = None):
    try:
        initial_url = os.environ.get('GCO_PARSER_URL')
        endpoint = initial_url + "storeusertrackinginfo"
        
        # Use PayloadUtils to get a standardized payload with only expected keys
        standardized_payload = PayloadUtils.get_standardized_tracking_payload(user_action_payload=user_action_payload)
        
        headers = {"Content-Type": "application/json"}
        client = httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True')
        response = await client.post(endpoint, json=standardized_payload, headers=headers)
        if response.status_code == 200:
            print("Successfully logged user activity")
        else:
            print(f"Failed to logged user activity: {response.text}")
    except Exception as e:
        await log_error_to_db_service.log_error_to_db(
            traceback.format_exc(), OEC_GCOREPORTING_CLIENT_ID, "N/A", "N/A", "ValueError"
        )
        traceback.print_exc()
        print("An unexpected error occurred while storing user tracking info")

async def dump_to_redis_all_group_list(uuid, user_type = 'EXTERNAL'):
    try:
        ######## get group list #########
        initial_url = os.environ.get('GCO_PARSER_URL')
        endpoint = initial_url + 'getreporttypescount?skip=0&page_size=100000'
        client = httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True')
        
        payload = {
            "user_type": user_type,
            "uuid": uuid,
        }
        headers = {"Content-Type": "application/json"}
        response = await client.post(endpoint, json=payload, headers=headers)
        
        ######## redis dump ########
        if response.status_code != 200:
            print(f"Failed to fetch group list: {response.text}")
            return {"status": "error", "response": "Failed to fetch group list"}
        response = response.json()
        
        if user_type.upper() == "INTERNAL":
            response = response.get('internal_user_records', [])
        else:
            response = response.get('external_user_records', [])
        
        if not response:
            redis_url = os.environ.get('GCO_REDIS_URL') + 'put'
            key = "GCO_ALLGROUPLIST:" + uuid
            
            payload = {
                "key": key,
                "timeUnit": "DAYS",
                "value": json.dumps(response),
                "expiration": "1"
            }
            response = await client.post(redis_url, json=payload, headers=headers)
            return {"status": "error", "response": "No group data found to store in Redis"}
        
        redis_url = os.environ.get('GCO_REDIS_URL') + 'put'
        key = "GCO_ALLGROUPLIST:" + uuid
        
        payload = {
            "key": key,
            "timeUnit": "DAYS",
            "value": json.dumps(response),
            "expiration": "1"
        }
        response = await client.post(redis_url, json=payload, headers=headers)
        if response.status_code != 200:
            return {"status": "error", "response": "Failed to store group list in Redis"}
    except Exception as e:
        await log_error_to_db_service.log_error_to_db(
            traceback.format_exc(), OEC_GCOREPORTING_CLIENT_ID, "N/A", "N/A", "RedisError"
        )
        traceback.print_exc()
        print(f"An error occurred while dumping all group list data to Redis: {e}")
        return {"status": "error", "response": "An unexpected error occurred while storing group list in Redis"}

async def get_redis_all_group_list(uuid: str, user_type: str = 'EXTERNAL'):
    try:
        redis_url = os.environ.get('GCO_REDIS_URL') + 'get'
        key = "GCO_ALLGROUPLIST:" + uuid
        payload = { "key": key }
        client = httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True')
        response = await client.post(redis_url, json=payload)
        if response.status_code == 200:
            return response.json()
        else:
            return []
    except Exception as e:
        traceback.print_exc()
        return {"status": "error", "response": "An unexpected error occurred while fetching group list from Redis"}

@router.post("/getWelcomePageData")
async def generate_welcome_page_data(request: Request):
    try:
        data = await request.json()
        uuid = data.get("uuid", "")
        current_page = data.get("current_page", 0)
        page_size = data.get("page_size", 10)
        user_type = data.get("user_type", "EXTERNAL")
        result = await fetch_gco_reports_data(uuid, user_type, current_page, page_size)
        return result
    except Exception as e:
        traceback.print_exc()
        return {"status": "error", "message": str(e)}

@router.post("/storeUserTrackingInfo")
async def store_user_tracking_info_route(request: Request):
    try:
        userData = await request.json()
        await store_user_tracking_info(userData)
        return {"status": "success"}
    except Exception as e:
        traceback.print_exc()
        return {"status": "error", "message": str(e)}

@router.post("/downloadGCOReports")
async def download_gco_reports(request: Request):
    try:
        data = await request.json()
        file_name = data.get("file_name", "")
        encrypted_file_name = await encrypt_filename(file_name, os.environ.get('GCO_CLIENT_SECRET'))
        
        if not file_name:
            return {"status": "error", "response": "File name is required"}
            
        initial_url = os.environ.get('GCO_PARSER_URL')
        download_url = f"{initial_url}/showfile"
        headers = {"Content-Type": "application/json"}
        payload = { "filename": encrypted_file_name }
        
        print(f"Downloading file from: {download_url} with payload: {payload} and headers: {headers}")
        client = httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True')
        response = await client.post(download_url, json=payload, headers=headers)
        
        if response.status_code == 200:
            # Return the file content with appropriate headers for frontend to handle as blob
            return Response(
                content=response.content,
                media_type=response.headers.get("Content-Type", "application/octet-stream"),
                headers={"Content-Disposition": f"attachment; filename={file_name}"}
            )
        else:
            return {"status": "error", "response": f"Failed to download file: {response.text}"}
    except Exception as e:
        await log_error_to_db_service.log_error_to_db(
            traceback.format_exc(), OEC_GCOREPORTING_CLIENT_ID, "N/A", "N/A", "ValueError"
        )
        traceback.print_exc()
        return {"status": "error", "response": "An unexpected error occurred"}
    
@router.post("/updateRedisStore")
async def dump_to_redis_all_group_list_route(request: Request):
    try:
        data = await request.json()
        uuid = data.get("uuid", "")
        user_type = data.get("user_type", "EXTERNAL")
        
        ####### redis dump ########
        res = await dump_to_redis_all_group_list(uuid, user_type)
        return res
    except Exception as e:
        traceback.print_exc()
        return {"status": "error", "message": str(e)}
    
@router.post("/submit_ticket")
async def submit_ticket_route(request: Request):
    try:
        endpoint = os.environ.get('GCO_PARSER_URL') + 'submit_ticket'
        headers = {"Content-Type": "application/json"}
        client = httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True')
        req = await request.json()
        response = await client.post(endpoint, json=req, headers=headers)
        if response.status_code != 200:
            print(f"Failed to submit ticket: {response.text}")
            return {"status": "error", "response": f"Failed to submit ticket: {response.text}"}
        return response.json()
    except Exception as e:
        traceback.print_exc()
        return {"status": "error", "response": str(e)}
    
@router.post("/isfirsttimeuser")
async def isfirsttimeuser_route(request: Request):
    try:
        endpoint = os.environ.get('GCO_PARSER_URL') + 'isfirsttimeuser'
        headers = {"Content-Type": "application/json"}
        client = httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True')
        req = await request.json()
        response = await client.post(endpoint, json=req, headers=headers)
        if response.status_code != 200:
            print(f"Failed to check first time user: {response.text}")
            return {"status": "error", "response": f"Failed to check first time user: {response.text}"}
        return response.json() 
    except Exception as e:
        traceback.print_exc()
        return {"status": "error", "response": str(e)}
 
    
async def get_redis(uuid:str):
    try:
        redis_url = os.environ.get('GCO_REDIS_URL') + 'get'
        payload = { "key": "GCO:" + uuid }
        client = httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True')
        response = await client.post(redis_url, json=payload)
        if response.status_code != 200:
            print(f"Failed to fetch data from Redis: {response.text}")
            return {"status": "error", "response": f"Failed to fetch data: {response.text}"}
        return response.json() 
    except Exception as e:
        print(f"Error fetching Redis client: {e}")
        return None

async def get_redis_report_types_group(uuid:str):
    try:
        redis_url = os.environ.get('GCO_REDIS_URL') + 'get'
        payload = { "key": "GCO:" + uuid }
        client = httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True')
        response = await client.post(redis_url, json=payload)
        if response.status_code != 200:
            print(f"Failed to fetch data from Redis: {response.text}")
            return {"status": "error", "response": f"Failed to fetch data: {response.text}"}
        return response.json() 
    except Exception as e:
        print(f"Error fetching Redis client: {e}")
        return None

async def dump_to_redis_report_types_group(uuid, response):
    try:
        redis_url = os.environ.get('GCO_REDIS_URL') + 'put'
        payload = {
            "key": "GCO:" + uuid,
            "timeUnit": "DAYS",
            "value": json.dumps(response),
            "expiration": "1"
        }
        headers = {"Content-Type": "application/json"}
        client = httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True')
        await client.post(redis_url, json=payload, headers=headers)
        print(f"Data dumped to Redis for uuid: {uuid}")
    except Exception as e:
        traceback.print_exc()
        print(f"An error occurred while dumping data to Redis: {e}")

async def get_report_details_by_group(uuid: str, group_names: list = []):
    #fetch from the GCO parser service
    try:
        initial_url = os.environ.get('GCO_PARSER_URL')
        endpoint = initial_url + 'getdatabygroup'
        headers = {"Content-Type": "application/json"}
        payload = {"group_names": group_names }
        
        client = httpx.AsyncClient(verify=os.environ.get('VERIFY_SSL') == 'True')
        response = await client.post(endpoint, json=payload, headers=headers)
        if response.status_code == 200:
            response = response.json()
            if not response:
                return {"status": "error", "response": "No data found for the provided group names"}
            
            return response.get('custom_records', [])
            # return {"status": "success", "response": response.get('custom_records', [])}
        else:
            return []
            # return {"status": "error", "response": f"Failed to fetch data: {response.text}"}
    except Exception as e:
        traceback.print_exc()
        return {"status": "error", "response": "An unexpected error occurred"}

    
async def validate_group_name(response, group_names):
    if not response or not isinstance(response, list):
        return False
    
    for group in response:
        if 'group_name' in group and group['group_name'] in group_names:
            return True
    return False

async def encrypt_filename(filename, key = os.environ.get('GCO_CLIENT_SECRET')):
    print(f"Encrypting filename: {filename}")
    # Convert key to bytes and ensure it's the right length for AES 
    key_bytes = hashlib.sha256(key.encode()).digest()
    iv = os.urandom(16)
    cipher = Cipher(algorithms.AES(key_bytes), modes.CFB(iv), backend=default_backend())
    encryptor = cipher.encryptor()
    encrypted = encryptor.update(filename.encode()) + encryptor.finalize()
    return base64.urlsafe_b64encode(iv + encrypted).decode()