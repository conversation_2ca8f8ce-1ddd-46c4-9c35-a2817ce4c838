"""
Service for calculating token usage and costs for LLM API calls.
"""

def calculate_token_usage_and_cost(response_metadata):
    """
    Calculate token usage and costs from response metadata.
    
    Args:
        response_metadata (dict): Response metadata containing token usage information
        
    Returns:
        dict: A dictionary containing token usage and cost information
    """
    model_name = response_metadata.get("model_name")
    usage = response_metadata.get("token_usage", {})
    prompt_tokens = usage.get("prompt_tokens")
    completion_tokens = usage.get("completion_tokens")
    total_tokens = usage.get("total_tokens")
    
    # GPT-4o pricing (hard-coded for simplicity)
    input_price_per_1k = 0.0025
    output_price_per_1k = 0.01
    
    # Calculate cost
    if prompt_tokens is not None and completion_tokens is not None:
        total_cost = (prompt_tokens * input_price_per_1k + completion_tokens * output_price_per_1k) / 1000
    else:
        total_cost = None
    
    return {
        "model": model_name,
        "provider": "OpenAI",
        "prompt_tokens": prompt_tokens,
        "completion_tokens": completion_tokens,
        "total_tokens": total_tokens,
        "total_cost": total_cost
    }