aiocache==0.12.3
aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.12.13
aiosignal==1.3.2
annotated-types==0.7.0
anthropic==0.58.2
anyio==4.9.0
arrow==1.3.0
attrs==25.3.0
azure-common==1.1.28
azure-core==1.35.0
azure-identity==1.19.0
azure-search-documents==11.5.3
backoff==2.2.1
boto3==1.35.98
botocore==1.35.99
certifi==2025.6.15
cffi==1.17.1
chardet==5.2.0
charset-normalizer==3.4.2
click==8.2.1
configparser==7.2.0
copilotkit==0.1.39
cryptography==45.0.4
dataclasses-json==0.6.7
dateparser==1.2.1
diskcache==5.6.3
distro==1.9.0
dnspython==2.7.0
editdistpy==0.1.6
environs==9.5.0
fastapi==0.115.11
frozenlist==1.7.0
grpcio==1.73.0
h11==0.16.0
httpcore==1.0.9
httpx==0.27.2
httpx-sse==0.4.0
idna==3.10
isodate==0.7.2
Jinja2==3.1.6
jiter==0.10.0
jmespath==1.0.1
jsonpatch==1.33
jsonpointer==3.0.0
langchain==0.3.23
langchain-anthropic==0.3.11
langchain-community==0.3.21
langchain-core==0.3.52
langchain-milvus==0.1.7
langchain-openai==0.2.3
langchain-text-splitters==0.3.8
langfuse==2.60.0
langgraph==0.2.50
langgraph-checkpoint==2.0.25
langgraph-checkpoint-postgres==2.0.21
langgraph-sdk==0.1.70
langsmith==0.3.45
LingoGPTConnector @ https://repo1.uhc.com:443/artifactory/pypi-virtual/optum-mangus-va/LingoGPTConnector/6.0/LingoGPTConnector-6.0.2-py3-none-any.whl#sha256=003c643fb42f03f9f8ef4d4877a4aa3d887970f25227eb9a137a726c70cb179c
llama_cpp_python==0.3.9
MagnusGPTConnector @ https://repo1.uhc.com:443/artifactory/pypi-virtual/optum-mangus-va/MagnusGPTConnector/4.3/magnusgptconnector-4.3.0-py3-none-any.whl#sha256=4217ce8b10978186325c4415e928183da16e6c5521fc61dc8ba77fe15cc44a89
MagnusLimraConverter @ https://repo1.uhc.com:443/artifactory/pypi-virtual/optum-mangus-va/MagnusLimraConverter/1.0/MagnusLimraConverter-0.1.1-py3-none-any.whl#sha256=8c6619a5e7697280fa7a26976e282466f0703ac6ef765c5b7605789355bc24fb
MarkupSafe==3.0.2
marshmallow==3.26.1
milvus-lite==2.4.12
moment==0.12.1
motor==3.3.2
msal==1.33.0
msal-extensions==1.3.1
multidict==6.5.0
mypy_extensions==1.1.0
numpy==2.3.0
openai==1.100.2
orjson==3.10.18
ormsgpack==1.10.0
packaging==24.2
pandas==2.3.0
partialjson==0.0.8
pdfminer.six==20250506
pdfplumber==0.11.7
pillow==11.2.1
propcache==0.3.2
protobuf==6.31.1
psycopg==3.2.9
psycopg-binary==3.2.6
psycopg-pool==3.2.6
pycparser==2.22
pydantic==2.9.1
pydantic-settings==2.9.1
pydantic_core==2.23.3
PyJWT==2.10.1
pymilvus==2.4.6
pymongo==4.6.3
pypdf==6.0.0
pypdfium2==4.30.1
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-multipart==0.0.20
pytz==2025.2
PyYAML==6.0.2
RapidFuzz==3.13.0
regex==2024.11.6
reportlab==4.2.2
requests==2.32.4
requests-toolbelt==1.0.0
s3transfer==0.10.4
setuptools==80.9.0
six==1.17.0
sniffio==1.3.1
SQLAlchemy==2.0.41
starlette==0.46.2
symspellpy==6.9.0
tenacity==9.1.2
tiktoken==0.9.0
times==0.7
toml==0.10.2
tqdm==4.67.1
types-python-dateutil==2.9.0.20250516
typing-inspect==0.9.0
typing-inspection==0.4.1
typing_extensions==4.14.0
tzdata==2025.2
tzlocal==5.3.1
ujson==5.10.0
urllib3==2.5.0
uvicorn==0.34.0
Werkzeug==3.0.3
wrapt==1.17.2
yarl==1.20.1
zstandard==0.23.0
