 
from langchain_core.prompts import ChatPromptTemplate
from services.get_prompts_service import master_prompt_document, get_prompt_by_assistant_name
# Add your system prompt here

samx_one_assistant_prompt_document = get_prompt_by_assistant_name("samx_one")

if not samx_one_assistant_prompt_document:
    raise ValueError("samx_one Assistant Prompt Not Found")

master_prompt = master_prompt_document.get("masterPrompt")
assistant_prompt = samx_one_assistant_prompt_document.get("assistantPrompt")
system_prompt = ("system", master_prompt + assistant_prompt)
samx_one_system_prompt = ChatPromptTemplate.from_messages([system_prompt, ("placeholder", "{messages}")]).partial() 
