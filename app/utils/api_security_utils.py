import logging
from fastapi import Header, HTTPException, Request
from services.ohid_user_auth_service import validate_lingo_token
from utils.helpers.constants import OEC_DEFAULT_CLIENT_ID, OEC_OPTUMAI_CLIENT_ID



async def authorize_request(
    authorization: str = Header(...),
    client_type: str = Header(...),
    request: Request = None
):
    if not authorization.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Unauthorized: Invalid Authorization header")
    
    if not request:
        raise HTTPException(status_code=401, detail="Unauthorized: Missing request object")
    
    token = authorization.split("Bearer ")[1]


    client_id = OEC_DEFAULT_CLIENT_ID
    if client_type and client_type == OEC_OPTUMAI_CLIENT_ID:
        client_id = client_type
   
    if not await validate_lingo_token(token, client_id):
        raise HTTPException(status_code=401, detail="Unauthorized: Invalid token")
