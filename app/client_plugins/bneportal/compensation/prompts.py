 
from langchain_core.prompts import ChatPromptTemplate
from graph.prompts_and_capabilities import master_prompt
from services.get_prompts_service import get_prompt_by_assistant_name

compensation_assistant_prompt_document = get_prompt_by_assistant_name("compensation")

if not compensation_assistant_prompt_document:
    raise ValueError("compensation Assistant Prompt Not Found")

assistant_prompt = compensation_assistant_prompt_document.get("assistantPrompt")
system_prompt = ("system", master_prompt + assistant_prompt)
compensation_system_prompt = ChatPromptTemplate.from_messages([system_prompt, ("placeholder", "{messages}")]).partial() 
