"""
Azure AI Search version of Surest tools for A/B testing comparison
"""

from langchain_core.tools import tool
from config.openAI import json_model
from services.azure_ai_search_service import azure_similarity_search, AZURE_INDEX_MAPPINGS
import json
from typing import Annotated
from langgraph.prebuilt.tool_node import InjectedState
from utils.tool_utils import log_tool_error
import traceback
from client_plugins.surest.surest_utils.utils import log_surest_qna_data
from utils.tool_invocation_logger import ToolInvocationLogger
from services.user_info_service import extract_user_info_from_state
from services.rag_comparison_service import rag_comparison_service
import time

surest_training_material_fallback_message = "Sorry we cannot find the answer to your question. Please contact UHC Representative."

@tool
@ToolInvocationLogger.log_tool_invocation_decorator
async def surest_training_video_qa_azure(state: Annotated[dict, InjectedState], surest_question: str):
    """
    Azure AI Search version: This tool provides access to videos related to 'surest'. It should be used whenever a user wants to watch or view training videos or any other video content related to 'surest'.
    """
    try:
        # Use Azure AI Search for similarity search
        results = await azure_similarity_search(
            index_name=AZURE_INDEX_MAPPINGS["surest_training_video"],
            query=surest_question,
            top_k=4,
            state=state
        )
        
        if not results or len(results) == 0:
            return surest_training_material_fallback_message
        
        # Extract the first video's URL and the names of the other three videos
        first_video_url = results[0]['file_url']
        follow_up_video_names = [f"Watch a video about {result['file_name']}" for result in results[1:]]
        
        if not first_video_url:
            return surest_training_material_fallback_message
        
        return {
            "client": "surest",
            "response_type": "surest_training_video_qa",
            "video_url": first_video_url,
            "follow_up_videos": follow_up_video_names
        }
        
    except Exception as e:
        await log_tool_error(state, traceback.format_exc(), "surest_training_video_qa_azure")
        raise Exception(traceback.format_exc())

@tool
@ToolInvocationLogger.log_tool_invocation_decorator
async def surest_training_material_qa_azure(state: Annotated[dict, InjectedState], surest_question: str):
    """
    Azure AI Search version: Provides answers to questions related to 'surest', including details about the surest plan, training materials and any related information.
    """
    try:
        # Use Azure AI Search for similarity search
        vector_results = await azure_similarity_search(
            index_name=AZURE_INDEX_MAPPINGS["surest_training_material"],
            query=surest_question,
            top_k=5,
            state=state
        )
        
        if not vector_results or len(vector_results) == 0:
            await log_surest_qna_data(surest_question, "", surest_training_material_fallback_message, state)
            return surest_training_material_fallback_message
        
        # Prepare context from search results
        vector_result_content = ""
        vector_results_file_url = ""
        
        for result in vector_results:
            file_name = result['file_name']
            file_url = result['file_url']
            page_content = result['page_content']
            score = result['score']
            
            vector_result_content += f"File: {file_name}\nContent: {page_content}\nRelevance Score: {score:.4f}\n\n"
            
            if not vector_results_file_url and file_url:
                vector_results_file_url = file_url
        
        # Create prompt for GPT-4o
        prompt = f"""
        You are a helpful assistant that answers questions about Surest health plans based on the provided context.
        
        Context from training materials:
        {vector_result_content}
        
        Question: {surest_question}
        
        Please provide a comprehensive answer based on the context. If the context doesn't contain enough information to answer the question, say so clearly.
        
        Format your response as JSON with the following structure:
        {{
            "answer": "Your detailed answer here",
            "follow_up_questions": ["Question 1", "Question 2", "Question 3"]
        }}
        """
        
        response = await json_model.ainvoke(prompt)
        json_response = json.loads(response.content)
        answer = json_response.get("answer", "") + "[view-source-hyper-link]" + vector_results_file_url
        follow_up_questions = json_response.get("follow_up_questions", [])
        
        if surest_training_material_fallback_message.lower() in answer.lower():
            await log_surest_qna_data(surest_question, vector_result_content, surest_training_material_fallback_message, state)
            return {
                "answer": surest_training_material_fallback_message,
                "follow_up_questions": []
            }
        
        # Log the Q&A data
        await log_surest_qna_data(surest_question, vector_result_content, answer, state)
        
        return {
            "answer": answer,
            "follow_up_questions": follow_up_questions
        }
        
    except Exception as e:
        await log_tool_error(state, traceback.format_exc(), "surest_training_material_qa_azure")
        raise Exception(traceback.format_exc())

@tool
@ToolInvocationLogger.log_tool_invocation_decorator
async def surest_training_material_compare_features_azure(state: Annotated[dict, InjectedState], surest_question: str) -> dict:
    """
    Azure AI Search version: This tool is responsible for comparing or differentiating features or anything for surest and provides a comparison result in a structured tabular format.
    This tool should be used when the question contains comparison-related terms like "pros and cons", "versus", "vs", "compare", "differences", "similarities", etc.
    """
    try:
        # Use Azure AI Search for similarity search
        vector_results = await azure_similarity_search(
            index_name=AZURE_INDEX_MAPPINGS["surest_training_material"],
            query=surest_question,
            top_k=5,
            state=state
        )
        
        if not vector_results or len(vector_results) == 0:
            await log_surest_qna_data(surest_question, "", surest_training_material_fallback_message, state)
            return surest_training_material_fallback_message
        
        # Prepare context from search results
        vector_result_content = ""
        for result in vector_results:
            file_name = result['file_name']
            page_content = result['page_content']
            score = result['score']
            vector_result_content += f"File: {file_name}\nContent: {page_content}\nRelevance Score: {score:.4f}\n\n"
        
        # Create comparison-focused prompt
        prompt = f"""
        You are a helpful assistant that creates structured comparisons about Surest health plans based on the provided context.
        
        Context from training materials:
        {vector_result_content}
        
        Question: {surest_question}
        
        Please create a structured comparison table based on the context. Focus on highlighting differences, similarities, pros and cons, or feature comparisons as requested.
        
        Format your response as JSON with the following structure:
        {{
            "comparison_table": [
                {{
                    "category": "Category Name",
                    "item1": "First item details",
                    "item2": "Second item details",
                    "notes": "Additional notes"
                }}
            ],
            "summary": "Brief summary of the comparison",
            "follow_up_questions": ["Question 1", "Question 2"]
        }}
        """
        
        response = await json_model.ainvoke(prompt)
        json_response = json.loads(response.content)
        
        comparison_table = json_response.get("comparison_table", [])
        summary = json_response.get("summary", "")
        follow_up_questions = json_response.get("follow_up_questions", [])
        
        # Log the Q&A data
        await log_surest_qna_data(surest_question, vector_result_content, summary, state)
        
        return {
            "answer": {
                "client": "surest",
                "response_type": "surest_training_material_compare_features",
                "comparison_table": comparison_table,
                "summary": summary
            },
            "follow_up_questions": follow_up_questions
        }
        
    except Exception as e:
        await log_tool_error(state, traceback.format_exc(), "surest_training_material_compare_features_azure")
        raise Exception(traceback.format_exc())

@tool
@ToolInvocationLogger.log_tool_invocation_decorator
async def surest_training_material_benefits_or_features_azure(state: Annotated[dict, InjectedState], surest_question: str) -> dict:
    """
    Azure AI Search version: This tool is responsible for providing benefits or list of benefits for surest based on the question.
    This tool should be used when questions are about benefits, features, advantages, "why choose", or value of surest based on the question.
    """
    try:
        # Use Azure AI Search for similarity search
        vector_results = await azure_similarity_search(
            index_name=AZURE_INDEX_MAPPINGS["surest_training_material"],
            query=surest_question,
            top_k=5,
            state=state
        )
        
        if not vector_results or len(vector_results) == 0:
            await log_surest_qna_data(surest_question, "", surest_training_material_fallback_message, state)
            return surest_training_material_fallback_message
        
        # Prepare context from search results
        vector_result_content = ""
        for result in vector_results:
            file_name = result['file_name']
            page_content = result['page_content']
            score = result['score']
            vector_result_content += f"File: {file_name}\nContent: {page_content}\nRelevance Score: {score:.4f}\n\n"
        
        # Create benefits-focused prompt
        prompt = f"""
        You are a helpful assistant that extracts and lists benefits, features, and advantages of Surest health plans based on the provided context.
        
        Context from training materials:
        {vector_result_content}
        
        Question: {surest_question}
        
        Please extract and organize the benefits, features, or advantages mentioned in the context. Present them as a structured list.
        
        Format your response as JSON with the following structure:
        {{
            "benefits_list": [
                {{
                    "benefit": "Benefit title",
                    "description": "Detailed description",
                    "category": "Category (e.g., Cost, Coverage, Convenience)"
                }}
            ],
            "summary": "Brief summary of key benefits",
            "follow_up_questions": ["Question 1", "Question 2"]
        }}
        """
        
        response = await json_model.ainvoke(prompt)
        json_response = json.loads(response.content)
        
        benefits_list = json_response.get("benefits_list", [])
        summary = json_response.get("summary", "No specific benefits found in the documentation.")
        follow_up_questions = json_response.get("follow_up_questions", [])
        
        # Log the Q&A data
        await log_surest_qna_data(surest_question, vector_result_content, summary, state)
        
        return {
            "answer": {
                "client": "surest",
                "response_type": "surest_training_material_benefits_or_features",
                "benefits_list": benefits_list,
                "summary": summary
            },
            "follow_up_questions": follow_up_questions
        }
        
    except Exception as e:
        await log_tool_error(state, traceback.format_exc(), "surest_training_material_benefits_or_features_azure")
        raise Exception(traceback.format_exc())

@tool
@ToolInvocationLogger.log_tool_invocation_decorator
async def surest_training_material_price_or_percentage_qa_azure(state: Annotated[dict, InjectedState], surest_question: str) -> dict:
    """
    Azure AI Search version: This tool is responsible for answering the questions where user is asking for price or quotation or percentage related information for surest.
    """
    try:
        # Use Azure AI Search for similarity search
        vector_results = await azure_similarity_search(
            index_name=AZURE_INDEX_MAPPINGS["surest_training_material"],
            query=surest_question,
            top_k=5,
            state=state
        )
        
        if not vector_results or len(vector_results) == 0:
            await log_surest_qna_data(surest_question, "", surest_training_material_fallback_message, state)
            return surest_training_material_fallback_message
        
        # Prepare context from search results
        vector_result_content = ""
        for result in vector_results:
            file_name = result['file_name']
            page_content = result['page_content']
            score = result['score']
            vector_result_content += f"File: {file_name}\nContent: {page_content}\nRelevance Score: {score:.4f}\n\n"
        
        # Create price/percentage-focused prompt
        prompt = f"""
        You are a helpful assistant that answers questions about pricing, costs, percentages, and financial information related to Surest health plans based on the provided context.
        
        Context from training materials:
        {vector_result_content}
        
        Question: {surest_question}
        
        Please provide detailed information about pricing, costs, percentages, or financial aspects mentioned in the context. Be specific with numbers and calculations where available.
        
        Format your response as JSON with the following structure:
        {{
            "answer": "Your detailed answer with specific pricing/percentage information",
            "key_figures": [
                {{
                    "item": "What the figure represents",
                    "value": "The actual number/percentage",
                    "context": "Additional context about this figure"
                }}
            ],
            "follow_up_questions": ["Question 1", "Question 2"]
        }}
        """
        
        response = await json_model.ainvoke(prompt)
        json_response = json.loads(response.content)
        
        answer = json_response.get("answer", "")
        key_figures = json_response.get("key_figures", [])
        follow_up_questions = json_response.get("follow_up_questions", [])
        
        if surest_training_material_fallback_message.lower() in answer.lower():
            await log_surest_qna_data(surest_question, vector_result_content, surest_training_material_fallback_message, state)
            return {
                "answer": surest_training_material_fallback_message,
                "follow_up_questions": []
            }
        
        # Log the Q&A data
        await log_surest_qna_data(surest_question, vector_result_content, answer, state)
        
        return {
            "answer": {
                "client": "surest",
                "response_type": "surest_training_material_price_or_percentage_qa",
                "answer": answer,
                "key_figures": key_figures
            },
            "follow_up_questions": follow_up_questions
        }
        
    except Exception as e:
        await log_tool_error(state, traceback.format_exc(), "surest_training_material_price_or_percentage_qa_azure")
        raise Exception(traceback.format_exc())


@tool
@ToolInvocationLogger.log_tool_invocation_decorator
async def surest_rag_comparison_tool(state: Annotated[dict, InjectedState], surest_question: str):
    """
    Compare PostgreSQL vs Azure AI Search for Surest training material queries.
    This tool runs the same query on both systems and shows performance and quality comparison.
    Use this when you want to see how both search systems perform for the same question.
    """
    try:
        # Run the comparison
        comparison_result = await rag_comparison_service.compare_search_providers(
            query=surest_question,
            top_k=5,
            include_quality_analysis=True
        )

        # Format the results for display
        pg_time = comparison_result.postgresql_metrics.total_time_ms
        azure_time = comparison_result.azure_metrics.total_time_ms
        pg_count = comparison_result.postgresql_metrics.results_count
        azure_count = comparison_result.azure_metrics.results_count

        # Determine winner
        winner = "Azure AI Search" if azure_time < pg_time else "PostgreSQL"
        time_diff = abs(pg_time - azure_time)

        # Get quality metrics
        overlap_score = comparison_result.quality_metrics.get("result_overlap", {}).get("jaccard_similarity", 0)

        # Build the comparison message
        comparison_message = f"""🔍 RAG COMPARISON RESULTS

Query: "{surest_question}"

📊 PERFORMANCE:
   PostgreSQL: {pg_time:.1f}ms ({pg_count} results)
   Azure AI Search: {azure_time:.1f}ms ({azure_count} results)
   Winner: {winner} ({time_diff:.1f}ms faster)

📝 POSTGRESQL TOP RESULTS:"""

        # Add PostgreSQL results
        for i, result in enumerate(comparison_result.postgresql_results[:3], 1):
            content_preview = result.page_content[:100] + "..." if len(result.page_content) > 100 else result.page_content
            comparison_message += f"""
   {i}. {result.file_name} (score: {result.score:.3f})
      "{content_preview}\""""

        comparison_message += "\n\n📝 AZURE AI SEARCH TOP RESULTS:"

        # Add Azure results
        for i, result in enumerate(comparison_result.azure_results[:3], 1):
            content_preview = result.page_content[:100] + "..." if len(result.page_content) > 100 else result.page_content
            comparison_message += f"""
   {i}. {result.file_name} (score: {result.score:.3f})
      "{content_preview}\""""

        comparison_message += f"\n\n🎯 QUALITY: {overlap_score:.0%} result overlap"

        # Add error information if any
        if comparison_result.postgresql_metrics.error:
            comparison_message += f"\n\n⚠️ PostgreSQL Error: {comparison_result.postgresql_metrics.error}"

        if comparison_result.azure_metrics.error:
            comparison_message += f"\n\n⚠️ Azure AI Search Error: {comparison_result.azure_metrics.error}"

        return comparison_message

    except Exception as e:
        await log_tool_error(state, traceback.format_exc(), "surest_rag_comparison_tool")
        return f"❌ Comparison failed: {str(e)}\n\nPlease check that both PostgreSQL and Azure AI Search are properly configured."
