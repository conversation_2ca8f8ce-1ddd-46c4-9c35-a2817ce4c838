from services.get_prompts_service import master_prompt_document, get_prompt_by_assistant_name
from langchain_core.prompts import ChatPromptTemplate

# Add your system prompt here
portal_form_enrollment_prompt_document = get_prompt_by_assistant_name("portal_form_enrollment")

if not portal_form_enrollment_prompt_document:
    raise ValueError("BNE Portal Form Enrollment Agent: Prompt Not Found")

master_prompt = master_prompt_document.get("masterPrompt")
assistant_prompt = portal_form_enrollment_prompt_document.get("assistantPrompt")
portal_form_enrollment_system_prompt = ChatPromptTemplate.from_messages(
    [
        ("system", f"{assistant_prompt}"),
        ("placeholder", '{messages}'),
    ]
).partial()
