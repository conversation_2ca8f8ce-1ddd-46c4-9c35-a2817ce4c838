from pydantic import BaseModel

class ToCommCentreAssistant(BaseModel):
    """Transfers work to a specialized assistant to handle every query related to "Pend Notes" and "Quote ID", the type of queries for these two intents can be regarding resolution/retrieval/creation of these items."""

class ToSbcRetrievalAssistant(BaseModel):
    """Transfers work to a specialized assistant to handle every query related to "SBC" or "SBC Retrieval" or "get SBC for a specific plan etc." """


bnemobile_router_tools = [ToCommCentreAssistant.__name__, ToSbcRetrievalAssistant.__name__]