from client_plugins.gco_reporting.gco_reporting.main import gco_reporting_tool_node
from client_plugins.gco_reporting.gco_reporting.main import gco_reporting_runnable
from client_plugins.gco_reporting.gco_reporting.router import route_gco_reporting_assistant

from graph.nodes import Assistant
from langgraph.pregel import RetryPolicy
from graph.nodes import pop_dialog_state, escalation_fallback, invalid_tool

from langgraph.graph import START
from graph.state import State, get_postgres_checkpointer
from utils.subgraph_builder import BaseSubgraphBuilder
from utils.subgraph_registry import SubgraphRegistry

gco_reporting_compiled_graph = None

async def init_gco_reporting_graph():
    """
    Initialize the gco reporting graph using the BaseSubgraphBuilder pattern
    and register it with SubgraphRegistry.
    """
    global gco_reporting_compiled_graph
    if gco_reporting_compiled_graph is None:
        # Create a new builder with fluent interface
        builder = BaseSubgraphBuilder(State)
        
        # Add nodes
        builder.add_node("gco_reporting_assistant", Assistant(gco_reporting_runnable), retry=RetryPolicy(max_attempts=2))
        builder.add_node("call_gco_reporting_tool", gco_reporting_tool_node)
        builder.add_node("leave_skill", pop_dialog_state)
        builder.add_node("escalation_fallback", escalation_fallback)
        builder.add_node("tool_disabled_handler", invalid_tool)  
        
        # Add edges
        builder.add_edge("leave_skill", "gco_reporting_assistant")
        builder.add_edge("escalation_fallback", "gco_reporting_assistant")
        builder.add_edge(START, "gco_reporting_assistant")
        
        # Add conditional edges
        builder.add_conditional_edges("gco_reporting_assistant", route_gco_reporting_assistant)
        
        # Compile with async checkpointer
        gco_reporting_compiled_graph = builder.compile(checkpointer=await get_postgres_checkpointer())
        
        # Register with SubgraphRegistry for future access
        SubgraphRegistry.register("gco_reporting", gco_reporting_compiled_graph)