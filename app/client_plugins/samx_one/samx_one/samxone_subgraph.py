from client_plugins.samx_one.samx_one.main import samx_one_tool_node
from client_plugins.samx_one.samx_one.main import samx_one_runnable
from client_plugins.samx_one.samx_one.router import route_samx_one_assistant
from graph.nodes import Assistant
from langgraph.pregel import RetryPolicy
from graph.nodes import pop_dialog_state, escalation_fallback, invalid_tool

from langgraph.graph import START
from graph.state import State, get_postgres_checkpointer
from utils.subgraph_builder import BaseSubgraphBuilder
from utils.subgraph_registry import SubgraphRegistry

samx_one_compiled_graph = None

async def init_samx_one_graph():
    """
    Initialize the samx_one graph using the BaseSubgraphBuilder pattern
    and register it with SubgraphRegistry.
    """
    global samx_one_compiled_graph
    if samx_one_compiled_graph is None:
        # Create a new builder with fluent interface
        builder = BaseSubgraphBuilder(State)
        
        # Add nodes
        builder.add_node("samx_one_assistant", Assistant(samx_one_runnable), retry=RetryPolicy(max_attempts=2))
        builder.add_node("call_samxone_tool", samx_one_tool_node)
        builder.add_node("leave_skill", pop_dialog_state)
        builder.add_node("escalation_fallback", escalation_fallback)
        builder.add_node("tool_disabled_handler", invalid_tool)  
        
        # Add edges
        builder.add_edge("leave_skill", "samx_one_assistant")
        builder.add_edge("escalation_fallback", "samx_one_assistant")
        builder.add_edge(START, "samx_one_assistant")
        
        # Add conditional edges
        builder.add_conditional_edges("samx_one_assistant", route_samx_one_assistant)
        
        # Compile with async checkpointer
        samx_one_compiled_graph = builder.compile(checkpointer=await get_postgres_checkpointer())
        
        # Register with SubgraphRegistry for future access
        SubgraphRegistry.register("samx_one", samx_one_compiled_graph)