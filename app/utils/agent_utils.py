from langchain_core.messages import ToolMessage, HumanMessage, SystemMessage, AIMessage, RemoveMessage, filter_messages
from config.openAI import model
import os
from typing import Callable, Optional
from tools.primary_assistant_tools.router_tools import router_tools
import json

MESSAGES_TO_DELETE_COUNT = int(os.getenv("MESSAGES_TO_DELETE_COUNT"))
SUMMARY_TRIGGER_THRESHOLD = int(os.getenv("SUMMARY_TRIGGER_THRESHOLD"))

async def last_message_has_tool_output(stateGraph, config, tool_name = None):
    "Returns True if the last message in the state has a tool output else False"
    state = await stateGraph.aget_state(config)
    messages = state.values.get("messages", [])
    
    if messages and hasattr(messages[-1], 'tool_call_id') and messages[-1].name not in router_tools: 
        return not tool_name or messages[-1].name == tool_name
    return False

def get_message_ids(messages):
    ids = []
    for index, message in enumerate(messages):
        if len(ids) >= MESSAGES_TO_DELETE_COUNT:
            break

        if isinstance(message, AIMessage):  
            finish_reason = message.response_metadata.get('finish_reason')  
            if finish_reason == 'tool_calls':  
                num_tool_calls = len(message.tool_calls) 
                if index + num_tool_calls < len(messages):  
                    all_tool_messages_present = all(isinstance(messages[index + i + 1], ToolMessage) for i in range(num_tool_calls))  
                    if all_tool_messages_present:  
                        ids.append(message.id)  
                        for i in range(num_tool_calls):  
                            ids.append(messages[index + i + 1].id)  
                        continue
            
            ids.append(message.id)

        elif isinstance(message, HumanMessage) or isinstance(message, SystemMessage):
            ids.append(message.id)
    return ids

async def summarize_messages(messages, ids_to_delete, previous_summary):
    if previous_summary:
        summary_message = (
            f"This is summary of the conversation to date: {previous_summary}\n\n"
            "Extend the summary by taking into account the new messages above, give more priority to latest messages in new summary:"
        )
    else:
        summary_message = "Create a summary of the conversation above:"

    filtered_messages = list(filter_messages(messages, exclude_names=["images"], include_ids=ids_to_delete)) + [HumanMessage(content=summary_message)]
    summary = await model.ainvoke(filtered_messages)
    return summary.content

async def clear_file_content_from_tool_response(messages, graph, config):
    if messages and (isinstance(messages[-1], ToolMessage)):
        content = messages[-1].content
        try:
            json_content = content if isinstance(content, dict) else json.loads(content)
            if isinstance(json_content, dict) and "temp_data" in json_content:
                json_content.pop("temp_data", None)
                updated_tool_message = ToolMessage(
                    content=json_content,
                    id=messages[-1].id,
                    tool_call_id=messages[-1].tool_call_id,
                    name=messages[-1].name,
                    additional_kwargs=messages[-1].additional_kwargs
                )
                await graph.aupdate_state(config, {"messages": updated_tool_message})
        except:
            pass

async def clear_and_summarize_messages(primary_assistant_graph, config):
    state = await primary_assistant_graph.aget_state(config)
    messages = state.values.get("messages", [])
    if not messages:
        return
    
    await clear_file_content_from_tool_response(messages, primary_assistant_graph, config)
    if len(messages) >= SUMMARY_TRIGGER_THRESHOLD:
        ids_to_delete = get_message_ids(messages)
        if ids_to_delete:
            print("Clearing and summarizing messages from state.....")
            previous_summary = state.values.get("summary", None)
            summary = await summarize_messages(messages, ids_to_delete, previous_summary)
            messages_to_delete=[RemoveMessage(id=id) for id in ids_to_delete]
            await primary_assistant_graph.aupdate_state(config, {"summary": summary, "messages": messages_to_delete})

async def clear_pdf_data(primary_assistant_graph, config):
    if await last_message_has_tool_output(primary_assistant_graph, config):
        await primary_assistant_graph.aupdate_state(config, {"pdf_data": None})

def create_entry_node(assistant_name: str, new_dialog_state: str) -> Callable:
    async def entry_node(state) -> dict:
        tool_call_id = state["messages"][-1].tool_calls[0]["id"]
        return {
            "messages": [
                ToolMessage(
                    content=f"The assistant is now the {assistant_name}."
                    " If the user changes their mind or needs help for other tasks, call the CompleteOrEscalate function to let the primary host assistant take control."
                    "If user is asking for multiple intents where any of the intent is not in your capability then strictly 'CompleteOrEscalate' the dialog to the host assistant without processing any intent."
                    " Do not mention who you are - just act as the proxy for the assistant.",
                    tool_call_id=tool_call_id,
                )
            ],
            "dialog_state": new_dialog_state,
        }

    return entry_node

def create_permission_node() -> Callable:
    async def entry_node(state) -> dict:
        tool_call_id = state["messages"][-1].tool_calls[0]["id"]
        return {
            "messages": [
                ToolMessage(
                    content=f"It appears that the request created is outside the domain of the platform or that you may require further individual permissions.\n\n"
                    "**This restriction is based on the client-based access provided to the user as part of platform operability.**\n\n"
                    "Thank you for using our product. Please contact the respective support team.",
                    tool_call_id=tool_call_id,
                )
            ],
            "dialog_state": "primary_assistant",
        }

    return entry_node

def update_dialog_stack(left: list[str], right: Optional[str]) -> list[str]:
    """Push or pop the state."""
    if right is None:
        return left
    if right == "pop":
        return left[:-1]
    return left + [right]


