class SubgraphRegistry:
    """
    Enhanced registry for managing subgraph compilation functions with async support.
    This provides a centralized way to access and initialize all subgraphs in the system.
    """
    
    # Dictionary to store registered subgraphs
    _subgraphs = {}
    
    @classmethod
    def register(cls, name, compiled_graph):
        """
        Register a compiled subgraph with the registry.
        
        Args:
            name: Name of the subgraph
            compiled_graph: The compiled subgraph
        """
        cls._subgraphs[name] = compiled_graph
        print(f"Initialized and Registered subgraph: {name}")
    
    @classmethod
    def get(cls, name):
        """
        Get a registered subgraph by name.
        
        Args:
            name: Name of the subgraph to retrieve
            
        Returns:
            The compiled subgraph, or None if not found
        """
        return cls._subgraphs.get(name)
        
    @classmethod
    def get_all(cls):
        """
        Get all registered subgraphs.
        
        Returns:
            Dictionary of all registered subgraphs
        """
        return cls._subgraphs