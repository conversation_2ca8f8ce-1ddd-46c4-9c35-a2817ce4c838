from graph.state import State
from langgraph.prebuilt import tools_condition
from langgraph.graph import END
from tools.common_tools import CompleteOrEscalate
from services.get_client_ids_and_assistants_service import validate_tool
from services.event_logger_service import logger

# Router: This is to be used for merging the capability, it is recommended not to modify it

async def route_portal_form_enrollment_assistant(
    state: State,
):
    route = tools_condition(state)
    if route == END:
        return END
    if route == "tools":
        tool_calls = state["messages"][-1].tool_calls
        if len(tool_calls) > 1 and CompleteOrEscalate.__name__ in [call["name"] for call in tool_calls]:
            return "escalation_fallback"
        elif tool_calls[0]["name"] == CompleteOrEscalate.__name__:
            return "leave_skill"

        if not await validate_tool(state.get("user_info")["client_id"], "ToPortalFormEnrollmentAssistant", tool_calls[0]["name"]):
            uuid = state.get("user_info")["uuid"]
            user_name = state.get("user_info").get("user_name", None)
            session_id = state.get("user_info")["session_id"]
            request_id = state.get("user_info").get("request_id", None)
            client_id = state.get("user_info")["client_id"]
            await logger.error(uuid, user_name, session_id, request_id, client_id, "router", "route_portal_form_enrollment_tool", "State", tool_calls[0]["name"], None, 400, "Routed tool is disabled.")
            return "tool_disabled_handler"
        else:
            return "call_portal_form_enrollment_tool"
            
            
    return "portal_form_enrollment_assistant"
