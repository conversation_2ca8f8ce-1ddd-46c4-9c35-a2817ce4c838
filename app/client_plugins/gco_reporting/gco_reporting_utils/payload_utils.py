import json

class PayloadUtils:
    """
    Utility class for standardizing payload structures.
    Ensures that only expected keys with proper formatting are included in payloads.
    """
    
    @staticmethod
    def get_standardized_tracking_payload(user_action_payload: dict):
        """
        Creates a standardized payload for user tracking information using a single argument.
        
        Args:
            user_action_payload (dict): Dictionary containing all necessary user, state, action, incident, and report data
            
        Returns:
            dict: Standardized payload with expected keys
        """
        user_action_payload = user_action_payload or {}
        
        # Extract feedback information if available
        feedback_data = user_action_payload.get("feedback", {})
        if isinstance(feedback_data, str):
            try:
                feedback_data = json.loads(feedback_data)
            except json.JSONDecodeError:
                feedback_data = {}
        
        # Build the standardized payload
        standardized_payload = {
            "user_id": user_action_payload.get("user_id") or user_action_payload.get("uuid") or "",
            "user_type": user_action_payload.get("user_type", ""),
            "user_access_role": user_action_payload.get("user_access_role", ""),
            "user_ext_type": user_action_payload.get("user_ext_type", ""),
            "user_name": user_action_payload.get("user_name", ""),
            "user_email": user_action_payload.get("user_email", ""),
            "action_type": user_action_payload.get("action_type", ""),
            "incident_number": user_action_payload.get("incident_number", ""),
            "feedback": {
                "feedback_desc": feedback_data.get("feedback_desc", ""),
                "feedback_resp": feedback_data.get("feedback_resp", "")
            },
            "group_id": user_action_payload.get("group_id", ""),
            "group_name": user_action_payload.get("group_name", ""),
            "file_name": user_action_payload.get("file_name", ""),
            "report_type": user_action_payload.get("report_type", ""),
            "report_desc": user_action_payload.get("report_desc", ""),
            "published_at": user_action_payload.get("published_at", ""),
            "file_path": user_action_payload.get("file_path", ""),
            "funding_type": user_action_payload.get("funding_type", ""),
            "revenue_arrangement": user_action_payload.get("revenue_arrangement", ""),
            "subscriber_count": user_action_payload.get("subscriber_count", ""),
            "platform_id": user_action_payload.get("platform_id", ""),
            "session_id": user_action_payload.get("session_id", ""),
        }
        
        return standardized_payload
